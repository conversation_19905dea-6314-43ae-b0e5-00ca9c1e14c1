2025-06-13 23:00:15.822 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/extensions/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/extensions/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:27.448 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:27.448 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:27.545 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.csdevkit-1.16.6-darwin-arm64/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.csdevkit-1.16.6-darwin-arm64/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:27.546 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.vscode-dotnet-runtime-2.2.8/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.vscode-dotnet-runtime-2.2.8/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:27.547 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/ms-python.debugpy-2025.8.0-darwin-arm64/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/ms-python.debugpy-2025.8.0-darwin-arm64/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:27.548 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/ms-python.python-2025.6.1-darwin-arm64/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/ms-python.python-2025.6.1-darwin-arm64/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:27.549 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.csharp-2.63.32-darwin-arm64/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.csharp-2.63.32-darwin-arm64/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:27.549 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/ms-python.vscode-pylance-2024.8.1/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/ms-python.vscode-pylance-2024.8.1/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:27.550 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/visualstudiotoolsforunity.vstuc-1.1.0/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/visualstudiotoolsforunity.vstuc-1.1.0/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 23:00:32.157 [info] Extensions added from another source augment.vscode-augment vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 23:00:32.158 [info] Extensions added from another source ms-dotnettools.csdevkit vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 23:00:32.158 [info] Extensions added from another source ms-dotnettools.csharp vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 23:00:32.158 [info] Extensions added from another source ms-dotnettools.vscode-dotnet-runtime vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 23:00:32.158 [info] Extensions added from another source ms-python.debugpy vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 23:00:32.158 [info] Extensions added from another source ms-python.python vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 23:00:32.158 [info] Extensions added from another source ms-python.vscode-pylance vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 23:00:32.158 [info] Extensions added from another source visualstudiotoolsforunity.vstuc vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 23:00:32.220 [info] Uninstalling extension from the profile: ms-python.vscode-pylance@2024.8.1 vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 23:00:32.241 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/ms-python.vscode-pylance-2024.8.1/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/ms-python.vscode-pylance-2024.8.1/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async Promise.all (index 0)
    at async U2.C (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:56617)
2025-06-13 23:00:32.243 [info] Extension not found at the location file:///Users/<USER>/.windsurf/extensions/ms-python.vscode-pylance-2024.8.1
2025-06-13 23:00:34.214 [info] Successfully uninstalled extension from the profile ms-python.vscode-pylance@2024.8.1 vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
