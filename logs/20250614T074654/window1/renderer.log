2025-06-14 07:46:55.140 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.140 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.140 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.vscode-pull-request-github' wants API proposal 'fileComments' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.140 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatVariableResolver' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.140 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.140 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.140 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.141 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.141 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'redhat.java' wants API proposal 'documentPaste' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.141 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-14 07:46:55.259 [warning] IWorkbenchContributionsRegistry#getContribution('windsurf.cascadePanel'): contribution instantiated before LifecyclePhase.Restored!
2025-06-14 07:46:55.520 [error] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-06-14 07:46:55.813 [info] Started local extension host with pid 97939.
2025-06-14 07:47:02.638 [info] [perf] Render performance baseline is 20ms
2025-06-14 07:47:11.481 [error] [Extension Host] Error during login process: ConnectError: [permission_denied] api server wire error: free user account exceeded, please use an existing account or upgrade to a paid plan
	at c (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856441)
	at l (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856837)
	at next (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3863488)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3462828
	at async Object.unary (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3861824)
	at async Object.getUserStatus (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3849687)
	at async v (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2282983)
	at async t.initializeAuthSession (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2282205)
2025-06-14 07:47:19.195 [error] [Extension Host] Failed to get remote url: Error: Failed to get remote url, no remote found
	at lU.handleGetRemoteUrlRequest (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1928:492)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1596:3962
	at async e.runTimed (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2210:84893)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1596:3920
2025-06-14 09:37:19.318 [error] [Extension Host] FetchError: Unleash Repository error: network timeout at: https://unleash.codeium.com/api/client/features
	at Timeout.<anonymous> (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2130260)
	at listOnTimeout (node:internal/timers:581:17)
	at process.processTimers (node:internal/timers:519:7)
