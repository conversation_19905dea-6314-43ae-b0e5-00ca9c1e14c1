2025-06-14 07:46:56.215 [info] [main] Log level: Info
2025-06-14 07:46:56.218 [info] [main] Validating found git in: "/usr/bin/git"
2025-06-14 07:46:56.237 [info] [main] Using git "2.39.3 (Apple Git-146)" from "/usr/bin/git"
2025-06-14 07:46:56.237 [info] [Model][doInitialScan] Initial repository scan started
2025-06-14 07:46:56.438 [info] > git rev-parse --show-toplevel [198ms]
2025-06-14 07:46:56.438 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.449 [info] > git rev-parse --show-toplevel [7ms]
2025-06-14 07:46:56.449 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.455 [info] > git rev-parse --show-toplevel [6ms]
2025-06-14 07:46:56.455 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.461 [info] > git rev-parse --show-toplevel [5ms]
2025-06-14 07:46:56.461 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.468 [info] > git rev-parse --show-toplevel [6ms]
2025-06-14 07:46:56.468 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.475 [info] > git rev-parse --show-toplevel [6ms]
2025-06-14 07:46:56.475 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.482 [info] > git rev-parse --show-toplevel [7ms]
2025-06-14 07:46:56.482 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.488 [info] > git rev-parse --show-toplevel [6ms]
2025-06-14 07:46:56.488 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.496 [info] > git rev-parse --show-toplevel [7ms]
2025-06-14 07:46:56.496 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.509 [info] > git rev-parse --show-toplevel [13ms]
2025-06-14 07:46:56.509 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.522 [info] > git rev-parse --show-toplevel [13ms]
2025-06-14 07:46:56.522 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.534 [info] > git rev-parse --show-toplevel [10ms]
2025-06-14 07:46:56.534 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.541 [info] > git rev-parse --show-toplevel [6ms]
2025-06-14 07:46:56.541 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.549 [info] > git rev-parse --show-toplevel [6ms]
2025-06-14 07:46:56.549 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.557 [info] > git rev-parse --show-toplevel [8ms]
2025-06-14 07:46:56.557 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.564 [info] > git rev-parse --show-toplevel [6ms]
2025-06-14 07:46:56.564 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.572 [info] > git rev-parse --show-toplevel [7ms]
2025-06-14 07:46:56.572 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.580 [info] > git rev-parse --show-toplevel [7ms]
2025-06-14 07:46:56.580 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.600 [info] > git rev-parse --show-toplevel [19ms]
2025-06-14 07:46:56.600 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-14 07:46:56.601 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-14 07:46:57.838 [info] > git rev-parse --show-toplevel [30ms]
2025-06-14 07:46:57.838 [info] fatal: not a git repository (or any of the parent directories): .git
