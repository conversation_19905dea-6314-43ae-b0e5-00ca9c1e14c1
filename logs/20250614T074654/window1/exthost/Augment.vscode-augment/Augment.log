2025-06-14 07:47:04.192 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-14 07:47:04.193 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"用中文回答"},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-14 07:47:04.193 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-14 07:47:04.193 [info] 'AugmentExtension' Retrieving model config
2025-06-14 07:47:09.974 [info] 'AugmentExtension' Retrieved model config
2025-06-14 07:47:09.974 [info] 'AugmentExtension' Returning model config
2025-06-14 07:47:09.986 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-14 07:47:09.986 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /Users/<USER>/Library/Application Support/Windsurf (explicit) at 6/13/2025, 11:01:00 PM
2025-06-14 07:47:09.986 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-14 07:47:09.986 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-14 07:47:09.986 [info] 'SyncingPermissionTracker' Permission to sync folder /Users/<USER>/Library/Application Support/Windsurf granted at 6/13/2025, 11:01:00 PM; type = explicit
2025-06-14 07:47:09.986 [info] 'WorkspaceManager' Adding workspace folder Windsurf; folderRoot = /Users/<USER>/Library/Application Support/Windsurf; syncingPermission = granted
2025-06-14 07:47:09.986 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /Users/<USER>/Library/Application Support/Windsurf (explicit) at 6/13/2025, 11:01:00 PM
2025-06-14 07:47:10.042 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-14 07:47:10.043 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-14 07:47:10.043 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-14 07:47:10.054 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-14 07:47:10.054 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-14 07:47:10.086 [info] 'WorkspaceManager[Windsurf]' Start tracking
2025-06-14 07:47:10.111 [info] 'PathMap' Opened source folder /Users/<USER>/Library/Application Support/Windsurf with id 100
2025-06-14 07:47:10.112 [info] 'OpenFileManager' Opened source folder 100
2025-06-14 07:47:10.151 [info] 'MtimeCache[Windsurf]' reading blob name cache from /Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json
2025-06-14 07:47:10.152 [info] 'MtimeCache[Windsurf]' read 111 entries from /Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json
2025-06-14 07:47:16.147 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-14 07:47:16.147 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-14 07:47:16.147 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-14 07:47:16.147 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-14 07:47:16.147 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-14 07:47:16.147 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-14 07:47:17.742 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-14 07:47:19.016 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-14 07:47:19.016 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-14 07:47:19.017 [info] 'TaskManager' Setting current root task UUID to 387b9221-ef76-4360-97ea-9642fccc0a22
2025-06-14 07:47:19.195 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-14 07:47:21.924 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2895.404792,"timestamp":"2025-06-13T23:47:21.913Z"}]
2025-06-14 07:47:22.026 [info] 'StallDetector' Recent work: [{"name":"get-remote-agent-overviews-request","durationMs":2909.705833,"timestamp":"2025-06-13T23:47:22.015Z"}]
2025-06-14 07:47:51.647 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2561.708125,"timestamp":"2025-06-13T23:47:51.580Z"}]
2025-06-14 07:47:52.997 [info] 'WorkspaceManager[Windsurf]' Tracking enabled
2025-06-14 07:47:52.997 [info] 'WorkspaceManager[Windsurf]' Path metrics:
  - directories emitted: 122
  - files emitted: 905
  - other paths emitted: 1
  - total paths emitted: 1028
  - timing stats:
    - readDir: 6 ms
    - filter: 15 ms
    - yield: 1 ms
    - total: 22 ms
2025-06-14 07:47:52.997 [info] 'WorkspaceManager[Windsurf]' File metrics:
  - paths accepted: 143
  - paths not accessible: 0
  - not plain files: 0
  - large files: 40
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 91
  - mtime cache misses: 52
  - probe batches: 8
  - blob names probed: 186
  - files read: 839
  - blobs uploaded: 44
  - timing stats:
    - ingestPath: 1 ms
    - probe: 20434 ms
    - stat: 8 ms
    - read: 477 ms
    - upload: 23787 ms
2025-06-14 07:47:52.997 [info] 'WorkspaceManager[Windsurf]' Startup metrics:
  - create SourceFolder: 66 ms
  - read MtimeCache: 1 ms
  - pre-populate PathMap: 2 ms
  - create PathFilter: 9 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 23 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 42807 ms
  - enable persist: 2 ms
  - total: 42911 ms
2025-06-14 07:47:52.997 [info] 'WorkspaceManager' Workspace startup complete in 43018 ms
2025-06-14 08:08:51.470 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1690.2485,"timestamp":"2025-06-14T00:08:51.469Z"}]
2025-06-14 08:09:20.632 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1529.162542,"timestamp":"2025-06-14T00:09:20.542Z"}]
2025-06-14 08:10:20.966 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1895.343208,"timestamp":"2025-06-14T00:10:20.908Z"}]
2025-06-14 08:10:50.310 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1198.016791,"timestamp":"2025-06-14T00:10:50.209Z"}]
2025-06-14 08:15:20.105 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1009.042667,"timestamp":"2025-06-14T00:15:20.020Z"}]
2025-06-14 08:17:04.130 [info] 'AugmentExtension' Retrieving model config
2025-06-14 08:17:05.079 [info] 'AugmentExtension' Retrieved model config
2025-06-14 08:17:05.079 [info] 'AugmentExtension' Returning model config
2025-06-14 08:23:50.212 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1186.09075,"timestamp":"2025-06-14T00:23:50.197Z"}]
2025-06-14 08:24:21.547 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1765.0045,"timestamp":"2025-06-14T00:24:21.539Z"}]
2025-06-14 08:25:20.804 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1745.686792,"timestamp":"2025-06-14T00:25:20.754Z"}]
2025-06-14 08:32:20.085 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1016.8505,"timestamp":"2025-06-14T00:32:20.024Z"}]
2025-06-14 08:32:50.954 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1877.385834,"timestamp":"2025-06-14T00:32:50.886Z"}]
2025-06-14 08:33:20.124 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1095.262791,"timestamp":"2025-06-14T00:33:20.103Z"}]
2025-06-14 08:33:50.508 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1459.240834,"timestamp":"2025-06-14T00:33:50.466Z"}]
2025-06-14 08:34:20.185 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1145.512667,"timestamp":"2025-06-14T00:34:20.157Z"}]
2025-06-14 08:34:50.366 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1257.459208,"timestamp":"2025-06-14T00:34:50.264Z"}]
2025-06-14 08:35:21.234 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1455.948958,"timestamp":"2025-06-14T00:35:21.227Z"}]
2025-06-14 08:35:51.750 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1968.310541,"timestamp":"2025-06-14T00:35:51.737Z"}]
2025-06-14 08:36:29.006 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1144.462125,"timestamp":"2025-06-14T00:36:28.929Z"}]
2025-06-14 08:36:50.068 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1032.907416,"timestamp":"2025-06-14T00:36:50.041Z"}]
2025-06-14 08:37:20.147 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1053.536417,"timestamp":"2025-06-14T00:37:20.060Z"}]
2025-06-14 08:37:50.119 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1013.257416,"timestamp":"2025-06-14T00:37:50.020Z"}]
2025-06-14 08:38:20.421 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1339.658083,"timestamp":"2025-06-14T00:38:20.346Z"}]
2025-06-14 08:38:50.397 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1374.479459,"timestamp":"2025-06-14T00:38:50.382Z"}]
2025-06-14 08:39:21.840 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2035.196625,"timestamp":"2025-06-14T00:39:21.803Z"}]
2025-06-14 08:39:51.519 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1662.787417,"timestamp":"2025-06-14T00:39:51.427Z"}]
2025-06-14 08:40:28.097 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2152.093625,"timestamp":"2025-06-14T00:40:28.060Z"}]
2025-06-14 08:40:50.368 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1316.175459,"timestamp":"2025-06-14T00:40:50.322Z"}]
2025-06-14 08:41:50.099 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1081.537875,"timestamp":"2025-06-14T00:41:50.088Z"}]
2025-06-14 08:42:20.403 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1373.333792,"timestamp":"2025-06-14T00:42:20.380Z"}]
2025-06-14 08:43:20.132 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1095.582,"timestamp":"2025-06-14T00:43:20.102Z"}]
2025-06-14 08:43:34.112 [error] 'AugmentExtension' API request 91350144-1a61-4a22-bd67-a33032e8e809 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 08:43:34.625 [error] 'AugmentExtension' API request a6f955e7-49b5-4b75-902a-a13ffa4df2f7 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 08:43:34.625 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 08:43:34.625 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-14 08:43:37.546 [info] 'DiskFileManager[Windsurf]' Operation succeeded after 1 transient failures
2025-06-14 08:43:51.061 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1991.467959,"timestamp":"2025-06-14T00:43:50.996Z"}]
2025-06-14 08:44:20.336 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1264.398208,"timestamp":"2025-06-14T00:44:20.270Z"}]
2025-06-14 08:44:50.412 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1401.732125,"timestamp":"2025-06-14T00:44:50.407Z"}]
2025-06-14 08:45:20.613 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1606.128708,"timestamp":"2025-06-14T00:45:20.611Z"}]
2025-06-14 08:45:50.235 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1160.823083,"timestamp":"2025-06-14T00:45:50.167Z"}]
2025-06-14 08:46:51.019 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1964.55425,"timestamp":"2025-06-14T00:46:50.970Z"}]
2025-06-14 08:47:04.128 [info] 'AugmentExtension' Retrieving model config
2025-06-14 08:47:06.932 [info] 'AugmentExtension' Retrieved model config
2025-06-14 08:47:06.932 [info] 'AugmentExtension' Returning model config
2025-06-14 08:47:22.866 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":3820.269417,"timestamp":"2025-06-14T00:47:22.824Z"}]
2025-06-14 08:47:51.211 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2103.908917,"timestamp":"2025-06-14T00:47:51.108Z"}]
2025-06-14 08:48:21.326 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2301.759125,"timestamp":"2025-06-14T00:48:21.307Z"}]
2025-06-14 08:48:50.168 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1100.360167,"timestamp":"2025-06-14T00:48:50.104Z"}]
2025-06-14 08:49:20.807 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1714.034542,"timestamp":"2025-06-14T00:49:20.719Z"}]
2025-06-14 08:49:50.248 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1168.949792,"timestamp":"2025-06-14T00:49:50.173Z"}]
2025-06-14 08:50:21.547 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2473.834916,"timestamp":"2025-06-14T00:50:21.479Z"}]
2025-06-14 08:51:20.486 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1388.2255,"timestamp":"2025-06-14T00:51:20.392Z"}]
2025-06-14 08:52:20.982 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1925.391334,"timestamp":"2025-06-14T00:52:20.929Z"}]
2025-06-14 08:52:50.286 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1247.497583,"timestamp":"2025-06-14T00:52:50.252Z"}]
2025-06-14 08:53:20.445 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1438.708625,"timestamp":"2025-06-14T00:53:20.442Z"}]
2025-06-14 08:53:50.504 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1447.981291,"timestamp":"2025-06-14T00:53:50.453Z"}]
2025-06-14 08:54:23.329 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":4325.434583,"timestamp":"2025-06-14T00:54:23.328Z"}]
2025-06-14 08:54:50.213 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1137.885333,"timestamp":"2025-06-14T00:54:50.141Z"}]
2025-06-14 08:55:20.242 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1192.508792,"timestamp":"2025-06-14T00:55:20.196Z"}]
2025-06-14 08:55:50.075 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1039.79475,"timestamp":"2025-06-14T00:55:50.043Z"}]
2025-06-14 08:56:20.082 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1067.517666,"timestamp":"2025-06-14T00:56:20.073Z"}]
2025-06-14 08:56:50.412 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1407.371417,"timestamp":"2025-06-14T00:56:50.411Z"}]
2025-06-14 08:57:22.891 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":3856.952,"timestamp":"2025-06-14T00:57:22.860Z"}]
2025-06-14 08:57:50.150 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1080.215083,"timestamp":"2025-06-14T00:57:50.115Z"}]
2025-06-14 08:58:20.230 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1180.416958,"timestamp":"2025-06-14T00:58:20.183Z"}]
2025-06-14 08:59:20.623 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1546.381375,"timestamp":"2025-06-14T00:59:20.549Z"}]
2025-06-14 08:59:51.447 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2429.415292,"timestamp":"2025-06-14T00:59:51.432Z"}]
2025-06-14 09:00:20.467 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1439.732459,"timestamp":"2025-06-14T01:00:20.446Z"}]
2025-06-14 09:00:50.551 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1486.417916,"timestamp":"2025-06-14T01:00:50.489Z"}]
2025-06-14 09:01:20.465 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1419.08875,"timestamp":"2025-06-14T01:01:20.421Z"}]
2025-06-14 09:01:50.990 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1135.751916,"timestamp":"2025-06-14T01:01:50.900Z"}]
2025-06-14 09:02:10.810 [error] 'AugmentExtension' API request 1f940a25-e78c-4adf-bd3c-be1c9fd3f4e8 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:02:11.317 [error] 'AugmentExtension' API request 46c0be3a-6a43-4506-80dd-7d80d5e47bc3 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:02:11.317 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:02:11.318 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-14 09:02:13.195 [info] 'DiskFileManager[Windsurf]' Operation succeeded after 1 transient failures
2025-06-14 09:02:21.526 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1752.325375,"timestamp":"2025-06-14T01:02:21.513Z"}]
2025-06-14 09:03:37.140 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1873.066584,"timestamp":"2025-06-14T01:03:37.063Z"}]
2025-06-14 09:03:51.866 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2789.270167,"timestamp":"2025-06-14T01:03:51.791Z"}]
2025-06-14 09:04:50.265 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1235.820333,"timestamp":"2025-06-14T01:04:50.238Z"}]
2025-06-14 09:07:20.925 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1153.816292,"timestamp":"2025-06-14T01:07:20.919Z"}]
2025-06-14 09:07:50.033 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1021.850583,"timestamp":"2025-06-14T01:07:50.026Z"}]
2025-06-14 09:08:21.001 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1923.127416,"timestamp":"2025-06-14T01:08:20.928Z"}]
2025-06-14 09:08:50.550 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1460.575542,"timestamp":"2025-06-14T01:08:50.462Z"}]
2025-06-14 09:09:20.959 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1101.278375,"timestamp":"2025-06-14T01:09:20.870Z"}]
2025-06-14 09:10:20.082 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1057.914083,"timestamp":"2025-06-14T01:10:20.059Z"}]
2025-06-14 09:10:51.695 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1921.989083,"timestamp":"2025-06-14T01:10:51.681Z"}]
2025-06-14 09:11:20.929 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1871.473375,"timestamp":"2025-06-14T01:11:20.874Z"}]
2025-06-14 09:11:50.097 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1085.511541,"timestamp":"2025-06-14T01:11:50.086Z"}]
2025-06-14 09:12:03.030 [error] 'AugmentExtension' API request 34fa31c7-0f07-4c4a-b7c5-2a9f5a3b6839 to https://i1.api.augmentcode.com/batch-upload failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:12:03.538 [error] 'AugmentExtension' API request ad79f085-8442-4e74-8999-7fcd59d76723 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:12:03.538 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:12:03.539 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-14 09:12:04.622 [info] 'DiskFileManager[Windsurf]' Operation succeeded after 1 transient failures
2025-06-14 09:12:20.168 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1097.008166,"timestamp":"2025-06-14T01:12:20.097Z"}]
2025-06-14 09:12:50.185 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1111.987292,"timestamp":"2025-06-14T01:12:50.113Z"}]
2025-06-14 09:13:20.616 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1597.73675,"timestamp":"2025-06-14T01:13:20.600Z"}]
2025-06-14 09:15:51.688 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2597.517917,"timestamp":"2025-06-14T01:15:51.608Z"}]
2025-06-14 09:16:50.469 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1381.559791,"timestamp":"2025-06-14T01:16:50.384Z"}]
2025-06-14 09:17:04.127 [info] 'AugmentExtension' Retrieving model config
2025-06-14 09:17:06.209 [info] 'AugmentExtension' Retrieved model config
2025-06-14 09:17:06.209 [info] 'AugmentExtension' Returning model config
2025-06-14 09:17:20.943 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1901.706291,"timestamp":"2025-06-14T01:17:20.907Z"}]
2025-06-14 09:18:49.918 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1049.702375,"timestamp":"2025-06-14T01:18:49.823Z"}]
2025-06-14 09:19:22.712 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":3919.520791,"timestamp":"2025-06-14T01:19:22.689Z"}]
2025-06-14 09:19:50.077 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1220.271583,"timestamp":"2025-06-14T01:19:49.987Z"}]
2025-06-14 09:20:08.294 [error] 'AugmentExtension' API request c431ee68-217b-47d0-8c9d-5360334de49a to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:20:08.802 [error] 'AugmentExtension' API request 67e96349-ac39-491b-9017-7ef3e1284a64 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:20:08.803 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:20:08.803 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-14 09:20:10.346 [info] 'DiskFileManager[Windsurf]' Operation succeeded after 1 transient failures
2025-06-14 09:21:20.202 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1398.360167,"timestamp":"2025-06-14T01:21:20.163Z"}]
2025-06-14 09:22:19.915 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1088.173166,"timestamp":"2025-06-14T01:22:19.852Z"}]
2025-06-14 09:25:20.245 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1444.690875,"timestamp":"2025-06-14T01:25:20.201Z"}]
2025-06-14 09:25:50.001 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1184.041667,"timestamp":"2025-06-14T01:25:49.943Z"}]
2025-06-14 09:26:50.518 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1764.667792,"timestamp":"2025-06-14T01:26:50.517Z"}]
2025-06-14 09:27:20.298 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1478.790708,"timestamp":"2025-06-14T01:27:20.231Z"}]
2025-06-14 09:27:50.312 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1509.986583,"timestamp":"2025-06-14T01:27:50.259Z"}]
2025-06-14 09:29:21.027 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2236.696292,"timestamp":"2025-06-14T01:29:20.983Z"}]
2025-06-14 09:30:19.976 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1189.50575,"timestamp":"2025-06-14T01:30:19.934Z"}]
2025-06-14 09:32:21.200 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2388.333708,"timestamp":"2025-06-14T01:32:21.138Z"}]
2025-06-14 09:33:50.767 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1910.012125,"timestamp":"2025-06-14T01:33:50.738Z"}]
2025-06-14 09:34:20.020 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1120.059542,"timestamp":"2025-06-14T01:34:19.947Z"}]
2025-06-14 09:35:24.698 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":5812.420833,"timestamp":"2025-06-14T01:35:24.638Z"}]
2025-06-14 09:36:20.059 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1144.867625,"timestamp":"2025-06-14T01:36:19.969Z"}]
2025-06-14 09:37:00.209 [error] 'AugmentExtension' API request 2554fbc9-313e-4095-b924-241858ec42b6 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:00.716 [error] 'AugmentExtension' API request f7cbdc4a-d0a9-49ed-a595-2966f82256f6 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:00.716 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:00.716 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-14 09:37:03.203 [error] 'AugmentExtension' API request a510b804-bde2-404f-b814-a9ccd1ed942e to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:03.709 [error] 'AugmentExtension' API request 327bb9bb-f9d5-4281-8076-c756de5d1f55 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:03.709 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:03.710 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-14 09:37:05.830 [error] 'AugmentExtension' API request 460ea7a6-ff38-4650-a425-ec9a6f76eeae to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:06.340 [error] 'AugmentExtension' API request 66ef2297-96ed-4e65-8b4d-730fc77e2829 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:06.340 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:06.340 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 200 ms; retries = 1
2025-06-14 09:37:08.821 [error] 'AugmentExtension' API request 3efab8b1-6498-49ac-bdda-9ed23228c74c to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:09.327 [error] 'AugmentExtension' API request 3b3b62f5-db79-46fc-9630-7f5ba140ca5e to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:09.327 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:09.328 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 200 ms; retries = 1
2025-06-14 09:37:11.557 [error] 'AugmentExtension' API request 5b67cd45-60e1-426e-a1e6-99623c8bd61f to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:12.064 [error] 'AugmentExtension' API request 4ba5d872-74e2-4b71-87b4-19e77fa366f5 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:12.064 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:12.065 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 400 ms; retries = 2
2025-06-14 09:37:14.544 [error] 'AugmentExtension' API request 3d0bec2e-9455-4831-9b3c-ef9062189c45 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:15.051 [error] 'AugmentExtension' API request 2e9d07c6-3a8b-450c-ae09-157c89e66d0e to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:15.051 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:15.051 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 400 ms; retries = 2
2025-06-14 09:37:17.482 [error] 'AugmentExtension' API request 3b5d604c-0c6e-42ad-b050-588f5c52e7c7 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:17.986 [error] 'AugmentExtension' API request 900d9f4f-a520-453b-aa7c-8d4f74ecab00 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:17.986 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:17.986 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 800 ms; retries = 3
2025-06-14 09:37:20.466 [error] 'AugmentExtension' API request e6f258c6-bdc7-47d8-98a8-a68044aa4d5e to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:20.968 [error] 'AugmentExtension' API request d80bbcb8-912d-4583-8dee-9b223a848f93 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:20.968 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:20.969 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 800 ms; retries = 3
2025-06-14 09:37:23.806 [error] 'AugmentExtension' API request 75f39a96-bcc6-4a41-831e-cf251b03d9a8 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:23.825 [error] 'AugmentExtension' API request bc76fd3f-f47f-4a3a-81b3-b0b4837291e4 to https://i1.api.augmentcode.com/subscription-info failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:24.312 [error] 'AugmentExtension' API request b978034b-57c4-40c6-aaa2-1ec56c196a56 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:24.312 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:24.312 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 1600 ms; retries = 4
2025-06-14 09:37:24.329 [error] 'AugmentExtension' API request 6d1080e4-1018-4bcf-9504-fcda097b0337 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:24.330 [error] 'AugmentExtension' Dropping error report "subscription-info call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:24.330 [error] 'ChatApp' Failed to get subscription info: Error: fetch failed
2025-06-14 09:37:24.363 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":5509.648666,"timestamp":"2025-06-14T01:37:24.330Z"}]
2025-06-14 09:37:26.788 [error] 'AugmentExtension' API request 4b759c9b-5382-4fb8-b3f8-402840143029 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:27.221 [error] 'AugmentExtension' API request 77cacfc0-2bce-4887-89ad-a1c39a181941 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:27.291 [error] 'AugmentExtension' API request e14694af-a435-430b-acd2-025d9e909290 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:27.291 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:27.291 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 1600 ms; retries = 4
2025-06-14 09:37:27.727 [error] 'AugmentExtension' API request d95c61c3-d0dc-48f5-a443-01dd593eaecf to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:27.727 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:27.727 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-14 09:37:30.926 [error] 'AugmentExtension' API request 135b05f1-22e4-49ce-99ba-dc136d9df859 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:31.431 [error] 'AugmentExtension' API request e412f920-7e0e-4230-8bc7-2d90906257df to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:31.431 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:31.435 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 3200 ms; retries = 5
2025-06-14 09:37:32.845 [error] 'AugmentExtension' API request a4f393d2-6872-48b5-ba1f-0103bcdc5b9c to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:33.351 [error] 'AugmentExtension' API request 149ad704-7efd-43d8-ae93-e524a5e4b6f5 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:33.351 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:33.351 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 200 ms; retries = 1
2025-06-14 09:37:33.905 [error] 'AugmentExtension' API request 745a55ec-2f07-4d77-93df-8183668e6db3 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:34.410 [error] 'AugmentExtension' API request 51f3923e-4427-4799-b09c-e64d0c34d637 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:34.410 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:34.410 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 3200 ms; retries = 5
2025-06-14 09:37:38.567 [error] 'AugmentExtension' API request f8f150cf-42eb-443e-940b-77d5e661b016 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:39.074 [error] 'AugmentExtension' API request 66a87ae2-ca3b-4665-b892-c61ae1dbd226 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:39.074 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:39.074 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 400 ms; retries = 2
2025-06-14 09:37:39.653 [error] 'AugmentExtension' API request 101ded7b-faf5-4891-8539-0975dafeae89 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:40.158 [error] 'AugmentExtension' API request dfbb7ade-43f4-4e34-bf98-1907138c9c2c to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:40.158 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:40.158 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 6400 ms; retries = 6
2025-06-14 09:37:42.625 [error] 'AugmentExtension' API request fae7ef67-97ce-4207-8291-08eb9f341cc8 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:43.130 [error] 'AugmentExtension' API request 92693786-dd40-4c9f-979c-ad5e05109896 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:43.130 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:43.130 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 6400 ms; retries = 6
2025-06-14 09:37:44.487 [error] 'AugmentExtension' API request fdadb0b3-63b4-4dd7-bef8-7141def668e4 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:44.990 [error] 'AugmentExtension' API request df9e4069-496c-4adc-abf4-2961a2112a00 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:44.990 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:44.990 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 800 ms; retries = 3
2025-06-14 09:37:50.805 [error] 'AugmentExtension' API request f3147d0f-2025-4164-b476-8a405ec57bbe to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:51.311 [error] 'AugmentExtension' API request 6ed072f2-72c9-4805-8733-d75749356a55 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:51.311 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:51.311 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 1600 ms; retries = 4
2025-06-14 09:37:51.567 [error] 'AugmentExtension' API request 283f0e2b-f0ad-436f-a320-0169c550de85 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:52.073 [error] 'AugmentExtension' API request d4fae5c5-4017-4d9e-89ac-5a00ee88cad3 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:52.073 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:52.073 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 12800 ms; retries = 7
2025-06-14 09:37:53.835 [error] 'AugmentExtension' API request e5e8936d-0430-43ce-8c9f-2a1080293da5 to https://i1.api.augmentcode.com/subscription-info failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:54.349 [error] 'AugmentExtension' API request 1c92a0d0-a979-4dc5-8222-76de56f07fdf to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:54.349 [error] 'AugmentExtension' Dropping error report "subscription-info call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:54.351 [error] 'ChatApp' Failed to get subscription info: Error: fetch failed
2025-06-14 09:37:54.423 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":5531.197541,"timestamp":"2025-06-14T01:37:54.352Z"}]
2025-06-14 09:37:54.541 [error] 'AugmentExtension' API request b842381c-231a-4c15-91d8-40bcb1904a39 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:55.044 [error] 'AugmentExtension' API request 53999396-47b3-4528-b667-c0bba7db57e9 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:55.044 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:55.044 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 12800 ms; retries = 7
2025-06-14 09:37:57.927 [error] 'AugmentExtension' API request 1b9d767f-83d2-4259-93be-7761c9fefe62 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:37:58.435 [error] 'AugmentExtension' API request 7fbb7d52-a5b5-469b-b5c1-4b1361030e48 to https://i1.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-14 09:37:58.435 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-14 09:37:58.435 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 3200 ms; retries = 5
2025-06-14 09:38:01.855 [error] 'AugmentExtension' API request ba9122f0-c6df-493d-bac9-14bb9953f626 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:38:01.898 [error] 'AugmentExtension' API request e0e2af6f-c373-45e3-98a6-67d96335226e to https://i1.api.augmentcode.com/report-error failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:38:01.898 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: fetch failed
2025-06-14 09:38:01.898 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 6400 ms; retries = 6
2025-06-14 09:38:04.908 [error] 'AugmentExtension' API request 33149a95-9ce5-4537-ba9a-1b89c191c078 to https://i1.api.augmentcode.com/find-missing failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:38:04.920 [error] 'AugmentExtension' API request 05f2580a-5caa-48f5-b79c-5acb46a7fddc to https://i1.api.augmentcode.com/report-error failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-06-14 09:38:04.921 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: fetch failed
2025-06-14 09:38:04.921 [info] 'DiskFileManager[Windsurf]' Operation failed with error Error: fetch failed, retrying in 25600 ms; retries = 8
2025-06-14 09:38:08.605 [info] 'DiskFileManager[Windsurf]' Operation succeeded after 8 transient failures
2025-06-14 09:38:10.395 [info] 'DiskFileManager[Windsurf]' Operation succeeded after 7 transient failures
2025-06-14 09:38:31.626 [info] 'DiskFileManager[Windsurf]' Operation succeeded after 9 transient failures
2025-06-14 09:38:50.639 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1014.695792,"timestamp":"2025-06-14T01:38:50.601Z"}]
2025-06-14 09:39:20.667 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1046.196208,"timestamp":"2025-06-14T01:39:20.629Z"}]
2025-06-14 09:39:51.403 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1742.887709,"timestamp":"2025-06-14T01:39:51.324Z"}]
2025-06-14 09:40:21.239 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2356.861833,"timestamp":"2025-06-14T01:40:21.175Z"}]
2025-06-14 09:42:20.633 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1806.001917,"timestamp":"2025-06-14T01:42:20.620Z"}]
2025-06-14 09:43:19.956 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1051.821125,"timestamp":"2025-06-14T01:43:19.865Z"}]
2025-06-14 09:44:21.483 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1874.889167,"timestamp":"2025-06-14T01:44:21.447Z"}]
2025-06-14 09:44:51.754 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2184.664167,"timestamp":"2025-06-14T01:44:51.754Z"}]
2025-06-14 09:47:03.939 [info] 'AugmentExtension' Retrieving model config
2025-06-14 09:47:05.392 [info] 'AugmentExtension' Retrieved model config
2025-06-14 09:47:05.392 [info] 'AugmentExtension' Returning model config
2025-06-14 09:49:18.189 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1549.39825,"timestamp":"2025-06-14T01:49:18.118Z"}]
2025-06-14 09:51:02.290 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1300.1935,"timestamp":"2025-06-14T01:51:02.208Z"}]
2025-06-14 09:51:19.961 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1077.6095,"timestamp":"2025-06-14T01:51:19.890Z"}]
2025-06-14 09:52:49.940 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1066.902375,"timestamp":"2025-06-14T01:52:49.877Z"}]
2025-06-14 09:53:19.934 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1096.127084,"timestamp":"2025-06-14T01:53:19.907Z"}]
2025-06-14 09:53:50.073 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1212.66275,"timestamp":"2025-06-14T01:53:50.022Z"}]
2025-06-14 09:54:20.253 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1429.631708,"timestamp":"2025-06-14T01:54:20.240Z"}]
2025-06-14 09:54:51.075 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2184.475209,"timestamp":"2025-06-14T01:54:50.992Z"}]
2025-06-14 09:55:33.893 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-14 09:55:33.932 [info] 'TaskManager' Setting current root task UUID to 008da93c-09b9-4dfe-9547-bb758edab4e8
2025-06-14 09:55:33.933 [info] 'TaskManager' Setting current root task UUID to 008da93c-09b9-4dfe-9547-bb758edab4e8
2025-06-14 09:55:50.024 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1132.788834,"timestamp":"2025-06-14T01:55:49.937Z"}]
2025-06-14 09:56:20.614 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1806.7345,"timestamp":"2025-06-14T01:56:20.612Z"}]
2025-06-14 09:57:21.129 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2247.629125,"timestamp":"2025-06-14T01:57:21.052Z"}]
2025-06-14 09:57:50.073 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1209.556084,"timestamp":"2025-06-14T01:57:50.013Z"}]
2025-06-14 09:58:19.986 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1137.252041,"timestamp":"2025-06-14T01:58:19.941Z"}]
2025-06-14 09:58:49.886 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1006.326916,"timestamp":"2025-06-14T01:58:49.809Z"}]
2025-06-14 10:00:51.294 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2486.493792,"timestamp":"2025-06-14T02:00:51.285Z"}]
2025-06-14 10:00:57.565 [info] 'PathMap' Closed source folder /Users/<USER>/Library/Application Support/Windsurf with id 100
2025-06-14 10:00:57.565 [info] 'OpenFileManager' Closed source folder 100
