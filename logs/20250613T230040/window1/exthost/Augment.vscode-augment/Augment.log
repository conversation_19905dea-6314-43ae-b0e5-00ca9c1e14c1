2025-06-13 23:00:47.526 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 23:00:47.526 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"用中文回答"},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-13 23:00:47.526 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-13 23:00:47.526 [info] 'AugmentExtension' Retrieving model config
2025-06-13 23:00:48.891 [info] 'AugmentExtension' Retrieved model config
2025-06-13 23:00:48.891 [info] 'AugmentExtension' Returning model config
2025-06-13 23:00:48.905 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-13 23:00:48.905 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-06-13 23:00:48.905 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-13 23:00:48.905 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-13 23:00:48.905 [info] 'SyncingPermissionTracker' Permission to sync folder /Users/<USER>/Library/Application Support/Windsurf unknown: no permission information recorded
2025-06-13 23:00:48.905 [info] 'WorkspaceManager' Adding workspace folder Windsurf; folderRoot = /Users/<USER>/Library/Application Support/Windsurf; syncingPermission = unknown
2025-06-13 23:00:48.948 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-13 23:00:48.948 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 23:00:48.948 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-13 23:00:48.949 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-13 23:00:48.963 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-13 23:00:48.964 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-13 23:00:48.981 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-13 23:00:48.982 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-13 23:00:48.983 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-13 23:00:48.983 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 23:00:48.992 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 23:00:48.993 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 23:00:49.118 [info] 'WorkspaceManager' Beginning full qualification of source folder /Users/<USER>/Library/Application Support/Windsurf
2025-06-13 23:00:49.171 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-13 23:00:49.249 [info] 'TaskManager' Setting current root task UUID to 25dd58e4-b424-4c48-9641-042a10254e92
2025-06-13 23:00:49.250 [info] 'TaskManager' Setting current root task UUID to 25dd58e4-b424-4c48-9641-042a10254e92
2025-06-13 23:00:49.254 [info] 'TaskManager' Setting current root task UUID to 489af9ee-a70b-4184-810d-9baadcbd5fd7
2025-06-13 23:00:49.255 [info] 'TaskManager' Setting current root task UUID to 489af9ee-a70b-4184-810d-9baadcbd5fd7
2025-06-13 23:00:49.256 [info] 'TaskManager' Setting current root task UUID to 387b9221-ef76-4360-97ea-9642fccc0a22
2025-06-13 23:00:49.256 [info] 'TaskManager' Setting current root task UUID to 387b9221-ef76-4360-97ea-9642fccc0a22
2025-06-13 23:00:50.305 [info] 'StallDetector' Recent work: [{"name":"get-remote-agent-overviews-request","durationMs":1290.59825,"timestamp":"2025-06-13T15:00:50.284Z"}]
2025-06-13 23:00:50.716 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1726.995458,"timestamp":"2025-06-13T15:00:50.711Z"}]
2025-06-13 23:00:50.804 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-13 23:00:50.804 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-13 23:00:50.895 [info] 'WorkspaceManager' Finished full qualification of source folder /Users/<USER>/Library/Application Support/Windsurf: trackable files: 854, uploaded fraction: 0.2692307692307692, is repo: false
2025-06-13 23:00:50.895 [info] 'WorkspaceManager' Requesting syncing permission because source folder does not appear to be a source repo
2025-06-13 23:00:50.897 [info] 'AwaitingSyncingPermissionApp' Registering AwaitingSyncingPermissionApp
2025-06-13 23:00:51.818 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 23:00:54.243 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-7e284cb8-809d-499a-b3b1-d7d8d95c32a9.json'
2025-06-13 23:01:00.410 [info] 'AwaitingSyncingPermissionApp' User granted syncing permission
2025-06-13 23:01:00.410 [info] 'WorkspaceManager' Enabling syncing for all trackable source folders
2025-06-13 23:01:00.410 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /Users/<USER>/Library/Application Support/Windsurf (explicit) at 6/13/2025, 11:01:00 PM
2025-06-13 23:01:00.414 [info] 'WorkspaceManager[Windsurf]' Start tracking
2025-06-13 23:01:00.417 [info] 'PathMap' Opened source folder /Users/<USER>/Library/Application Support/Windsurf with id 100
2025-06-13 23:01:00.417 [info] 'OpenFileManager' Opened source folder 100
2025-06-13 23:01:00.418 [info] 'MtimeCache[Windsurf]' reading blob name cache from /Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json
2025-06-13 23:01:00.419 [info] 'MtimeCache[Windsurf]' no blob name cache found at /Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json (probably new source folder); error = ENOENT: no such file or directory, open '/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json'
2025-06-13 23:01:00.460 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-13 23:01:00.460 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 23:01:00.465 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-13 23:01:00.465 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-13 23:01:00.466 [info] 'TaskManager' Setting current root task UUID to 387b9221-ef76-4360-97ea-9642fccc0a22
2025-06-13 23:01:00.542 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-13 23:01:01.944 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1450.429625,"timestamp":"2025-06-13T15:01:01.917Z"},{"name":"get-remote-agent-overviews-request","durationMs":1431.4835,"timestamp":"2025-06-13T15:01:01.918Z"}]
2025-06-13 23:01:04.900 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-13 23:01:04.900 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-13 23:01:04.900 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-13 23:01:06.798 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-13 23:01:06.798 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-13 23:01:25.584 [info] 'WorkspaceManager[Windsurf]' Tracking enabled
2025-06-13 23:01:25.584 [info] 'WorkspaceManager[Windsurf]' Path metrics:
  - directories emitted: 102
  - files emitted: 860
  - other paths emitted: 1
  - total paths emitted: 963
  - timing stats:
    - readDir: 7 ms
    - filter: 8 ms
    - yield: 4 ms
    - total: 23 ms
2025-06-13 23:01:25.584 [info] 'WorkspaceManager[Windsurf]' File metrics:
  - paths accepted: 90
  - paths not accessible: 0
  - not plain files: 0
  - large files: 43
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 0
  - mtime cache misses: 90
  - probe batches: 8
  - blob names probed: 155
  - files read: 937
  - blobs uploaded: 65
  - timing stats:
    - ingestPath: 2 ms
    - probe: 11532 ms
    - stat: 8 ms
    - read: 247 ms
    - upload: 8522 ms
2025-06-13 23:01:25.584 [info] 'WorkspaceManager[Windsurf]' Startup metrics:
  - create SourceFolder: 4 ms
  - read MtimeCache: 1 ms
  - pre-populate PathMap: 0 ms
  - create PathFilter: 15 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 25 ms
  - purge stale PathMap entries: 0 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 25123 ms
  - enable persist: 1 ms
  - total: 25169 ms
2025-06-13 23:01:25.584 [info] 'WorkspaceManager' Workspace startup complete in 36687 ms
2025-06-13 23:01:25.727 [info] 'WorkspaceManager[Windsurf]' Directory created: User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7
2025-06-13 23:01:32.543 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1997.820041,"timestamp":"2025-06-13T15:01:32.466Z"}]
2025-06-13 23:01:39.032 [info] 'PathMap' Closed source folder /Users/<USER>/Library/Application Support/Windsurf with id 100
2025-06-13 23:01:39.032 [info] 'OpenFileManager' Closed source folder 100
