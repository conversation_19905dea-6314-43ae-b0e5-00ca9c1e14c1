2025-06-14 11:28:34.040 [info] 2025/06/14 11:28:34 maxprocs: Leaving GOMAXPROCS=8: CPU quota undefined
2025-06-14 11:28:34.359 [info] I0614 11:28:34.359785 78039 main.go:602] Setting GOMAXPROCS to 4
2025-06-14 11:28:34.361 [info] I0614 11:28:34.361285 78039 main.go:817] Starting language server process with pid 78039
2025-06-14 11:28:34.362 [info] I0614 11:28:34.361941 78039 server.go:292] Language server will attempt to listen on host 127.0.0.1
2025-06-14 11:28:34.363 [info] I0614 11:28:34.363236 78039 server.go:299] Language server listening on random port at 64470
2025-06-14 11:28:35.158 [info] I0614 11:28:35.158082 78039 server.go:420] Created extension server client at port 64465
2025-06-14 11:28:35.317 [info] I0614 11:28:35.316959 78039 server.go:948] Local search is enabled, will index local files.
2025-06-14 11:28:35.317 [info] I0614 11:28:35.316980 78039 server.go:952] Using 2 indexer workers
2025-06-14 11:28:36.040 [info] (Windsurf) 2025-06-14 11:28:36.038 [INFO]: Language server started
2025-06-14 11:28:36.327 [info] (Windsurf) 2025-06-14 11:28:36.327 [INFO]: LS lspClient started successfully
2025-06-14 11:28:41.476 [info] (Windsurf) 2025-06-14 11:28:41.476 [INFO]: [Terminal] Command completed: echo 'Terminal capability test' exit code 0
2025-06-14 11:28:42.230 [info] (Windsurf) 2025-06-14 11:28:42.230 [INFO]: [Terminal] Command completed: echo 'Terminal capability test' exit code 0
2025-06-14 11:28:42.432 [info] (Windsurf) 2025-06-14 11:28:42.432 [INFO]: [Terminal] Command completed: echo 'Terminal capability test' exit code 0
2025-06-14 11:28:42.574 [info] (Windsurf) 2025-06-14 11:28:42.574 [INFO]: [Terminal] Command completed: echo 'Terminal capability test' exit code 0
