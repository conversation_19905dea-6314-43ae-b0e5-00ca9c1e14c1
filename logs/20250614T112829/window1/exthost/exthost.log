2025-06-14 11:28:31.899 [info] Extension host with pid 77205 started
2025-06-14 11:28:31.899 [info] Skipping acquiring lock for /Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec.
2025-06-14 11:28:31.909 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-14 11:28:31.929 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-14 11:28:31.939 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-14 11:28:31.966 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-14 11:28:32.034 [info] ExtensionService#_doActivateExtension codeium.windsurf, startup: true, activationEvent: '*'
2025-06-14 11:28:36.053 [info] Eager extensions activated
2025-06-14 11:28:36.059 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 11:28:36.061 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 11:28:36.072 [info] ExtensionService#_doActivateExtension Codeium.windsurf-dev-containers, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 11:28:36.075 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 11:28:36.231 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
