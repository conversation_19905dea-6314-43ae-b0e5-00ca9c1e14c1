2025-06-14 00:29:04.988 [info] Extension host with pid 61485 started
2025-06-14 00:29:05.003 [info] Skipping acquiring lock for /Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec.
2025-06-14 00:29:05.015 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-14 00:29:05.034 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-14 00:29:05.042 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-14 00:29:05.067 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-14 00:29:05.132 [info] ExtensionService#_doActivateExtension codeium.windsurf, startup: true, activationEvent: '*'
2025-06-14 00:29:12.000 [info] Eager extensions activated
2025-06-14 00:29:12.006 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 00:29:12.008 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 00:29:12.019 [info] ExtensionService#_doActivateExtension Codeium.windsurf-dev-containers, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 00:29:12.022 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 00:29:12.185 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 00:30:34.730 [info] Extension host terminating: renderer closed the MessagePort
2025-06-14 00:30:34.750 [error] Canceled: Canceled
	at new t2 (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:113090)
	at qA.U (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:117701)
	at s.<computed>.r.charCodeAt.s.<computed> (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115195)
	at CI.g (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40984)
	at CI.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40488)
	at i.registerCommand.description (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:39818)
	at CI.h (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:41504)
	at CI.g (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40581)
	at CI.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40488)
	at Object.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:39981)
	at e.decorate (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2222:17310)
	at e._drawDecorations (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:16681)
	at e._updateSuggestions (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:16233)
	at e._handleSuggestionsChanged (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:14666)
	at /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:9525
	at /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2239:1028
	at Array.forEach (<anonymous>)
	at E4.dispatchSuggestionsChangedEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2239:1012)
	at E4.clear (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2239:2882)
	at hb.<anonymous> (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2210:88614)
	at hb.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:107:9018)
	at qd.dispose (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:1146)
	at e.dispose (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:1540)
	at e.disable (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:136718)
	at i (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144027)
	at s (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144106)
	at hb.<anonymous> (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144236)
	at hb.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:107:9018)
	at On (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:701)
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:15142
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:1011
	at Object.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:8:705)
	at cte.eb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:12069)
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10024
	at Array.map (<anonymous>)
	at cte.$ (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10012)
	at cte.terminate (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10255)
	at Zx.terminate (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:117:1434)
	at _s (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:5722)
	at MessagePortMain.<anonymous> (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:2262)
	at MessagePortMain.emit (node:events:518:28)
	at MessagePortMain.emit (node:domain:489:12)
	at MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949) setContext undefined
2025-06-14 00:30:34.750 [error] Canceled: Canceled
	at new t2 (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:113090)
	at qA.U (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:117701)
	at s.<computed>.r.charCodeAt.s.<computed> (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115195)
	at CI.g (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40984)
	at CI.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40488)
	at i.registerCommand.description (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:39818)
	at CI.h (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:41504)
	at CI.g (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40581)
	at CI.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40488)
	at Object.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:39981)
	at e.decorate (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2222:17437)
	at e._drawDecorations (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:16681)
	at e._updateSuggestions (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:16233)
	at e._handleSuggestionsChanged (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:14666)
	at /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:9525
	at /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2239:1028
	at Array.forEach (<anonymous>)
	at E4.dispatchSuggestionsChangedEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2239:1012)
	at E4.clear (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2239:2882)
	at hb.<anonymous> (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2210:88614)
	at hb.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:107:9018)
	at qd.dispose (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:1146)
	at e.dispose (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:1540)
	at e.disable (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:136718)
	at i (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144027)
	at s (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144106)
	at hb.<anonymous> (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144236)
	at hb.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:107:9018)
	at On (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:701)
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:15142
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:1011
	at Object.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:8:705)
	at cte.eb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:12069)
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10024
	at Array.map (<anonymous>)
	at cte.$ (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10012)
	at cte.terminate (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10255)
	at Zx.terminate (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:117:1434)
	at _s (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:5722)
	at MessagePortMain.<anonymous> (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:2262)
	at MessagePortMain.emit (node:events:518:28)
	at MessagePortMain.emit (node:domain:489:12)
	at MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949) setContext undefined
2025-06-14 00:30:34.750 [error] Canceled: Canceled
	at new t2 (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:113090)
	at qA.U (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:117701)
	at s.<computed>.r.charCodeAt.s.<computed> (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115195)
	at CI.g (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40984)
	at CI.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40488)
	at i.registerCommand.description (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:39818)
	at CI.h (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:41504)
	at CI.g (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40581)
	at CI.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40488)
	at Object.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:39981)
	at e.decorate (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2222:17310)
	at hb.<anonymous> (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:8877)
	at hb.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:107:9018)
	at qd.dispose (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:1146)
	at e.dispose (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:1540)
	at e.disable (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:136718)
	at i (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144027)
	at s (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144106)
	at hb.<anonymous> (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144236)
	at hb.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:107:9018)
	at On (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:701)
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:15142
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:1011
	at Object.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:8:705)
	at cte.eb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:12069)
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10024
	at Array.map (<anonymous>)
	at cte.$ (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10012)
	at cte.terminate (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10255)
	at Zx.terminate (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:117:1434)
	at _s (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:5722)
	at MessagePortMain.<anonymous> (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:2262)
	at MessagePortMain.emit (node:events:518:28)
	at MessagePortMain.emit (node:domain:489:12)
	at MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949) setContext undefined
2025-06-14 00:30:34.750 [error] Canceled: Canceled
	at new t2 (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:113090)
	at qA.U (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:117701)
	at s.<computed>.r.charCodeAt.s.<computed> (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115195)
	at CI.g (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40984)
	at CI.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40488)
	at i.registerCommand.description (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:39818)
	at CI.h (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:41504)
	at CI.g (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40581)
	at CI.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:40488)
	at Object.executeCommand (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:39981)
	at e.decorate (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2222:17437)
	at hb.<anonymous> (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2238:8877)
	at hb.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:107:9018)
	at qd.dispose (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:1146)
	at e.dispose (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:1540)
	at e.disable (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:136718)
	at i (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144027)
	at s (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144106)
	at hb.<anonymous> (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2288:144236)
	at hb.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:107:9018)
	at On (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:701)
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:15142
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:1011
	at Object.dispose (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:8:705)
	at cte.eb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:12069)
	at file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10024
	at Array.map (<anonymous>)
	at cte.$ (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10012)
	at cte.terminate (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:116:10255)
	at Zx.terminate (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:117:1434)
	at _s (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:5722)
	at MessagePortMain.<anonymous> (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:2262)
	at MessagePortMain.emit (node:events:518:28)
	at MessagePortMain.emit (node:domain:489:12)
	at MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949) setContext undefined
2025-06-14 00:30:34.750 [info] Extension host with pid 61485 exiting with code 0
