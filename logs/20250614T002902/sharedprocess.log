2025-06-14 00:29:04.391 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async Promise.all (index 2)
    at async da.r (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:40624)
    at async qu.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:33645)
    at async vh.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67437)
    at async vh.J (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:73847)
    at async vh.cleanUp (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:66866)
    at async wh.cleanUp (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:61234)
2025-06-14 00:29:04.394 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async Promise.all (index 2)
    at async da.r (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:40624)
    at async qu.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:33645)
    at async vh.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67437)
    at async U2.H (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:57403)
    at async U2.s (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:55298)
2025-06-14 00:29:04.467 [info] Marked extension as removed ms-dotnettools.vscode-dotnet-runtime-2.2.8
2025-06-14 00:29:04.467 [info] Marked extension as removed ms-python.vscode-pylance-2024.8.1
2025-06-14 00:29:04.467 [info] Deleted marked for removal extension from disk ms-dotnettools.vscode-dotnet-runtime /Users/<USER>/.windsurf/extensions/ms-dotnettools.vscode-dotnet-runtime-2.2.8
2025-06-14 00:29:04.467 [info] Deleted marked for removal extension from disk ms-python.vscode-pylance /Users/<USER>/.windsurf/extensions/ms-python.vscode-pylance-2024.8.1
2025-06-14 00:29:04.926 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async Promise.all (index 1)
    at async da.r (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:40624)
    at async qu.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:33645)
    at async vh.I (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:73378)
    at async vh.cleanUp (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:66881)
    at async wh.cleanUp (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:61234)
