[["6188955e-08ba-4721-9af6-b8a8a575f5fc", {"value": {"selectedCode": "", "prefix": "{\n    \"telemetry.sqmId\": \"AuxiliaryActivityBarPositionMenu\",\n    \"telemetry.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"telemetry.devDeviceId\": \"7583883f-b598-424d-b907-67f448c00f85\",\n    \"backupWorkspaces\": {\n        \"workspaces\": [],\n        \"folders\": [\n            {\n                \"folderUri\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\"\n            }\n        ],\n        \"emptyWindows\": []\n    },\n    \"windowControlHeight\": 35,\n    \"profileAssociations\": {\n        \"workspaces\": {\n            \"file:///Users/<USER>/Library/Application%20Support/Windsurf\": \"__default__profile__\"\n        },\n        \"emptyWindows\": {}\n    },\n    \"lastKnownMenubarData\": {\n        \"menus\": {\n            \"File\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.files.newUntitledFile\",\n                        \"label\": \"&&New Text File\"\n                    },\n                    {\n                        \"id\": \"welcome.showNewFileEntries\",\n                        \"label\": \"New File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.newWindow\",\n                        \"label\": \"New &&Window\"\n                    },\n                    {\n                        \"id\": \"submenuitem.OpenProfile\",\n                        \"label\": \"New Window with Profile\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.createProfile\",\n                                    \"label\": \"New Profile...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFileFolder\",\n                        \"label\": \"&&Open...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFolder\",\n                        \"label\": \"Open &&Folder...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWorkspace\",\n                        \"label\": \"Open Wor&&kspace from File...\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarRecentMenu\",\n                        \"label\": \"Open &&Recent\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.reopenClosedEditor\",\n                                    \"label\": \"&&Reopen Closed Editor\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"openRecentFolder\",\n                                    \"uri\": {\n                                        \"$mid\": 1,\n                                        \"path\": \"/Users/<USER>/Library/Application Support/Windsurf\",\n                                        \"scheme\": \"file\"\n                                    },\n                                    \"enabled\": true,\n                                    \"label\": \"~/Library/Application Support/Windsurf\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openRecent\",\n                                    \"label\": \"&&More...\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.clearRecentFiles\",\n                                    \"label\": \"&&Clear Recently Opened...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"addRootFolder\",\n                        \"label\": \"A&&dd Folder to Workspace...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.saveWorkspaceAs\",\n                        \"label\": \"Save Workspace As...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.duplicateWorkspaceInNewWindow\",\n                        \"label\": \"Duplicate Workspace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.save\",\n                        \"label\": \"&&Save\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.saveAs\",\n                        \"label\": \"Save &&As...\"\n                    },\n                    {\n                        \"id\": \"saveAll\",\n                        \"label\": \"Save A&&ll\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarShare\",\n                        \"label\": \"Share\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.exportProfile\",\n                                    \"label\": \"Export Profile (Default)...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleAutoSave\",\n                        \"label\": \"A&&uto Save\",\n                        \"checked\": true\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.revert\",\n                        \"label\": \"Re&&vert File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeActiveEditor\",\n                        \"label\": \"&&Close Editor\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeFolder\",\n                        \"label\": \"Close &&Folder\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeWindow\",\n                        \"label\": \"Clos&&e Window\"\n                    }\n                ]\n            },\n            \"Edit\": {\n                \"items\": [\n                    {\n                        \"id\": \"undo\",\n                        \"label\": \"&&Undo\"\n                    },\n                    {\n                        \"id\": \"redo\",\n                        \"label\": \"&&Redo\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCutAction\",\n                        \"label\": \"Cu&&t\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCopyAction\",\n                        \"label\": \"&&Copy\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardPasteAction\",\n                        \"label\": \"&&Paste\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"actions.find\",\n                        \"label\": \"&&Find\"\n                    },\n                    {\n                        \"id\": \"editor.action.startFindReplaceAction\",\n                        \"label\": \"&&Replace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.findInFiles\",\n                        \"label\": \"Find &&in Files\"\n                    },\n                    {\n                        \"id\": \"workbench.action.replaceInFiles\",\n                        \"label\": \"Replace in Files\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.commentLine\",\n                        \"label\": \"&&Toggle Line Comment\"\n                    },\n                    {\n                        \"id\": \"editor.action.blockComment\",\n                        \"label\": \"Toggle &&Block Comment\"\n                    },\n                    {\n                        \"id\": \"editor.emmet.action.expandAbbreviation\",\n                        \"label\": \"Emmet: E&&xpand Abbreviation\"\n                    }\n                ]\n            },\n            \"Selection\": {\n                \"items\": [\n                    {\n                        \"id\": \"editor.action.selectAll\",\n                        \"label\": \"&&Select All\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.expand\",\n                        \"label\": \"&&Expand Selection\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.shrink\",\n                        \"label\": \"&&Shrink Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesUpAction\",\n                        \"label\": \"&&Copy Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesDownAction\",\n                        \"label\": \"Co&&py Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesUpAction\",\n                        \"label\": \"Mo&&ve Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesDownAction\",\n                        \"label\": \"Move &&Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.duplicateSelection\",\n                        \"label\": \"&&Duplicate Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAbove\",\n                        \"label\": \"&&Add Cursor Above\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorBelow\",\n                        \"label\": \"A&&dd Cursor Below\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAtEndOfEachLineSelected\",\n                        \"label\": \"Add C&&ursors to Line Ends\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToNextFindMatch\",\n                        \"label\": \"Add &&Next Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToPreviousFindMatch\",\n                        \"label\": \"Add P&&revious Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.selectHighlights\",\n                        \"label\": \"Select All &&Occurrences\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleMultiCursorModifier\",\n                        \"label\": \"Switch to Cmd+Click for Multi-Cursor\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleColumnSelection\",\n                        \"label\": \"Column &&Selection Mode\"\n                    }\n                ]\n            },\n            \"View\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"&&Command Palette...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openView\",\n                        \"label\": \"&&Open View...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarAppearanceMenu\",\n                        \"label\": \"&&Appearance\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.toggleFullScreen\",\n                                    \"label\": \"&&Full Screen\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleZenMode\",\n                                    \"label\": \"Zen Mode\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleCenteredLayout\",\n                                    \"label\": \"&&Centered Layout\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarVisibility\",\n                                    \"label\": \"&&Primary Side Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleAuxiliaryBar\",\n                                    \"label\": \"Secondary Si&&de Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleStatusbarVisibility\",\n                                    \"label\": \"S&&tatus Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.togglePanel\",\n                                    \"label\": \"&&Panel\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarPosition\",\n                                    \"label\": \"&&Move Primary Side Bar Right\"\n                                },\n                                {\n                                    \"id\": \"submenuitem.ActivityBarPositionMenu\",\n                                    \"label\": \"Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.default\",\n                                                \"label\": \"&&Default\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.AuxiliaryActivityBarPositionMenu\",\n                                    \"label\": \"Secondary Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.default\",\n                                                \"label\": \"&&Default\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelPositionMenu\",\n                                    \"label\": \"Panel Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.positionPanelTop\",\n                                                \"label\": \"Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelRight\",\n                                                \"label\": \"Right\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelBottom\",\n                                                \"label\": \"Bottom\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelAlignmentMenu\",\n                                    \"label\": \"Align Panel\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.alignPanelCenter\",\n                                                \"label\": \"Center\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelJustify\",\n                                                \"label\": \"Justify\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelRight\",\n                                                \"label\": \"Right\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorTabsBarShowTabsSubmenu\",\n                                    \"label\": \"Tab Bar\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.showMultipleEditorTabs\",\n                                                \"label\": \"Multiple Tabs\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.showEditorTab\",\n                                                \"label\": \"Single Tab\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorTabs\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorActionsPositionSubmenu\",\n                                    \"label\": \"Editor Actions Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.editorActionsDefault\",\n                                                \"label\": \"Tab Bar\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.editorActionsTitleBar\",\n                                                \"label\": \"Title Bar\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorActions\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleMinimap\",\n                                    \"label\": \"&&Minimap\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"breadcrumbs.toggle\",\n                                    \"label\": \"Toggle &&Breadcrumbs\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleStickyScroll\",\n                                    \"label\": \"&&Sticky Scroll\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderWhitespace\",\n                                    \"label\": \"&&Render Whitespace\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderControlCharacter\",\n                                    \"label\": \"Render &&Control Characters\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomIn\",\n                                    \"label\": \"&&Zoom In\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomOut\",\n                                    \"label\": \"&&Zoom Out\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomReset\",\n                                    \"label\": \"&&Reset Zoom\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarLayoutMenu\",\n                        \"label\": \"Editor &&Layout\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.splitEditorUp\",\n                                    \"label\": \"Split &&Up\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorDown\",\n                                    \"label\": \"Split &&Down\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorLeft\",\n                                    \"label\": \"Split &&Left\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorRight\",\n                                    \"label\": \"Split &&Right\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorInGroup\",\n                                    \"label\": \"Split in &&Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.moveEditorToNewWindow\",\n                                    \"label\": \"&&Move Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.copyEditorToNewWindow\",\n                                    \"label\": \"&&Copy Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutSingle\",\n                                    \"label\": \"&&Single\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumns\",\n                                    \"label\": \"&&Two Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeColumns\",\n                                    \"label\": \"T&&hree Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRows\",\n                                    \"label\": \"T&&wo Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeRows\",\n                                    \"label\": \"Three &&Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoByTwoGrid\",\n                                    \"label\": \"&&Grid (2x2)\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRowsRight\",\n                                    \"label\": \"Two R&&ows Right\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumnsBottom\",\n                                    \"label\": \"Two &&Columns Bottom\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleEditorGroupLayout\",\n                                    \"label\": \"Flip &&Layout\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.view.explorer\",\n                        \"label\": \"&&Explorer\"\n                    },\n                    {\n                        \"id\": \"workbench.view.search\",\n                        \"label\": \"&&Search\"\n                    },\n                    {\n                        \"id\": \"workbench.view.scm\",\n                        \"label\": \"Source &&Control\"\n                    },\n                    {\n                        \"id\": \"workbench.view.debug\",\n                        \"label\": \"&&Run\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"E&&xtensions\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.actions.view.problems\",\n                        \"label\": \"&&Problems\"\n                    },\n                    {\n                        \"id\": \"workbench.action.output.toggleOutput\",\n                        \"label\": \"&&Output\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.action.toggleRepl\",\n                        \"label\": \"De&&bug Console\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.toggleTerminal\",\n                        \"label\": \"&&Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleWordWrap\",\n                        \"label\": \"&&Word Wrap\",\n                        \"checked\": true\n                    }\n                ]\n            },\n            \"Go\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.navigateBack\",\n                        \"label\": \"&&Back\"\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateForward\",\n                        \"label\": \"&&Forward\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateToLastEditLocation\",\n                        \"label\": \"&&Last Edit Location\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchEditorMenu\",\n                        \"label\": \"Switch &&Editor\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.nextEditor\",\n                                    \"label\": \"&&Next Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditor\",\n                                    \"label\": \"&&Previous Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditor\",\n                                    \"label\": \"&&Next Used Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditor\",\n                                    \"label\": \"&&Previous Used Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.nextEditorInGroup\",\n                                    \"label\": \"&&Next Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditorInGroup\",\n                                    \"label\": \"&&Previous Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Next Used Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Previous Used Editor in Group\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchGroupMenu\",\n                        \"label\": \"Switch &&Group\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.focusFirstEditorGroup\",\n                                    \"label\": \"Group &&1\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusSecondEditorGroup\",\n                                    \"label\": \"Group &&2\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusThirdEditorGroup\",\n                                    \"label\": \"Group &&3\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFourthEditorGroup\",\n                                    \"label\": \"Group &&4\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFifthEditorGroup\",\n                                    \"label\": \"Group &&5\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusNextGroup\",\n                                    \"label\": \"&&Next Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusPreviousGroup\",\n                                    \"label\": \"&&Previous Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusLeftGroup\",\n                                    \"label\": \"Group &&Left\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusRightGroup\",\n                                    \"label\": \"Group &&Right\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusAboveGroup\",\n                                    \"label\": \"Group &&Above\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusBelowGroup\",\n                                    \"label\": \"Group &&Below\",\n                                    \"enabled\": false\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.quickOpen\",\n                        \"label\": \"Go to &&File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showAllSymbols\",\n                        \"label\": \"Go to Symbol in &&Workspace...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoSymbol\",\n                        \"label\": \"Go to &&Symbol in Editor...\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDefinition\",\n                        \"label\": \"Go to &&Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDeclaration\",\n                        \"label\": \"Go to &&Declaration\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToTypeDefinition\",\n                        \"label\": \"Go to &&Type Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToImplementation\",\n                        \"label\": \"Go to &&Implementations\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToReferences\",\n                        \"label\": \"Go to &&References\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoLine\",\n                        \"label\": \"Go to &&Line/Column...\"\n                    },\n                    {\n                        \"id\": \"editor.action.jumpToBracket\",\n                        \"label\": \"Go to &&Bracket\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.nextInFiles\",\n                        \"label\": \"Next &&Problem\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.prevInFiles\",\n                        \"label\": \"Previous &&Problem\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.next\",\n                        \"label\": \"Next &&Change\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.previous\",\n                        \"label\": \"Previous &&Change\"\n                    }\n                ]\n            },\n            \"Run\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.debug.start\",\n                        \"label\": \"&&Start Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.run\",\n                        \"label\": \"Run &&Without Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stop\",\n                        \"label\": \"&&Stop Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.restart\",\n                        \"label\": \"&&Restart Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.configure\",\n                        \"label\": \"Open &&Configurations\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"debug.addConfiguration\",\n                        \"label\": \"A&&dd Configuration...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOver\",\n                        \"label\": \"Step &&Over\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepInto\",\n                        \"label\": \"Step &&Into\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOut\",\n                        \"label\": \"Step O&&ut\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.continue\",\n                        \"label\": \"&&Continue\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.debug.action.toggleBreakpoint\",\n                        \"label\": \"Toggle &&Breakpoint\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarNewBreakpointMenu\",\n                        \"label\": \"&&New Breakpoint\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"editor.debug.action.conditionalBreakpoint\",\n                                    \"label\": \"&&Conditional Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.editBreakpoint\",\n                                    \"label\": \"&&Edit Breakpoint\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.toggleInlineBreakpoint\",\n                                    \"label\": \"Inline Breakp&&oint\"\n                                },\n                                {\n                                    \"id\": \"workbench.debug.viewlet.action.addFunctionBreakpointAction\",\n                                    \"label\": \"&&Function Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.addLogPoint\",\n                                    \"label\": \"&&Logpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.triggerByBreakpoint\",\n                                    \"label\": \"&&Triggered Breakpoint...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.enableAllBreakpoints\",\n                        \"label\": \"&&Enable All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.disableAllBreakpoints\",\n                        \"label\": \"Disable A&&ll Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.removeAllBreakpoints\",\n                        \"label\": \"Remove &&All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"debug.installAdditionalDebuggers\",\n                        \"label\": \"&&Install Additional Debuggers...\"\n                    }\n                ]\n            },\n            \"Terminal\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.terminal.new\",\n                        \"label\": \"&&New Terminal\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.split\",\n                        \"label\": \"&&Split Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.runTask\",\n                        \"label\": \"&&Run Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.build\",\n                        \"label\": \"Run &&Build Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runActiveFile\",\n                        \"label\": \"Run &&Active File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runSelectedText\",\n                        \"label\": \"Run &&Selected Text\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.showTasks\",\n                        \"label\": \"Show Runnin&&g Tasks...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.restartTask\",\n                        \"label\": \"R&&estart Running Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.terminate\",\n                        \"label\": \"&&Terminate Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureTaskRunner\",\n                        \"label\": \"&&Configure Tasks...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureDefaultBuildTask\",\n                        \"label\": \"Configure De&&fault Build Task...\"\n                    }\n                ]\n            },\n            \"Help\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"Show All Commands\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showInteractivePlayground\",\n                        \"label\": \"Editor Playgrou&&nd\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openLicenseUrl\",\n                        \"label\": \"View &&License\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleDevTools\",\n                        \"label\": \"Toggle Developer Tools\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openProcessExplorer\",\n                        \"label\": \"Open &&Process Explorer\"\n                    }\n                ]\n            },\n            \"Preferences\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.openSettings\",\n                        \"label\": \"&&Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWindsurfSettings\",\n                        \"label\": \"Windsurf Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"&&Extensions\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openGlobalKeybindings\",\n                        \"label\": \"Keyboard Shortcuts\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openSnippets\",\n                        \"label\": \"Configure Snippets\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.openUserTasks\",\n                        \"label\": \"Tasks\"\n                    },\n                    {\n                        \"id\": \"submenuitem.ThemesSubMenu\",\n                        \"label\": \"&&Themes\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.selectTheme\",\n                                    \"label\": \"Color Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectIconTheme\",\n                                    \"label\": \"File Icon Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectProductIconTheme\",\n                                    \"label\": \"Product Icon Theme\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"settings.filterByOnline\",\n                        \"label\": \"&&Online Services Settings\"\n                    }\n                ]\n            }\n        },\n        \"keybindings\": {\n            \"workbench.action.quit\": {\n                \"label\": \"Cmd+Q\",\n                \"userSettingsLabel\": \"cmd+q\"\n            },\n            \"workbench.action.files.newUntitledFile\": {\n                \"label\": \"Cmd+N\",\n                \"userSettingsLabel\": \"cmd+n\"\n            },\n            \"welcome.showNewFileEntries\": {\n                \"label\": \"Ctrl+Alt+Cmd+N\",\n                \"userSettingsLabel\": \"ctrl+alt+cmd+n\"\n            },\n            \"workbench.action.newWindow\": {\n                \"label\": \"Shift+Cmd+N\",\n                \"userSettingsLabel\": \"shift+cmd+n\"\n            },\n            \"workbench.action.files.openFileFolder\": {\n                \"label\": \"Cmd+O\",\n                \"userSettingsLabel\": \"cmd+o\"\n            },\n            \"workbench.action.reopenClosedEditor\": {\n                \"label\": \"Shift+Cmd+T\",\n                \"userSettingsLabel\": \"shift+cmd+t\"\n            },\n            \"workbench.action.openRecent\": {\n                \"label\": \"Ctrl+R\",\n                \"userSettingsLabel\": \"ctrl+r\"\n            },\n            \"workbench.action.files.save\": {\n                \"label\": \"Cmd+S\",\n                \"userSettingsLabel\": \"cmd+s\"\n            },\n            \"workbench.action.files.saveAs\": {\n                \"label\": \"Shift+Cmd+S\",\n                \"userSettingsLabel\": \"shift+cmd+s\"\n            },\n            \"saveAll\": {\n                \"label\": \"Alt+Cmd+S\",\n                \"userSettingsLabel\": \"alt+cmd+s\"\n            },\n            \"workbench.action.closeActiveEditor\": {\n                \"label\": \"Cmd+W\",\n                \"userSettingsLabel\": \"cmd+w\"\n            },\n            \"workbench.action.closeWindow\": {\n                \"label\": \"Shift+Cmd+W\",\n                \"userSettingsLabel\": \"shift+cmd+w\"\n            },\n            \"undo\": {\n                \"label\": \"Cmd+Z\",\n                \"userSettingsLabel\": \"cmd+z\"\n            },\n            \"redo\": {\n                \"label\": \"Shift+Cmd+Z\",\n                \"userSettingsLabel\": \"shift+cmd+z\"\n            },\n            \"editor.action.clipboardCutAction\": {\n                \"label\": \"Cmd+X\",\n                \"userSettingsLabel\": \"cmd+x\"\n            },\n            \"editor.action.clipboardCopyAction\": {\n                \"label\": \"Cmd+C\",\n                \"userSettingsLabel\": \"cmd+c\"\n            },\n            \"editor.action.clipboardPasteAction\": {\n                \"label\": \"Cmd+V\",\n                \"userSettingsLabel\": \"cmd+v\"\n            },\n            \"actions.find\": {\n                \"label\": \"Cmd+F\",\n                \"userSettingsLabel\": \"cmd+f\"\n            },\n            \"editor.action.startFindReplaceAction\": {\n                \"label\": \"Alt+Cmd+F\",\n                \"userSettingsLabel\": \"alt+cmd+f\"\n            },\n            \"workbench.action.findInFiles\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.action.replaceInFiles\": {\n                \"label\": \"Shift+Cmd+H\",\n                \"userSettingsLabel\": \"shift+cmd+h\"\n            },\n            \"editor.action.commentLine\": {\n                \"label\": \"Cmd+/\",\n                \"userSettingsLabel\": \"cmd+/\"\n            },\n            \"editor.action.blockComment\": {\n                \"label\": \"Shift+Alt+A\",\n                \"userSettingsLabel\": \"shift+alt+a\"\n            },\n            \"editor.emmet.action.expandAbbreviation\": {\n                \"label\": \"Tab\",\n                \"userSettingsLabel\": \"tab\"\n            },\n            \"editor.action.selectAll\": {\n                \"label\": \"Cmd+A\",\n                \"userSettingsLabel\": \"cmd+a\"\n            },\n            \"editor.action.smartSelect.expand\": {\n                \"label\": \"Ctrl+Shift+Cmd+Right\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+right\"\n            },\n            \"editor.action.smartSelect.shrink\": {\n                \"label\": \"Ctrl+Shift+Cmd+Left\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+left\"\n            },\n            \"editor.action.copyLinesUpAction\": {\n                \"label\": \"Shift+Alt+Up\",\n                \"userSettingsLabel\": \"shift+alt+up\"\n            },\n            \"editor.action.copyLinesDownAction\": {\n                \"label\": \"Shift+Alt+Down\",\n                \"userSettingsLabel\": \"shift+alt+down\"\n            },\n            \"editor.action.moveLinesUpAction\": {\n                \"label\": \"Alt+Up\",\n                \"userSettingsLabel\": \"alt+up\"\n            },\n            \"editor.action.moveLinesDownAction\": {\n                \"label\": \"Alt+Down\",\n                \"userSettingsLabel\": \"alt+down\"\n            },\n            \"editor.action.insertCursorAbove\": {\n                \"label\": \"Alt+Cmd+Up\",\n                \"userSettingsLabel\": \"alt+cmd+up\"\n            },\n            \"editor.action.insertCursorBelow\": {\n                \"label\": \"Alt+Cmd+Down\",\n                \"userSettingsLabel\": \"alt+cmd+down\"\n            },\n            \"editor.action.insertCursorAtEndOfEachLineSelected\": {\n                \"label\": \"Shift+Alt+I\",\n                \"userSettingsLabel\": \"shift+alt+i\"\n            },\n            \"editor.action.addSelectionToNextFindMatch\": {\n                \"label\": \"Cmd+D\",\n                \"userSettingsLabel\": \"cmd+d\"\n            },\n            \"editor.action.selectHighlights\": {\n                \"label\": \"Shift+Cmd+L\",\n                \"userSettingsLabel\": \"shift+cmd+l\"\n            },\n            \"workbench.action.showCommands\": {\n                \"label\": \"Shift+Cmd+P\",\n                \"userSettingsLabel\": \"shift+cmd+p\"\n            },\n            \"workbench.action.toggleFullScreen\": {\n                \"label\": \"Ctrl+Cmd+F\",\n                \"userSettingsLabel\": \"ctrl+cmd+f\"\n            },\n            \"workbench.action.toggleSidebarVisibility\": {\n                \"label\": \"Cmd+B\",\n                \"userSettingsLabel\": \"cmd+b\"\n            },\n            \"workbench.action.toggleAuxiliaryBar\": {\n                \"label\": \"Alt+Cmd+B\",\n                \"userSettingsLabel\": \"alt+cmd+b\"\n            },\n            \"workbench.action.togglePanel\": {\n                \"label\": \"Cmd+J\",\n                \"userSettingsLabel\": \"cmd+j\"\n            },\n            \"workbench.action.zoomIn\": {\n                \"label\": \"Cmd+=\",\n                \"userSettingsLabel\": \"cmd+=\"\n            },\n            \"workbench.action.zoomOut\": {\n                \"label\": \"Cmd+-\",\n                \"userSettingsLabel\": \"cmd+-\"\n            },\n            \"workbench.action.zoomReset\": {\n                \"label\": \"⌘NumPad0\",\n                \"isNative\": false,\n                \"userSettingsLabel\": \"cmd+numpad0\"\n            },\n            \"workbench.action.toggleEditorGroupLayout\": {\n                \"label\": \"Alt+Cmd+0\",\n                \"userSettingsLabel\": \"alt+cmd+0\"\n            },\n            \"workbench.view.explorer\": {\n                \"label\": \"Shift+Cmd+E\",\n                \"userSettingsLabel\": \"shift+cmd+e\"\n            },\n            \"workbench.view.search\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.view.scm\": {\n                \"label\": \"Ctrl+Shift+G\",\n                \"userSettingsLabel\": \"ctrl+shift+g\"\n            },\n            \"workbench.view.debug\": {\n                \"label\": \"Shift+Cmd+D\",\n                \"userSettingsLabel\": \"shift+cmd+d\"\n            },\n            \"workbench.view.extensions\": {\n                \"label\": \"Shift+Cmd+X\",\n                \"userSettingsLabel\": \"shift+cmd+x\"\n            },\n            \"workbench.actions.view.problems\": {\n                \"label\": \"Shift+Cmd+M\",\n                \"userSettingsLabel\": \"shift+cmd+m\"\n            },\n            \"workbench.action.output.toggleOutput\": {\n                \"label\": \"Shift+Cmd+U\",\n                \"userSettingsLabel\": \"shift+cmd+u\"\n            },\n            \"workbench.debug.action.toggleRepl\": {\n                \"label\": \"Shift+Cmd+Y\",\n                \"userSettingsLabel\": \"shift+cmd+y\"\n            },\n            \"workbench.action.terminal.toggleTerminal\": {\n                \"label\": \"Ctrl+`\",\n                \"userSettingsLabel\": \"ctrl+`\"\n            },\n            \"editor.action.toggleWordWrap\": {\n                \"label\": \"Alt+Z\",\n                \"userSettingsLabel\": \"alt+z\"\n            },\n            \"workbench.action.navigateBack\": {\n                \"label\": \"Ctrl+-\",\n                \"userSettingsLabel\": \"ctrl+-\"\n            },\n", "suffix": "            \"workbench.action.navigateForward\": {\n                \"label\": \"Ctrl+Shift+-\",\n                \"userSettingsLabel\": \"ctrl+shift+-\"\n            },\n            \"workbench.action.nextEditor\": {\n                \"label\": \"Alt+Cmd+Right\",\n                \"userSettingsLabel\": \"alt+cmd+right\"\n            },\n            \"workbench.action.previousEditor\": {\n                \"label\": \"Alt+Cmd+Left\",\n                \"userSettingsLabel\": \"alt+cmd+left\"\n            },\n            \"workbench.action.focusFirstEditorGroup\": {\n                \"label\": \"Cmd+1\",\n                \"userSettingsLabel\": \"cmd+1\"\n            },\n            \"workbench.action.focusSecondEditorGroup\": {\n                \"label\": \"Cmd+2\",\n                \"userSettingsLabel\": \"cmd+2\"\n            },\n            \"workbench.action.focusThirdEditorGroup\": {\n                \"label\": \"Cmd+3\",\n                \"userSettingsLabel\": \"cmd+3\"\n            },\n            \"workbench.action.focusFourthEditorGroup\": {\n                \"label\": \"Cmd+4\",\n                \"userSettingsLabel\": \"cmd+4\"\n            },\n            \"workbench.action.focusFifthEditorGroup\": {\n                \"label\": \"Cmd+5\",\n                \"userSettingsLabel\": \"cmd+5\"\n            },\n            \"workbench.action.quickOpen\": {\n                \"label\": \"Cmd+P\",\n                \"userSettingsLabel\": \"cmd+p\"\n            },\n            \"workbench.action.showAllSymbols\": {\n                \"label\": \"Cmd+T\",\n                \"userSettingsLabel\": \"cmd+t\"\n            },\n            \"workbench.action.gotoSymbol\": {\n                \"label\": \"Shift+Cmd+O\",\n                \"userSettingsLabel\": \"shift+cmd+o\"\n            },\n            \"editor.action.revealDefinition\": {\n                \"label\": \"F12\",\n                \"userSettingsLabel\": \"f12\"\n            },\n            \"editor.action.goToImplementation\": {\n                \"label\": \"Cmd+F12\",\n                \"userSettingsLabel\": \"cmd+f12\"\n            },\n            \"editor.action.goToReferences\": {\n                \"label\": \"Shift+F12\",\n                \"userSettingsLabel\": \"shift+f12\"\n            },\n            \"workbench.action.gotoLine\": {\n                \"label\": \"Ctrl+G\",\n                \"userSettingsLabel\": \"ctrl+g\"\n            },\n            \"editor.action.jumpToBracket\": {\n                \"label\": \"Shift+Cmd+\\\\\",\n                \"userSettingsLabel\": \"shift+cmd+\\\\\"\n            },\n            \"editor.action.marker.nextInFiles\": {\n                \"label\": \"F8\",\n                \"userSettingsLabel\": \"f8\"\n            },\n            \"editor.action.marker.prevInFiles\": {\n                \"label\": \"Shift+F8\",\n                \"userSettingsLabel\": \"shift+f8\"\n            },\n            \"editor.action.dirtydiff.next\": {\n                \"label\": \"Alt+F3\",\n                \"userSettingsLabel\": \"alt+f3\"\n            },\n            \"editor.action.dirtydiff.previous\": {\n                \"label\": \"Shift+Alt+F3\",\n                \"userSettingsLabel\": \"shift+alt+f3\"\n            },\n            \"workbench.action.debug.start\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"workbench.action.debug.run\": {\n                \"label\": \"Ctrl+F5\",\n                \"userSettingsLabel\": \"ctrl+f5\"\n            },\n            \"workbench.action.debug.stop\": {\n                \"label\": \"Shift+F5\",\n                \"userSettingsLabel\": \"shift+f5\"\n            },\n            \"workbench.action.debug.restart\": {\n                \"label\": \"Shift+Cmd+F5\",\n                \"userSettingsLabel\": \"shift+cmd+f5\"\n            },\n            \"workbench.action.debug.stepOver\": {\n                \"label\": \"F10\",\n                \"userSettingsLabel\": \"f10\"\n            },\n            \"workbench.action.debug.stepInto\": {\n                \"label\": \"F11\",\n                \"userSettingsLabel\": \"f11\"\n            },\n            \"workbench.action.debug.stepOut\": {\n                \"label\": \"Shift+F11\",\n                \"userSettingsLabel\": \"shift+f11\"\n            },\n            \"workbench.action.debug.continue\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"editor.debug.action.toggleBreakpoint\": {\n                \"label\": \"F9\",\n                \"userSettingsLabel\": \"f9\"\n            },\n            \"editor.debug.action.toggleInlineBreakpoint\": {\n                \"label\": \"Shift+F9\",\n                \"userSettingsLabel\": \"shift+f9\"\n            },\n            \"workbench.action.terminal.new\": {\n                \"label\": \"Ctrl+Shift+`\",\n                \"userSettingsLabel\": \"ctrl+shift+`\"\n            },\n            \"workbench.action.terminal.split\": {\n                \"label\": \"Cmd+\\\\\",\n                \"userSettingsLabel\": \"cmd+\\\\\"\n            },\n            \"workbench.action.tasks.build\": {\n                \"label\": \"Shift+Cmd+B\",\n                \"userSettingsLabel\": \"shift+cmd+b\"\n            },\n            \"workbench.action.openWindsurfSettings\": {\n                \"label\": \"Cmd+,\",\n                \"userSettingsLabel\": \"cmd+,\"\n            }\n        }\n    },\n    \"theme\": \"vs-dark\",\n    \"themeBackground\": \"#1f1f1f\",\n    \"windowSplash\": {\n        \"zoomLevel\": 1,\n        \"baseTheme\": \"vs-dark\",\n        \"colorInfo\": {\n            \"foreground\": \"#cccccc\",\n            \"background\": \"#1f1f1f\",\n            \"editorBackground\": \"#1f1f1f\",\n            \"titleBarBackground\": \"#181818\",\n            \"titleBarBorder\": \"#2b2b2b\",\n            \"activityBarBackground\": \"#181818\",\n            \"activityBarBorder\": \"#2b2b2b\",\n            \"sideBarBackground\": \"#181818\",\n            \"sideBarBorder\": \"#2b2b2b\",\n            \"statusBarBackground\": \"#181818\",\n            \"statusBarBorder\": \"#2b2b2b\",\n            \"statusBarNoFolderBackground\": \"#1f1f1f\"\n        },\n        \"layoutInfo\": {\n            \"sideBarSide\": \"left\",\n            \"editorPartMinWidth\": 220,\n            \"titleBarHeight\": 35,\n            \"activityBarWidth\": 42,\n            \"sideBarWidth\": 213,\n            \"auxiliarySideBarWidth\": 523,\n            \"statusBarHeight\": 22,\n            \"windowBorder\": false\n        }\n    },\n    \"windowsState\": {\n        \"lastActiveWindow\": {\n            \"folder\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\",\n            \"backupPath\": \"/Users/<USER>/Library/Application Support/Windsurf/Backups/a159014097c8119daed6fdd2d20bad90\",\n            \"uiState\": {\n                \"mode\": 1,\n                \"x\": 33,\n                \"y\": 25,\n                \"width\": 1407,\n                \"height\": 875,\n                \"zoomLevel\": 0\n            }\n        },\n        \"openedWindows\": []\n    },\n    \"windowSplashWorkspaceOverride\": {\n        \"layoutInfo\": {\n            \"auxiliarySideBarWidth\": [\n                523,\n                [\n                    \"c59be2f483dcdc78296c3a4a55c688ec\"\n                ]\n            ]\n        }\n    },\n    \"telemetry.sessionId\": \"2034D9B9-B912-42D0-B6E7-393388AEF72A\",\n    \"telemetry.instanceId\": \"63372F8A-6B5E-45AF-BB7C-E786045FBDDB\",\n    \"telemetry.sqmUserId\": \"a137b0725136a02065862bd942e721af\",\n    \"telemetry.installationId\": \"FE358A14-E67F-4A20-B61F-42151D94EAAC\",\n    \"telemetry.firstSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"telemetry.lastSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"user.globalId\": \"E5D9C812-6AF1-41AE-8197-65B98780FCD6\",\n    \"installation.time\": \"2025-06-13T14:55:49.3NZ\",\n    \"sync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"settingsSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"userDataSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"marketplace.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"gallery.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"remote.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"tunnels.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\"\n}", "path": "User/globalStorage/storage.json", "language": "json", "prefixBegin": 0, "suffixEnd": 1}}], ["d6c0ff07-7c59-4ff0-97f2-b0f8ff99c280", {"value": {"selectedCode": "", "prefix": "{\n    \"telemetry.sqmId\": \"AuxiliaryActivityBarPositionMenu\",\n    \"telemetry.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"telemetry.devDeviceId\": \"7583883f-b598-424d-b907-67f448c00f85\",\n    \"backupWorkspaces\": {\n        \"workspaces\": [],\n        \"folders\": [\n            {\n                \"folderUri\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\"\n            }\n        ],\n        \"emptyWindows\": []\n    },\n    \"windowControlHeight\": 35,\n    \"profileAssociations\": {\n        \"workspaces\": {\n            \"file:///Users/<USER>/Library/Application%20Support/Windsurf\": \"__default__profile__\"\n        },\n        \"emptyWindows\": {}\n    },\n    \"lastKnownMenubarData\": {\n        \"menus\": {\n            \"File\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.files.newUntitledFile\",\n                        \"label\": \"&&New Text File\"\n                    },\n                    {\n                        \"id\": \"welcome.showNewFileEntries\",\n                        \"label\": \"New File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.newWindow\",\n                        \"label\": \"New &&Window\"\n                    },\n                    {\n                        \"id\": \"submenuitem.OpenProfile\",\n                        \"label\": \"New Window with Profile\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.createProfile\",\n                                    \"label\": \"New Profile...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFileFolder\",\n                        \"label\": \"&&Open...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFolder\",\n                        \"label\": \"Open &&Folder...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWorkspace\",\n                        \"label\": \"Open Wor&&kspace from File...\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarRecentMenu\",\n                        \"label\": \"Open &&Recent\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.reopenClosedEditor\",\n                                    \"label\": \"&&Reopen Closed Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"openRecentFolder\",\n                                    \"uri\": {\n                                        \"$mid\": 1,\n                                        \"path\": \"/Users/<USER>/Library/Application Support/Windsurf\",\n                                        \"scheme\": \"file\"\n                                    },\n                                    \"enabled\": true,\n                                    \"label\": \"~/Library/Application Support/Windsurf\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openRecent\",\n                                    \"label\": \"&&More...\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.clearRecentFiles\",\n                                    \"label\": \"&&Clear Recently Opened...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"addRootFolder\",\n                        \"label\": \"A&&dd Folder to Workspace...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.saveWorkspaceAs\",\n                        \"label\": \"Save Workspace As...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.duplicateWorkspaceInNewWindow\",\n                        \"label\": \"Duplicate Workspace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.save\",\n                        \"label\": \"&&Save\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.saveAs\",\n                        \"label\": \"Save &&As...\"\n                    },\n                    {\n                        \"id\": \"saveAll\",\n                        \"label\": \"Save A&&ll\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarShare\",\n                        \"label\": \"Share\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.exportProfile\",\n                                    \"label\": \"Export Profile (Default)...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleAutoSave\",\n                        \"label\": \"A&&uto Save\",\n                        \"checked\": true\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.revert\",\n                        \"label\": \"Re&&vert File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeActiveEditor\",\n                        \"label\": \"&&Close Editor\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeFolder\",\n                        \"label\": \"Close &&Folder\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeWindow\",\n                        \"label\": \"Clos&&e Window\"\n                    }\n                ]\n            },\n            \"Edit\": {\n                \"items\": [\n                    {\n                        \"id\": \"undo\",\n                        \"label\": \"&&Undo\"\n                    },\n                    {\n                        \"id\": \"redo\",\n                        \"label\": \"&&Redo\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCutAction\",\n                        \"label\": \"Cu&&t\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCopyAction\",\n                        \"label\": \"&&Copy\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardPasteAction\",\n                        \"label\": \"&&Paste\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"actions.find\",\n                        \"label\": \"&&Find\"\n                    },\n                    {\n                        \"id\": \"editor.action.startFindReplaceAction\",\n                        \"label\": \"&&Replace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.findInFiles\",\n                        \"label\": \"Find &&in Files\"\n                    },\n                    {\n                        \"id\": \"workbench.action.replaceInFiles\",\n                        \"label\": \"Replace in Files\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.commentLine\",\n                        \"label\": \"&&Toggle Line Comment\"\n                    },\n                    {\n                        \"id\": \"editor.action.blockComment\",\n                        \"label\": \"Toggle &&Block Comment\"\n                    },\n                    {\n                        \"id\": \"editor.emmet.action.expandAbbreviation\",\n                        \"label\": \"Emmet: E&&xpand Abbreviation\"\n                    }\n                ]\n            },\n            \"Selection\": {\n                \"items\": [\n                    {\n                        \"id\": \"editor.action.selectAll\",\n                        \"label\": \"&&Select All\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.expand\",\n                        \"label\": \"&&Expand Selection\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.shrink\",\n                        \"label\": \"&&Shrink Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesUpAction\",\n                        \"label\": \"&&Copy Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesDownAction\",\n                        \"label\": \"Co&&py Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesUpAction\",\n                        \"label\": \"Mo&&ve Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesDownAction\",\n                        \"label\": \"Move &&Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.duplicateSelection\",\n                        \"label\": \"&&Duplicate Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAbove\",\n                        \"label\": \"&&Add Cursor Above\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorBelow\",\n                        \"label\": \"A&&dd Cursor Below\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAtEndOfEachLineSelected\",\n                        \"label\": \"Add C&&ursors to Line Ends\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToNextFindMatch\",\n                        \"label\": \"Add &&Next Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToPreviousFindMatch\",\n                        \"label\": \"Add P&&revious Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.selectHighlights\",\n                        \"label\": \"Select All &&Occurrences\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleMultiCursorModifier\",\n                        \"label\": \"Switch to Cmd+Click for Multi-Cursor\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleColumnSelection\",\n                        \"label\": \"Column &&Selection Mode\"\n                    }\n                ]\n            },\n            \"View\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"&&Command Palette...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openView\",\n                        \"label\": \"&&Open View...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarAppearanceMenu\",\n                        \"label\": \"&&Appearance\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.toggleFullScreen\",\n                                    \"label\": \"&&Full Screen\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleZenMode\",\n                                    \"label\": \"Zen Mode\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleCenteredLayout\",\n                                    \"label\": \"&&Centered Layout\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarVisibility\",\n                                    \"label\": \"&&Primary Side Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleAuxiliaryBar\",\n                                    \"label\": \"Secondary Si&&de Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleStatusbarVisibility\",\n                                    \"label\": \"S&&tatus Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.togglePanel\",\n                                    \"label\": \"&&Panel\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarPosition\",\n                                    \"label\": \"&&Move Primary Side Bar Right\"\n                                },\n                                {\n                                    \"id\": \"submenuitem.ActivityBarPositionMenu\",\n                                    \"label\": \"Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.default\",\n                                                \"label\": \"&&Default\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.AuxiliaryActivityBarPositionMenu\",\n                                    \"label\": \"Secondary Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.default\",\n                                                \"label\": \"&&Default\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelPositionMenu\",\n                                    \"label\": \"Panel Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.positionPanelTop\",\n                                                \"label\": \"Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelRight\",\n                                                \"label\": \"Right\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelBottom\",\n                                                \"label\": \"Bottom\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelAlignmentMenu\",\n                                    \"label\": \"Align Panel\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.alignPanelCenter\",\n                                                \"label\": \"Center\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelJustify\",\n                                                \"label\": \"Justify\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelRight\",\n                                                \"label\": \"Right\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorTabsBarShowTabsSubmenu\",\n                                    \"label\": \"Tab Bar\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.showMultipleEditorTabs\",\n                                                \"label\": \"Multiple Tabs\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.showEditorTab\",\n                                                \"label\": \"Single Tab\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorTabs\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorActionsPositionSubmenu\",\n                                    \"label\": \"Editor Actions Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.editorActionsDefault\",\n                                                \"label\": \"Tab Bar\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.editorActionsTitleBar\",\n                                                \"label\": \"Title Bar\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorActions\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleMinimap\",\n                                    \"label\": \"&&Minimap\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"breadcrumbs.toggle\",\n                                    \"label\": \"Toggle &&Breadcrumbs\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleStickyScroll\",\n                                    \"label\": \"&&Sticky Scroll\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderWhitespace\",\n                                    \"label\": \"&&Render Whitespace\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderControlCharacter\",\n                                    \"label\": \"Render &&Control Characters\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomIn\",\n                                    \"label\": \"&&Zoom In\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomOut\",\n                                    \"label\": \"&&Zoom Out\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomReset\",\n                                    \"label\": \"&&Reset Zoom\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarLayoutMenu\",\n                        \"label\": \"Editor &&Layout\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.splitEditorUp\",\n                                    \"label\": \"Split &&Up\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorDown\",\n                                    \"label\": \"Split &&Down\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorLeft\",\n                                    \"label\": \"Split &&Left\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorRight\",\n                                    \"label\": \"Split &&Right\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorInGroup\",\n                                    \"label\": \"Split in &&Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.moveEditorToNewWindow\",\n                                    \"label\": \"&&Move Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.copyEditorToNewWindow\",\n                                    \"label\": \"&&Copy Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutSingle\",\n                                    \"label\": \"&&Single\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumns\",\n                                    \"label\": \"&&Two Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeColumns\",\n                                    \"label\": \"T&&hree Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRows\",\n                                    \"label\": \"T&&wo Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeRows\",\n                                    \"label\": \"Three &&Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoByTwoGrid\",\n                                    \"label\": \"&&Grid (2x2)\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRowsRight\",\n                                    \"label\": \"Two R&&ows Right\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumnsBottom\",\n                                    \"label\": \"Two &&Columns Bottom\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleEditorGroupLayout\",\n                                    \"label\": \"Flip &&Layout\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.view.explorer\",\n                        \"label\": \"&&Explorer\"\n                    },\n                    {\n                        \"id\": \"workbench.view.search\",\n                        \"label\": \"&&Search\"\n                    },\n                    {\n                        \"id\": \"workbench.view.scm\",\n                        \"label\": \"Source &&Control\"\n                    },\n                    {\n                        \"id\": \"workbench.view.debug\",\n                        \"label\": \"&&Run\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"E&&xtensions\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.actions.view.problems\",\n                        \"label\": \"&&Problems\"\n                    },\n                    {\n                        \"id\": \"workbench.action.output.toggleOutput\",\n                        \"label\": \"&&Output\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.action.toggleRepl\",\n                        \"label\": \"De&&bug Console\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.toggleTerminal\",\n                        \"label\": \"&&Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleWordWrap\",\n                        \"label\": \"&&Word Wrap\",\n                        \"checked\": true\n                    }\n                ]\n            },\n            \"Go\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.navigateBack\",\n                        \"label\": \"&&Back\"\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateForward\",\n                        \"label\": \"&&Forward\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateToLastEditLocation\",\n                        \"label\": \"&&Last Edit Location\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchEditorMenu\",\n                        \"label\": \"Switch &&Editor\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.nextEditor\",\n                                    \"label\": \"&&Next Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditor\",\n                                    \"label\": \"&&Previous Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditor\",\n                                    \"label\": \"&&Next Used Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditor\",\n                                    \"label\": \"&&Previous Used Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.nextEditorInGroup\",\n                                    \"label\": \"&&Next Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditorInGroup\",\n                                    \"label\": \"&&Previous Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Next Used Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Previous Used Editor in Group\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchGroupMenu\",\n                        \"label\": \"Switch &&Group\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.focusFirstEditorGroup\",\n                                    \"label\": \"Group &&1\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusSecondEditorGroup\",\n                                    \"label\": \"Group &&2\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusThirdEditorGroup\",\n                                    \"label\": \"Group &&3\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFourthEditorGroup\",\n                                    \"label\": \"Group &&4\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFifthEditorGroup\",\n                                    \"label\": \"Group &&5\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusNextGroup\",\n                                    \"label\": \"&&Next Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusPreviousGroup\",\n                                    \"label\": \"&&Previous Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusLeftGroup\",\n                                    \"label\": \"Group &&Left\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusRightGroup\",\n                                    \"label\": \"Group &&Right\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusAboveGroup\",\n                                    \"label\": \"Group &&Above\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusBelowGroup\",\n                                    \"label\": \"Group &&Below\",\n                                    \"enabled\": false\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.quickOpen\",\n                        \"label\": \"Go to &&File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showAllSymbols\",\n                        \"label\": \"Go to Symbol in &&Workspace...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoSymbol\",\n                        \"label\": \"Go to &&Symbol in Editor...\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDefinition\",\n                        \"label\": \"Go to &&Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDeclaration\",\n                        \"label\": \"Go to &&Declaration\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToTypeDefinition\",\n                        \"label\": \"Go to &&Type Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToImplementation\",\n                        \"label\": \"Go to &&Implementations\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToReferences\",\n                        \"label\": \"Go to &&References\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoLine\",\n                        \"label\": \"Go to &&Line/Column...\"\n                    },\n                    {\n                        \"id\": \"editor.action.jumpToBracket\",\n                        \"label\": \"Go to &&Bracket\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.nextInFiles\",\n                        \"label\": \"Next &&Problem\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.prevInFiles\",\n                        \"label\": \"Previous &&Problem\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.next\",\n                        \"label\": \"Next &&Change\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.previous\",\n                        \"label\": \"Previous &&Change\"\n                    }\n                ]\n            },\n            \"Run\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.debug.start\",\n                        \"label\": \"&&Start Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.run\",\n                        \"label\": \"Run &&Without Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stop\",\n                        \"label\": \"&&Stop Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.restart\",\n                        \"label\": \"&&Restart Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.configure\",\n                        \"label\": \"Open &&Configurations\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"debug.addConfiguration\",\n                        \"label\": \"A&&dd Configuration...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOver\",\n                        \"label\": \"Step &&Over\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepInto\",\n                        \"label\": \"Step &&Into\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOut\",\n                        \"label\": \"Step O&&ut\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.continue\",\n                        \"label\": \"&&Continue\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.debug.action.toggleBreakpoint\",\n                        \"label\": \"Toggle &&Breakpoint\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarNewBreakpointMenu\",\n                        \"label\": \"&&New Breakpoint\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"editor.debug.action.conditionalBreakpoint\",\n                                    \"label\": \"&&Conditional Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.editBreakpoint\",\n                                    \"label\": \"&&Edit Breakpoint\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.toggleInlineBreakpoint\",\n                                    \"label\": \"Inline Breakp&&oint\"\n                                },\n                                {\n                                    \"id\": \"workbench.debug.viewlet.action.addFunctionBreakpointAction\",\n                                    \"label\": \"&&Function Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.addLogPoint\",\n                                    \"label\": \"&&Logpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.triggerByBreakpoint\",\n                                    \"label\": \"&&Triggered Breakpoint...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.enableAllBreakpoints\",\n                        \"label\": \"&&Enable All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.disableAllBreakpoints\",\n                        \"label\": \"Disable A&&ll Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.removeAllBreakpoints\",\n                        \"label\": \"Remove &&All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"debug.installAdditionalDebuggers\",\n                        \"label\": \"&&Install Additional Debuggers...\"\n                    }\n                ]\n            },\n            \"Terminal\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.terminal.new\",\n                        \"label\": \"&&New Terminal\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.split\",\n                        \"label\": \"&&Split Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.runTask\",\n                        \"label\": \"&&Run Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.build\",\n                        \"label\": \"Run &&Build Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runActiveFile\",\n                        \"label\": \"Run &&Active File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runSelectedText\",\n                        \"label\": \"Run &&Selected Text\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.showTasks\",\n                        \"label\": \"Show Runnin&&g Tasks...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.restartTask\",\n                        \"label\": \"R&&estart Running Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.terminate\",\n                        \"label\": \"&&Terminate Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureTaskRunner\",\n                        \"label\": \"&&Configure Tasks...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureDefaultBuildTask\",\n                        \"label\": \"Configure De&&fault Build Task...\"\n                    }\n                ]\n            },\n            \"Help\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"Show All Commands\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showInteractivePlayground\",\n                        \"label\": \"Editor Playgrou&&nd\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openLicenseUrl\",\n                        \"label\": \"View &&License\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleDevTools\",\n                        \"label\": \"Toggle Developer Tools\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openProcessExplorer\",\n                        \"label\": \"Open &&Process Explorer\"\n                    }\n                ]\n            },\n            \"Preferences\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.openSettings\",\n                        \"label\": \"&&Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWindsurfSettings\",\n                        \"label\": \"Windsurf Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"&&Extensions\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openGlobalKeybindings\",\n                        \"label\": \"Keyboard Shortcuts\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openSnippets\",\n                        \"label\": \"Configure Snippets\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.openUserTasks\",\n                        \"label\": \"Tasks\"\n                    },\n                    {\n                        \"id\": \"submenuitem.ThemesSubMenu\",\n                        \"label\": \"&&Themes\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.selectTheme\",\n                                    \"label\": \"Color Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectIconTheme\",\n                                    \"label\": \"File Icon Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectProductIconTheme\",\n                                    \"label\": \"Product Icon Theme\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"settings.filterByOnline\",\n                        \"label\": \"&&Online Services Settings\"\n                    }\n                ]\n            }\n        },\n        \"keybindings\": {\n            \"workbench.action.quit\": {\n                \"label\": \"Cmd+Q\",\n                \"userSettingsLabel\": \"cmd+q\"\n            },\n            \"workbench.action.files.newUntitledFile\": {\n                \"label\": \"Cmd+N\",\n                \"userSettingsLabel\": \"cmd+n\"\n            },\n            \"welcome.showNewFileEntries\": {\n                \"label\": \"Ctrl+Alt+Cmd+N\",\n                \"userSettingsLabel\": \"ctrl+alt+cmd+n\"\n            },\n            \"workbench.action.newWindow\": {\n                \"label\": \"Shift+Cmd+N\",\n                \"userSettingsLabel\": \"shift+cmd+n\"\n            },\n            \"workbench.action.files.openFileFolder\": {\n                \"label\": \"Cmd+O\",\n                \"userSettingsLabel\": \"cmd+o\"\n            },\n            \"workbench.action.reopenClosedEditor\": {\n                \"label\": \"Shift+Cmd+T\",\n                \"userSettingsLabel\": \"shift+cmd+t\"\n            },\n            \"workbench.action.openRecent\": {\n                \"label\": \"Ctrl+R\",\n                \"userSettingsLabel\": \"ctrl+r\"\n            },\n            \"workbench.action.files.save\": {\n                \"label\": \"Cmd+S\",\n                \"userSettingsLabel\": \"cmd+s\"\n            },\n            \"workbench.action.files.saveAs\": {\n                \"label\": \"Shift+Cmd+S\",\n                \"userSettingsLabel\": \"shift+cmd+s\"\n            },\n            \"saveAll\": {\n                \"label\": \"Alt+Cmd+S\",\n                \"userSettingsLabel\": \"alt+cmd+s\"\n            },\n            \"workbench.action.closeActiveEditor\": {\n                \"label\": \"Cmd+W\",\n                \"userSettingsLabel\": \"cmd+w\"\n            },\n            \"workbench.action.closeWindow\": {\n                \"label\": \"Shift+Cmd+W\",\n                \"userSettingsLabel\": \"shift+cmd+w\"\n            },\n            \"undo\": {\n                \"label\": \"Cmd+Z\",\n                \"userSettingsLabel\": \"cmd+z\"\n            },\n            \"redo\": {\n                \"label\": \"Shift+Cmd+Z\",\n                \"userSettingsLabel\": \"shift+cmd+z\"\n            },\n            \"editor.action.clipboardCutAction\": {\n                \"label\": \"Cmd+X\",\n                \"userSettingsLabel\": \"cmd+x\"\n            },\n            \"editor.action.clipboardCopyAction\": {\n                \"label\": \"Cmd+C\",\n                \"userSettingsLabel\": \"cmd+c\"\n            },\n            \"editor.action.clipboardPasteAction\": {\n                \"label\": \"Cmd+V\",\n                \"userSettingsLabel\": \"cmd+v\"\n            },\n            \"actions.find\": {\n                \"label\": \"Cmd+F\",\n                \"userSettingsLabel\": \"cmd+f\"\n            },\n            \"editor.action.startFindReplaceAction\": {\n                \"label\": \"Alt+Cmd+F\",\n                \"userSettingsLabel\": \"alt+cmd+f\"\n            },\n            \"workbench.action.findInFiles\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.action.replaceInFiles\": {\n                \"label\": \"Shift+Cmd+H\",\n                \"userSettingsLabel\": \"shift+cmd+h\"\n            },\n            \"editor.action.commentLine\": {\n                \"label\": \"Cmd+/\",\n                \"userSettingsLabel\": \"cmd+/\"\n            },\n            \"editor.action.blockComment\": {\n                \"label\": \"Shift+Alt+A\",\n                \"userSettingsLabel\": \"shift+alt+a\"\n            },\n            \"editor.emmet.action.expandAbbreviation\": {\n                \"label\": \"Tab\",\n                \"userSettingsLabel\": \"tab\"\n            },\n            \"editor.action.selectAll\": {\n                \"label\": \"Cmd+A\",\n                \"userSettingsLabel\": \"cmd+a\"\n            },\n            \"editor.action.smartSelect.expand\": {\n                \"label\": \"Ctrl+Shift+Cmd+Right\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+right\"\n            },\n            \"editor.action.smartSelect.shrink\": {\n                \"label\": \"Ctrl+Shift+Cmd+Left\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+left\"\n            },\n            \"editor.action.copyLinesUpAction\": {\n                \"label\": \"Shift+Alt+Up\",\n                \"userSettingsLabel\": \"shift+alt+up\"\n            },\n            \"editor.action.copyLinesDownAction\": {\n                \"label\": \"Shift+Alt+Down\",\n                \"userSettingsLabel\": \"shift+alt+down\"\n            },\n            \"editor.action.moveLinesUpAction\": {\n                \"label\": \"Alt+Up\",\n                \"userSettingsLabel\": \"alt+up\"\n            },\n            \"editor.action.moveLinesDownAction\": {\n                \"label\": \"Alt+Down\",\n                \"userSettingsLabel\": \"alt+down\"\n            },\n            \"editor.action.insertCursorAbove\": {\n                \"label\": \"Alt+Cmd+Up\",\n                \"userSettingsLabel\": \"alt+cmd+up\"\n            },\n            \"editor.action.insertCursorBelow\": {\n                \"label\": \"Alt+Cmd+Down\",\n                \"userSettingsLabel\": \"alt+cmd+down\"\n            },\n            \"editor.action.insertCursorAtEndOfEachLineSelected\": {\n                \"label\": \"Shift+Alt+I\",\n                \"userSettingsLabel\": \"shift+alt+i\"\n            },\n            \"editor.action.addSelectionToNextFindMatch\": {\n                \"label\": \"Cmd+D\",\n                \"userSettingsLabel\": \"cmd+d\"\n            },\n            \"editor.action.selectHighlights\": {\n                \"label\": \"Shift+Cmd+L\",\n                \"userSettingsLabel\": \"shift+cmd+l\"\n            },\n            \"workbench.action.showCommands\": {\n                \"label\": \"Shift+Cmd+P\",\n                \"userSettingsLabel\": \"shift+cmd+p\"\n            },\n            \"workbench.action.toggleFullScreen\": {\n                \"label\": \"Ctrl+Cmd+F\",\n                \"userSettingsLabel\": \"ctrl+cmd+f\"\n            },\n            \"workbench.action.toggleSidebarVisibility\": {\n                \"label\": \"Cmd+B\",\n                \"userSettingsLabel\": \"cmd+b\"\n            },\n            \"workbench.action.toggleAuxiliaryBar\": {\n                \"label\": \"Alt+Cmd+B\",\n                \"userSettingsLabel\": \"alt+cmd+b\"\n            },\n            \"workbench.action.togglePanel\": {\n                \"label\": \"Cmd+J\",\n                \"userSettingsLabel\": \"cmd+j\"\n            },\n            \"workbench.action.zoomIn\": {\n                \"label\": \"Cmd+=\",\n                \"userSettingsLabel\": \"cmd+=\"\n            },\n            \"workbench.action.zoomOut\": {\n                \"label\": \"Cmd+-\",\n                \"userSettingsLabel\": \"cmd+-\"\n            },\n            \"workbench.action.zoomReset\": {\n                \"label\": \"⌘NumPad0\",\n                \"isNative\": false,\n                \"userSettingsLabel\": \"cmd+numpad0\"\n            },\n            \"workbench.action.toggleEditorGroupLayout\": {\n                \"label\": \"Alt+Cmd+0\",\n                \"userSettingsLabel\": \"alt+cmd+0\"\n            },\n            \"workbench.view.explorer\": {\n                \"label\": \"Shift+Cmd+E\",\n                \"userSettingsLabel\": \"shift+cmd+e\"\n            },\n            \"workbench.view.search\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.view.scm\": {\n                \"label\": \"Ctrl+Shift+G\",\n                \"userSettingsLabel\": \"ctrl+shift+g\"\n            },\n            \"workbench.view.debug\": {\n                \"label\": \"Shift+Cmd+D\",\n                \"userSettingsLabel\": \"shift+cmd+d\"\n            },\n            \"workbench.view.extensions\": {\n                \"label\": \"Shift+Cmd+X\",\n                \"userSettingsLabel\": \"shift+cmd+x\"\n            },\n            \"workbench.actions.view.problems\": {\n                \"label\": \"Shift+Cmd+M\",\n                \"userSettingsLabel\": \"shift+cmd+m\"\n            },\n            \"workbench.action.output.toggleOutput\": {\n                \"label\": \"Shift+Cmd+U\",\n                \"userSettingsLabel\": \"shift+cmd+u\"\n            },\n            \"workbench.debug.action.toggleRepl\": {\n                \"label\": \"Shift+Cmd+Y\",\n                \"userSettingsLabel\": \"shift+cmd+y\"\n            },\n            \"editor.action.toggleWordWrap\": {\n                \"label\": \"Alt+Z\",\n                \"userSettingsLabel\": \"alt+z\"\n            },\n            \"workbench.action.navigateBack\": {\n                \"label\": \"Ctrl+-\",\n                \"userSettingsLabel\": \"ctrl+-\"\n            },\n            \"workbench.action.navigateForward\": {\n                \"label\": \"Ctrl+Shift+-\",\n                \"userSettingsLabel\": \"ctrl+shift+-\"\n            },\n            \"workbench.action.nextEditor\": {\n                \"label\": \"Alt+Cmd+Right\",\n                \"userSettingsLabel\": \"alt+cmd+right\"\n            },\n            \"workbench.action.previousEditor\": {\n                \"label\": \"Alt+Cmd+Left\",\n                \"userSettingsLabel\": \"alt+cmd+left\"\n            },\n            \"workbench.action.focusFirstEditorGroup\": {\n                \"label\": \"Cmd+1\",\n                \"userSettingsLabel\": \"cmd+1\"\n            },\n            \"workbench.action.focusSecondEditorGroup\": {\n                \"label\": \"Cmd+2\",\n                \"userSettingsLabel\": \"cmd+2\"\n            },\n            \"workbench.action.focusThirdEditorGroup\": {\n                \"label\": \"Cmd+3\",\n                \"userSettingsLabel\": \"cmd+3\"\n            },\n            \"workbench.action.focusFourthEditorGroup\": {\n                \"label\": \"Cmd+4\",\n                \"userSettingsLabel\": \"cmd+4\"\n            },\n            \"workbench.action.focusFifthEditorGroup\": {\n                \"label\": \"Cmd+5\",\n                \"userSettingsLabel\": \"cmd+5\"\n            },\n            \"workbench.action.quickOpen\": {\n                \"label\": \"Cmd+P\",\n                \"userSettingsLabel\": \"cmd+p\"\n            },\n            \"workbench.action.showAllSymbols\": {\n                \"label\": \"Cmd+T\",\n                \"userSettingsLabel\": \"cmd+t\"\n            },\n            \"workbench.action.gotoSymbol\": {\n                \"label\": \"Shift+Cmd+O\",\n                \"userSettingsLabel\": \"shift+cmd+o\"\n            },\n            \"editor.action.revealDefinition\": {\n                \"label\": \"F12\",\n                \"userSettingsLabel\": \"f12\"\n            },\n            \"editor.action.goToImplementation\": {\n                \"label\": \"Cmd+F12\",\n                \"userSettingsLabel\": \"cmd+f12\"\n            },\n            \"editor.action.goToReferences\": {\n                \"label\": \"Shift+F12\",\n                \"userSettingsLabel\": \"shift+f12\"\n            },\n            \"workbench.action.gotoLine\": {\n                \"label\": \"Ctrl+G\",\n                \"userSettingsLabel\": \"ctrl+g\"\n            },\n            \"editor.action.marker.nextInFiles\": {\n                \"label\": \"F8\",\n                \"userSettingsLabel\": \"f8\"\n            },\n            \"editor.action.marker.prevInFiles\": {\n                \"label\": \"Shift+F8\",\n                \"userSettingsLabel\": \"shift+f8\"\n            },\n            \"editor.action.dirtydiff.next\": {\n                \"label\": \"Alt+F3\",\n                \"userSettingsLabel\": \"alt+f3\"\n            },\n            \"editor.action.dirtydiff.previous\": {\n                \"label\": \"Shift+Alt+F3\",\n                \"userSettingsLabel\": \"shift+alt+f3\"\n            },\n            \"workbench.action.debug.start\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"workbench.action.debug.run\": {\n                \"label\": \"Ctrl+F5\",\n                \"userSettingsLabel\": \"ctrl+f5\"\n            },\n            \"workbench.action.debug.stop\": {\n                \"label\": \"Shift+F5\",\n                \"userSettingsLabel\": \"shift+f5\"\n            },\n            \"workbench.action.debug.restart\": {\n                \"label\": \"Shift+Cmd+F5\",\n                \"userSettingsLabel\": \"shift+cmd+f5\"\n            },\n            \"workbench.action.debug.stepOver\": {\n                \"label\": \"F10\",\n                \"userSettingsLabel\": \"f10\"\n            },\n            \"workbench.action.debug.stepInto\": {\n                \"label\": \"F11\",\n                \"userSettingsLabel\": \"f11\"\n            },\n            \"workbench.action.debug.stepOut\": {\n                \"label\": \"Shift+F11\",\n                \"userSettingsLabel\": \"shift+f11\"\n            },\n            \"workbench.action.debug.continue\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"editor.debug.action.toggleBreakpoint\": {\n                \"label\": \"F9\",\n                \"userSettingsLabel\": \"f9\"\n            },\n            \"editor.debug.action.toggleInlineBreakpoint\": {\n                \"label\": \"Shift+F9\",\n                \"userSettingsLabel\": \"shift+f9\"\n            },\n            \"workbench.action.terminal.split\": {\n                \"label\": \"Ctrl+Shift+5\",\n                \"userSettingsLabel\": \"ctrl+shift+5\"\n            },\n", "suffix": "            \"workbench.action.tasks.build\": {\n                \"label\": \"Shift+Cmd+B\",\n                \"userSettingsLabel\": \"shift+cmd+b\"\n            },\n            \"workbench.action.openWindsurfSettings\": {\n                \"label\": \"Cmd+,\",\n                \"userSettingsLabel\": \"cmd+,\"\n            }\n        }\n    },\n    \"theme\": \"vs-dark\",\n    \"themeBackground\": \"#1f1f1f\",\n    \"windowSplash\": {\n        \"zoomLevel\": 1,\n        \"baseTheme\": \"vs-dark\",\n        \"colorInfo\": {\n            \"foreground\": \"#cccccc\",\n            \"background\": \"#1f1f1f\",\n            \"editorBackground\": \"#1f1f1f\",\n            \"titleBarBackground\": \"#181818\",\n            \"titleBarBorder\": \"#2b2b2b\",\n            \"activityBarBackground\": \"#181818\",\n            \"activityBarBorder\": \"#2b2b2b\",\n            \"sideBarBackground\": \"#181818\",\n            \"sideBarBorder\": \"#2b2b2b\",\n            \"statusBarBackground\": \"#181818\",\n            \"statusBarBorder\": \"#2b2b2b\",\n            \"statusBarNoFolderBackground\": \"#1f1f1f\"\n        },\n        \"layoutInfo\": {\n            \"sideBarSide\": \"left\",\n            \"editorPartMinWidth\": 220,\n            \"titleBarHeight\": 35,\n            \"activityBarWidth\": 42,\n            \"sideBarWidth\": 213,\n            \"auxiliarySideBarWidth\": 523,\n            \"statusBarHeight\": 22,\n            \"windowBorder\": false\n        }\n    },\n    \"windowsState\": {\n        \"lastActiveWindow\": {\n            \"folder\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\",\n            \"backupPath\": \"/Users/<USER>/Library/Application Support/Windsurf/Backups/a159014097c8119daed6fdd2d20bad90\",\n            \"uiState\": {\n                \"mode\": 1,\n                \"x\": 33,\n                \"y\": 25,\n                \"width\": 1407,\n                \"height\": 875,\n                \"zoomLevel\": 0\n            }\n        },\n        \"openedWindows\": []\n    },\n    \"windowSplashWorkspaceOverride\": {\n        \"layoutInfo\": {\n            \"auxiliarySideBarWidth\": [\n                523,\n                [\n                    \"c59be2f483dcdc78296c3a4a55c688ec\"\n                ]\n            ]\n        }\n    },\n    \"telemetry.sessionId\": \"2034D9B9-B912-42D0-B6E7-393388AEF72A\",\n    \"telemetry.instanceId\": \"63372F8A-6B5E-45AF-BB7C-E786045FBDDB\",\n    \"telemetry.sqmUserId\": \"a137b0725136a02065862bd942e721af\",\n    \"telemetry.installationId\": \"FE358A14-E67F-4A20-B61F-42151D94EAAC\",\n    \"telemetry.firstSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"telemetry.lastSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"user.globalId\": \"E5D9C812-6AF1-41AE-8197-65B98780FCD6\",\n    \"installation.time\": \"2025-06-13T14:55:49.3NZ\",\n    \"sync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"settingsSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"userDataSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"marketplace.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"gallery.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"remote.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"tunnels.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\"\n}", "path": "User/globalStorage/storage.json", "language": "json", "prefixBegin": 0, "suffixEnd": 1}}], ["848ddf64-f45d-4fd6-8693-1234bade1efb", {"value": {"selectedCode": "", "prefix": "{\n    \"telemetry.sqmId\": \"AuxiliaryActivityBarPositionMenu\",\n    \"telemetry.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"telemetry.devDeviceId\": \"7583883f-b598-424d-b907-67f448c00f85\",\n    \"backupWorkspaces\": {\n        \"workspaces\": [],\n        \"folders\": [\n            {\n                \"folderUri\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\"\n            }\n        ],\n        \"emptyWindows\": []\n    },\n    \"windowControlHeight\": 35,\n    \"profileAssociations\": {\n        \"workspaces\": {\n            \"file:///Users/<USER>/Library/Application%20Support/Windsurf\": \"__default__profile__\"\n        },\n        \"emptyWindows\": {}\n    },\n    \"lastKnownMenubarData\": {\n        \"menus\": {\n            \"File\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.files.newUntitledFile\",\n                        \"label\": \"&&New Text File\"\n                    },\n                    {\n                        \"id\": \"welcome.showNewFileEntries\",\n                        \"label\": \"New File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.newWindow\",\n                        \"label\": \"New &&Window\"\n                    },\n                    {\n                        \"id\": \"submenuitem.OpenProfile\",\n                        \"label\": \"New Window with Profile\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.createProfile\",\n                                    \"label\": \"New Profile...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFileFolder\",\n                        \"label\": \"&&Open...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFolder\",\n                        \"label\": \"Open &&Folder...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWorkspace\",\n                        \"label\": \"Open Wor&&kspace from File...\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarRecentMenu\",\n                        \"label\": \"Open &&Recent\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.reopenClosedEditor\",\n                                    \"label\": \"&&Reopen Closed Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"openRecentFolder\",\n                                    \"uri\": {\n                                        \"$mid\": 1,\n                                        \"path\": \"/Users/<USER>/Library/Application Support/Windsurf\",\n                                        \"scheme\": \"file\"\n                                    },\n                                    \"enabled\": true,\n                                    \"label\": \"~/Library/Application Support/Windsurf\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openRecent\",\n                                    \"label\": \"&&More...\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.clearRecentFiles\",\n                                    \"label\": \"&&Clear Recently Opened...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"addRootFolder\",\n                        \"label\": \"A&&dd Folder to Workspace...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.saveWorkspaceAs\",\n                        \"label\": \"Save Workspace As...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.duplicateWorkspaceInNewWindow\",\n                        \"label\": \"Duplicate Workspace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.save\",\n                        \"label\": \"&&Save\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.saveAs\",\n                        \"label\": \"Save &&As...\"\n                    },\n                    {\n                        \"id\": \"saveAll\",\n                        \"label\": \"Save A&&ll\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarShare\",\n                        \"label\": \"Share\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.exportProfile\",\n                                    \"label\": \"Export Profile (Default)...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleAutoSave\",\n                        \"label\": \"A&&uto Save\",\n                        \"checked\": true\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.revert\",\n                        \"label\": \"Re&&vert File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeActiveEditor\",\n                        \"label\": \"&&Close Editor\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeFolder\",\n                        \"label\": \"Close &&Folder\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeWindow\",\n                        \"label\": \"Clos&&e Window\"\n                    }\n                ]\n            },\n            \"Edit\": {\n                \"items\": [\n                    {\n                        \"id\": \"undo\",\n                        \"label\": \"&&Undo\"\n                    },\n                    {\n                        \"id\": \"redo\",\n                        \"label\": \"&&Redo\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCutAction\",\n                        \"label\": \"Cu&&t\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCopyAction\",\n                        \"label\": \"&&Copy\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardPasteAction\",\n                        \"label\": \"&&Paste\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"actions.find\",\n                        \"label\": \"&&Find\"\n                    },\n                    {\n                        \"id\": \"editor.action.startFindReplaceAction\",\n                        \"label\": \"&&Replace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.findInFiles\",\n                        \"label\": \"Find &&in Files\"\n                    },\n                    {\n                        \"id\": \"workbench.action.replaceInFiles\",\n                        \"label\": \"Replace in Files\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.commentLine\",\n                        \"label\": \"&&Toggle Line Comment\"\n                    },\n                    {\n                        \"id\": \"editor.action.blockComment\",\n                        \"label\": \"Toggle &&Block Comment\"\n                    },\n                    {\n                        \"id\": \"editor.emmet.action.expandAbbreviation\",\n                        \"label\": \"Emmet: E&&xpand Abbreviation\"\n                    }\n                ]\n            },\n            \"Selection\": {\n                \"items\": [\n                    {\n                        \"id\": \"editor.action.selectAll\",\n                        \"label\": \"&&Select All\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.expand\",\n                        \"label\": \"&&Expand Selection\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.shrink\",\n                        \"label\": \"&&Shrink Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesUpAction\",\n                        \"label\": \"&&Copy Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesDownAction\",\n                        \"label\": \"Co&&py Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesUpAction\",\n                        \"label\": \"Mo&&ve Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesDownAction\",\n                        \"label\": \"Move &&Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.duplicateSelection\",\n                        \"label\": \"&&Duplicate Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAbove\",\n                        \"label\": \"&&Add Cursor Above\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorBelow\",\n                        \"label\": \"A&&dd Cursor Below\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAtEndOfEachLineSelected\",\n                        \"label\": \"Add C&&ursors to Line Ends\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToNextFindMatch\",\n                        \"label\": \"Add &&Next Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToPreviousFindMatch\",\n                        \"label\": \"Add P&&revious Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.selectHighlights\",\n                        \"label\": \"Select All &&Occurrences\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleMultiCursorModifier\",\n                        \"label\": \"Switch to Cmd+Click for Multi-Cursor\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleColumnSelection\",\n                        \"label\": \"Column &&Selection Mode\"\n                    }\n                ]\n            },\n            \"View\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"&&Command Palette...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openView\",\n                        \"label\": \"&&Open View...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarAppearanceMenu\",\n                        \"label\": \"&&Appearance\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.toggleFullScreen\",\n                                    \"label\": \"&&Full Screen\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleZenMode\",\n                                    \"label\": \"Zen Mode\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleCenteredLayout\",\n                                    \"label\": \"&&Centered Layout\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarVisibility\",\n                                    \"label\": \"&&Primary Side Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleAuxiliaryBar\",\n                                    \"label\": \"Secondary Si&&de Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleStatusbarVisibility\",\n                                    \"label\": \"S&&tatus Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.togglePanel\",\n                                    \"label\": \"&&Panel\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarPosition\",\n                                    \"label\": \"&&Move Primary Side Bar Right\"\n                                },\n                                {\n                                    \"id\": \"submenuitem.ActivityBarPositionMenu\",\n                                    \"label\": \"Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.default\",\n                                                \"label\": \"&&Default\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.AuxiliaryActivityBarPositionMenu\",\n                                    \"label\": \"Secondary Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.default\",\n                                                \"label\": \"&&Default\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelPositionMenu\",\n                                    \"label\": \"Panel Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.positionPanelTop\",\n                                                \"label\": \"Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelRight\",\n                                                \"label\": \"Right\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelBottom\",\n                                                \"label\": \"Bottom\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelAlignmentMenu\",\n                                    \"label\": \"Align Panel\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.alignPanelCenter\",\n                                                \"label\": \"Center\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelJustify\",\n                                                \"label\": \"Justify\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelRight\",\n                                                \"label\": \"Right\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorTabsBarShowTabsSubmenu\",\n                                    \"label\": \"Tab Bar\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.showMultipleEditorTabs\",\n                                                \"label\": \"Multiple Tabs\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.showEditorTab\",\n                                                \"label\": \"Single Tab\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorTabs\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorActionsPositionSubmenu\",\n                                    \"label\": \"Editor Actions Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.editorActionsDefault\",\n                                                \"label\": \"Tab Bar\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.editorActionsTitleBar\",\n                                                \"label\": \"Title Bar\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorActions\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleMinimap\",\n                                    \"label\": \"&&Minimap\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"breadcrumbs.toggle\",\n                                    \"label\": \"Toggle &&Breadcrumbs\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleStickyScroll\",\n                                    \"label\": \"&&Sticky Scroll\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderWhitespace\",\n                                    \"label\": \"&&Render Whitespace\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderControlCharacter\",\n                                    \"label\": \"Render &&Control Characters\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomIn\",\n                                    \"label\": \"&&Zoom In\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomOut\",\n                                    \"label\": \"&&Zoom Out\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomReset\",\n                                    \"label\": \"&&Reset Zoom\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarLayoutMenu\",\n                        \"label\": \"Editor &&Layout\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.splitEditorUp\",\n                                    \"label\": \"Split &&Up\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorDown\",\n                                    \"label\": \"Split &&Down\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorLeft\",\n                                    \"label\": \"Split &&Left\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorRight\",\n                                    \"label\": \"Split &&Right\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorInGroup\",\n                                    \"label\": \"Split in &&Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.moveEditorToNewWindow\",\n                                    \"label\": \"&&Move Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.copyEditorToNewWindow\",\n                                    \"label\": \"&&Copy Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutSingle\",\n                                    \"label\": \"&&Single\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumns\",\n                                    \"label\": \"&&Two Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeColumns\",\n                                    \"label\": \"T&&hree Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRows\",\n                                    \"label\": \"T&&wo Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeRows\",\n                                    \"label\": \"Three &&Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoByTwoGrid\",\n                                    \"label\": \"&&Grid (2x2)\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRowsRight\",\n                                    \"label\": \"Two R&&ows Right\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumnsBottom\",\n                                    \"label\": \"Two &&Columns Bottom\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleEditorGroupLayout\",\n                                    \"label\": \"Flip &&Layout\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.view.explorer\",\n                        \"label\": \"&&Explorer\"\n                    },\n                    {\n                        \"id\": \"workbench.view.search\",\n                        \"label\": \"&&Search\"\n                    },\n                    {\n                        \"id\": \"workbench.view.scm\",\n                        \"label\": \"Source &&Control\"\n                    },\n                    {\n                        \"id\": \"workbench.view.debug\",\n                        \"label\": \"&&Run\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"E&&xtensions\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.actions.view.problems\",\n                        \"label\": \"&&Problems\"\n                    },\n                    {\n                        \"id\": \"workbench.action.output.toggleOutput\",\n                        \"label\": \"&&Output\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.action.toggleRepl\",\n                        \"label\": \"De&&bug Console\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.toggleTerminal\",\n                        \"label\": \"&&Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleWordWrap\",\n                        \"label\": \"&&Word Wrap\",\n                        \"checked\": true\n                    }\n                ]\n            },\n            \"Go\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.navigateBack\",\n                        \"label\": \"&&Back\"\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateForward\",\n                        \"label\": \"&&Forward\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateToLastEditLocation\",\n                        \"label\": \"&&Last Edit Location\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchEditorMenu\",\n                        \"label\": \"Switch &&Editor\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.nextEditor\",\n                                    \"label\": \"&&Next Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditor\",\n                                    \"label\": \"&&Previous Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditor\",\n                                    \"label\": \"&&Next Used Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditor\",\n                                    \"label\": \"&&Previous Used Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.nextEditorInGroup\",\n                                    \"label\": \"&&Next Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditorInGroup\",\n                                    \"label\": \"&&Previous Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Next Used Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Previous Used Editor in Group\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchGroupMenu\",\n                        \"label\": \"Switch &&Group\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.focusFirstEditorGroup\",\n                                    \"label\": \"Group &&1\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusSecondEditorGroup\",\n                                    \"label\": \"Group &&2\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusThirdEditorGroup\",\n                                    \"label\": \"Group &&3\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFourthEditorGroup\",\n                                    \"label\": \"Group &&4\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFifthEditorGroup\",\n                                    \"label\": \"Group &&5\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusNextGroup\",\n                                    \"label\": \"&&Next Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusPreviousGroup\",\n                                    \"label\": \"&&Previous Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusLeftGroup\",\n                                    \"label\": \"Group &&Left\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusRightGroup\",\n                                    \"label\": \"Group &&Right\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusAboveGroup\",\n                                    \"label\": \"Group &&Above\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusBelowGroup\",\n                                    \"label\": \"Group &&Below\",\n                                    \"enabled\": false\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.quickOpen\",\n                        \"label\": \"Go to &&File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showAllSymbols\",\n                        \"label\": \"Go to Symbol in &&Workspace...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoSymbol\",\n                        \"label\": \"Go to &&Symbol in Editor...\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDefinition\",\n                        \"label\": \"Go to &&Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDeclaration\",\n                        \"label\": \"Go to &&Declaration\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToTypeDefinition\",\n                        \"label\": \"Go to &&Type Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToImplementation\",\n                        \"label\": \"Go to &&Implementations\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToReferences\",\n                        \"label\": \"Go to &&References\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoLine\",\n                        \"label\": \"Go to &&Line/Column...\"\n                    },\n                    {\n                        \"id\": \"editor.action.jumpToBracket\",\n                        \"label\": \"Go to &&Bracket\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.nextInFiles\",\n                        \"label\": \"Next &&Problem\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.prevInFiles\",\n                        \"label\": \"Previous &&Problem\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.next\",\n                        \"label\": \"Next &&Change\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.previous\",\n                        \"label\": \"Previous &&Change\"\n                    }\n                ]\n            },\n            \"Run\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.debug.start\",\n                        \"label\": \"&&Start Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.run\",\n                        \"label\": \"Run &&Without Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stop\",\n                        \"label\": \"&&Stop Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.restart\",\n                        \"label\": \"&&Restart Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.configure\",\n                        \"label\": \"Open &&Configurations\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"debug.addConfiguration\",\n                        \"label\": \"A&&dd Configuration...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOver\",\n                        \"label\": \"Step &&Over\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepInto\",\n                        \"label\": \"Step &&Into\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOut\",\n                        \"label\": \"Step O&&ut\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.continue\",\n                        \"label\": \"&&Continue\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.debug.action.toggleBreakpoint\",\n                        \"label\": \"Toggle &&Breakpoint\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarNewBreakpointMenu\",\n                        \"label\": \"&&New Breakpoint\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"editor.debug.action.conditionalBreakpoint\",\n                                    \"label\": \"&&Conditional Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.editBreakpoint\",\n                                    \"label\": \"&&Edit Breakpoint\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.toggleInlineBreakpoint\",\n                                    \"label\": \"Inline Breakp&&oint\"\n                                },\n                                {\n                                    \"id\": \"workbench.debug.viewlet.action.addFunctionBreakpointAction\",\n                                    \"label\": \"&&Function Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.addLogPoint\",\n                                    \"label\": \"&&Logpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.triggerByBreakpoint\",\n                                    \"label\": \"&&Triggered Breakpoint...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.enableAllBreakpoints\",\n                        \"label\": \"&&Enable All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.disableAllBreakpoints\",\n                        \"label\": \"Disable A&&ll Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.removeAllBreakpoints\",\n                        \"label\": \"Remove &&All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"debug.installAdditionalDebuggers\",\n                        \"label\": \"&&Install Additional Debuggers...\"\n                    }\n                ]\n            },\n            \"Terminal\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.terminal.new\",\n                        \"label\": \"&&New Terminal\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.split\",\n                        \"label\": \"&&Split Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.runTask\",\n                        \"label\": \"&&Run Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.build\",\n                        \"label\": \"Run &&Build Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runActiveFile\",\n                        \"label\": \"Run &&Active File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runSelectedText\",\n                        \"label\": \"Run &&Selected Text\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.showTasks\",\n                        \"label\": \"Show Runnin&&g Tasks...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.restartTask\",\n                        \"label\": \"R&&estart Running Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.terminate\",\n                        \"label\": \"&&Terminate Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureTaskRunner\",\n                        \"label\": \"&&Configure Tasks...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureDefaultBuildTask\",\n                        \"label\": \"Configure De&&fault Build Task...\"\n                    }\n                ]\n            },\n            \"Help\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"Show All Commands\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showInteractivePlayground\",\n                        \"label\": \"Editor Playgrou&&nd\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openLicenseUrl\",\n                        \"label\": \"View &&License\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleDevTools\",\n                        \"label\": \"Toggle Developer Tools\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openProcessExplorer\",\n                        \"label\": \"Open &&Process Explorer\"\n                    }\n                ]\n            },\n            \"Preferences\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.openSettings\",\n                        \"label\": \"&&Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWindsurfSettings\",\n                        \"label\": \"Windsurf Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"&&Extensions\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openGlobalKeybindings\",\n                        \"label\": \"Keyboard Shortcuts\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openSnippets\",\n                        \"label\": \"Configure Snippets\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.openUserTasks\",\n                        \"label\": \"Tasks\"\n                    },\n                    {\n                        \"id\": \"submenuitem.ThemesSubMenu\",\n                        \"label\": \"&&Themes\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.selectTheme\",\n                                    \"label\": \"Color Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectIconTheme\",\n                                    \"label\": \"File Icon Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectProductIconTheme\",\n                                    \"label\": \"Product Icon Theme\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"settings.filterByOnline\",\n                        \"label\": \"&&Online Services Settings\"\n                    }\n                ]\n            }\n        },\n        \"keybindings\": {\n            \"workbench.action.quit\": {\n                \"label\": \"Cmd+Q\",\n                \"userSettingsLabel\": \"cmd+q\"\n            },\n            \"workbench.action.files.newUntitledFile\": {\n                \"label\": \"Cmd+N\",\n                \"userSettingsLabel\": \"cmd+n\"\n            },\n            \"welcome.showNewFileEntries\": {\n                \"label\": \"Ctrl+Alt+Cmd+N\",\n                \"userSettingsLabel\": \"ctrl+alt+cmd+n\"\n            },\n            \"workbench.action.newWindow\": {\n                \"label\": \"Shift+Cmd+N\",\n                \"userSettingsLabel\": \"shift+cmd+n\"\n            },\n            \"workbench.action.files.openFileFolder\": {\n                \"label\": \"Cmd+O\",\n                \"userSettingsLabel\": \"cmd+o\"\n            },\n            \"workbench.action.reopenClosedEditor\": {\n                \"label\": \"Shift+Cmd+T\",\n                \"userSettingsLabel\": \"shift+cmd+t\"\n            },\n            \"workbench.action.openRecent\": {\n                \"label\": \"Ctrl+R\",\n                \"userSettingsLabel\": \"ctrl+r\"\n            },\n            \"workbench.action.files.save\": {\n                \"label\": \"Cmd+S\",\n                \"userSettingsLabel\": \"cmd+s\"\n            },\n            \"workbench.action.files.saveAs\": {\n                \"label\": \"Shift+Cmd+S\",\n                \"userSettingsLabel\": \"shift+cmd+s\"\n            },\n            \"saveAll\": {\n                \"label\": \"Alt+Cmd+S\",\n                \"userSettingsLabel\": \"alt+cmd+s\"\n            },\n            \"workbench.action.closeActiveEditor\": {\n                \"label\": \"Cmd+W\",\n                \"userSettingsLabel\": \"cmd+w\"\n            },\n            \"workbench.action.closeWindow\": {\n                \"label\": \"Shift+Cmd+W\",\n                \"userSettingsLabel\": \"shift+cmd+w\"\n            },\n            \"undo\": {\n                \"label\": \"Cmd+Z\",\n                \"userSettingsLabel\": \"cmd+z\"\n            },\n            \"redo\": {\n                \"label\": \"Shift+Cmd+Z\",\n                \"userSettingsLabel\": \"shift+cmd+z\"\n            },\n            \"editor.action.clipboardCutAction\": {\n                \"label\": \"Cmd+X\",\n                \"userSettingsLabel\": \"cmd+x\"\n            },\n            \"editor.action.clipboardCopyAction\": {\n                \"label\": \"Cmd+C\",\n                \"userSettingsLabel\": \"cmd+c\"\n            },\n            \"editor.action.clipboardPasteAction\": {\n                \"label\": \"Cmd+V\",\n                \"userSettingsLabel\": \"cmd+v\"\n            },\n            \"actions.find\": {\n                \"label\": \"Cmd+F\",\n                \"userSettingsLabel\": \"cmd+f\"\n            },\n            \"editor.action.startFindReplaceAction\": {\n                \"label\": \"Alt+Cmd+F\",\n                \"userSettingsLabel\": \"alt+cmd+f\"\n            },\n            \"workbench.action.findInFiles\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.action.replaceInFiles\": {\n                \"label\": \"Shift+Cmd+H\",\n                \"userSettingsLabel\": \"shift+cmd+h\"\n            },\n            \"editor.action.commentLine\": {\n                \"label\": \"Cmd+/\",\n                \"userSettingsLabel\": \"cmd+/\"\n            },\n            \"editor.action.blockComment\": {\n                \"label\": \"Shift+Alt+A\",\n                \"userSettingsLabel\": \"shift+alt+a\"\n            },\n            \"editor.emmet.action.expandAbbreviation\": {\n                \"label\": \"Tab\",\n                \"userSettingsLabel\": \"tab\"\n            },\n            \"editor.action.selectAll\": {\n                \"label\": \"Cmd+A\",\n                \"userSettingsLabel\": \"cmd+a\"\n            },\n            \"editor.action.smartSelect.expand\": {\n                \"label\": \"Ctrl+Shift+Cmd+Right\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+right\"\n            },\n            \"editor.action.smartSelect.shrink\": {\n                \"label\": \"Ctrl+Shift+Cmd+Left\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+left\"\n            },\n            \"editor.action.copyLinesUpAction\": {\n                \"label\": \"Shift+Alt+Up\",\n                \"userSettingsLabel\": \"shift+alt+up\"\n            },\n            \"editor.action.copyLinesDownAction\": {\n                \"label\": \"Shift+Alt+Down\",\n                \"userSettingsLabel\": \"shift+alt+down\"\n            },\n            \"editor.action.moveLinesUpAction\": {\n                \"label\": \"Alt+Up\",\n                \"userSettingsLabel\": \"alt+up\"\n            },\n            \"editor.action.moveLinesDownAction\": {\n                \"label\": \"Alt+Down\",\n                \"userSettingsLabel\": \"alt+down\"\n            },\n            \"editor.action.insertCursorAbove\": {\n                \"label\": \"Alt+Cmd+Up\",\n                \"userSettingsLabel\": \"alt+cmd+up\"\n            },\n            \"editor.action.insertCursorBelow\": {\n                \"label\": \"Alt+Cmd+Down\",\n                \"userSettingsLabel\": \"alt+cmd+down\"\n            },\n            \"editor.action.insertCursorAtEndOfEachLineSelected\": {\n                \"label\": \"Shift+Alt+I\",\n                \"userSettingsLabel\": \"shift+alt+i\"\n            },\n            \"editor.action.addSelectionToNextFindMatch\": {\n                \"label\": \"Cmd+D\",\n                \"userSettingsLabel\": \"cmd+d\"\n            },\n            \"editor.action.selectHighlights\": {\n                \"label\": \"Shift+Cmd+L\",\n                \"userSettingsLabel\": \"shift+cmd+l\"\n            },\n            \"workbench.action.showCommands\": {\n                \"label\": \"Shift+Cmd+P\",\n                \"userSettingsLabel\": \"shift+cmd+p\"\n            },\n            \"workbench.action.toggleFullScreen\": {\n                \"label\": \"Ctrl+Cmd+F\",\n                \"userSettingsLabel\": \"ctrl+cmd+f\"\n            },\n            \"workbench.action.toggleSidebarVisibility\": {\n                \"label\": \"Cmd+B\",\n                \"userSettingsLabel\": \"cmd+b\"\n            },\n            \"workbench.action.toggleAuxiliaryBar\": {\n                \"label\": \"Alt+Cmd+B\",\n                \"userSettingsLabel\": \"alt+cmd+b\"\n            },\n            \"workbench.action.togglePanel\": {\n                \"label\": \"Cmd+J\",\n                \"userSettingsLabel\": \"cmd+j\"\n            },\n            \"workbench.action.zoomIn\": {\n                \"label\": \"Cmd+=\",\n                \"userSettingsLabel\": \"cmd+=\"\n            },\n            \"workbench.action.zoomOut\": {\n                \"label\": \"Cmd+-\",\n                \"userSettingsLabel\": \"cmd+-\"\n            },\n            \"workbench.action.zoomReset\": {\n                \"label\": \"⌘NumPad0\",\n                \"isNative\": false,\n                \"userSettingsLabel\": \"cmd+numpad0\"\n            },\n            \"workbench.action.toggleEditorGroupLayout\": {\n                \"label\": \"Alt+Cmd+0\",\n                \"userSettingsLabel\": \"alt+cmd+0\"\n            },\n            \"workbench.view.explorer\": {\n                \"label\": \"Shift+Cmd+E\",\n                \"userSettingsLabel\": \"shift+cmd+e\"\n            },\n            \"workbench.view.search\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.view.scm\": {\n                \"label\": \"Ctrl+Shift+G\",\n                \"userSettingsLabel\": \"ctrl+shift+g\"\n            },\n            \"workbench.view.debug\": {\n                \"label\": \"Shift+Cmd+D\",\n                \"userSettingsLabel\": \"shift+cmd+d\"\n            },\n            \"workbench.view.extensions\": {\n                \"label\": \"Shift+Cmd+X\",\n                \"userSettingsLabel\": \"shift+cmd+x\"\n            },\n            \"workbench.actions.view.problems\": {\n                \"label\": \"Shift+Cmd+M\",\n                \"userSettingsLabel\": \"shift+cmd+m\"\n            },\n            \"workbench.action.output.toggleOutput\": {\n                \"label\": \"Shift+Cmd+U\",\n                \"userSettingsLabel\": \"shift+cmd+u\"\n            },\n            \"workbench.debug.action.toggleRepl\": {\n                \"label\": \"Shift+Cmd+Y\",\n                \"userSettingsLabel\": \"shift+cmd+y\"\n            },\n            \"workbench.action.terminal.toggleTerminal\": {\n                \"label\": \"Ctrl+`\",\n                \"userSettingsLabel\": \"ctrl+`\"\n            },\n            \"editor.action.toggleWordWrap\": {\n                \"label\": \"Alt+Z\",\n                \"userSettingsLabel\": \"alt+z\"\n            },\n            \"workbench.action.navigateBack\": {\n                \"label\": \"Ctrl+-\",\n                \"userSettingsLabel\": \"ctrl+-\"\n            },\n            \"workbench.action.navigateForward\": {\n                \"label\": \"Ctrl+Shift+-\",\n                \"userSettingsLabel\": \"ctrl+shift+-\"\n            },\n            \"workbench.action.nextEditor\": {\n                \"label\": \"Alt+Cmd+Right\",\n                \"userSettingsLabel\": \"alt+cmd+right\"\n            },\n            \"workbench.action.previousEditor\": {\n                \"label\": \"Alt+Cmd+Left\",\n                \"userSettingsLabel\": \"alt+cmd+left\"\n            },\n            \"workbench.action.focusFirstEditorGroup\": {\n                \"label\": \"Cmd+1\",\n                \"userSettingsLabel\": \"cmd+1\"\n            },\n            \"workbench.action.focusSecondEditorGroup\": {\n                \"label\": \"Cmd+2\",\n                \"userSettingsLabel\": \"cmd+2\"\n            },\n            \"workbench.action.focusThirdEditorGroup\": {\n                \"label\": \"Cmd+3\",\n                \"userSettingsLabel\": \"cmd+3\"\n            },\n            \"workbench.action.focusFourthEditorGroup\": {\n                \"label\": \"Cmd+4\",\n                \"userSettingsLabel\": \"cmd+4\"\n            },\n            \"workbench.action.focusFifthEditorGroup\": {\n                \"label\": \"Cmd+5\",\n                \"userSettingsLabel\": \"cmd+5\"\n            },\n            \"workbench.action.quickOpen\": {\n                \"label\": \"Cmd+P\",\n                \"userSettingsLabel\": \"cmd+p\"\n            },\n            \"workbench.action.showAllSymbols\": {\n                \"label\": \"Cmd+T\",\n                \"userSettingsLabel\": \"cmd+t\"\n            },\n            \"workbench.action.gotoSymbol\": {\n                \"label\": \"Shift+Cmd+O\",\n                \"userSettingsLabel\": \"shift+cmd+o\"\n            },\n            \"editor.action.revealDefinition\": {\n                \"label\": \"F12\",\n                \"userSettingsLabel\": \"f12\"\n            },\n            \"editor.action.goToImplementation\": {\n                \"label\": \"Cmd+F12\",\n                \"userSettingsLabel\": \"cmd+f12\"\n            },\n            \"editor.action.goToReferences\": {\n                \"label\": \"Shift+F12\",\n                \"userSettingsLabel\": \"shift+f12\"\n            },\n            \"workbench.action.gotoLine\": {\n                \"label\": \"Ctrl+G\",\n                \"userSettingsLabel\": \"ctrl+g\"\n            },\n            \"editor.action.jumpToBracket\": {\n                \"label\": \"Shift+Cmd+\\\\\",\n                \"userSettingsLabel\": \"shift+cmd+\\\\\"\n            },\n            \"editor.action.marker.nextInFiles\": {\n                \"label\": \"F8\",\n                \"userSettingsLabel\": \"f8\"\n            },\n            \"editor.action.marker.prevInFiles\": {\n                \"label\": \"Shift+F8\",\n                \"userSettingsLabel\": \"shift+f8\"\n            },\n            \"editor.action.dirtydiff.next\": {\n                \"label\": \"Alt+F3\",\n                \"userSettingsLabel\": \"alt+f3\"\n            },\n            \"editor.action.dirtydiff.previous\": {\n                \"label\": \"Shift+Alt+F3\",\n                \"userSettingsLabel\": \"shift+alt+f3\"\n            },\n            \"workbench.action.debug.start\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"workbench.action.debug.run\": {\n                \"label\": \"Ctrl+F5\",\n                \"userSettingsLabel\": \"ctrl+f5\"\n            },\n            \"workbench.action.debug.stop\": {\n                \"label\": \"Shift+F5\",\n                \"userSettingsLabel\": \"shift+f5\"\n            },\n            \"workbench.action.debug.restart\": {\n                \"label\": \"Shift+Cmd+F5\",\n                \"userSettingsLabel\": \"shift+cmd+f5\"\n            },\n            \"workbench.action.debug.stepOver\": {\n                \"label\": \"F10\",\n                \"userSettingsLabel\": \"f10\"\n            },\n            \"workbench.action.debug.stepInto\": {\n                \"label\": \"F11\",\n                \"userSettingsLabel\": \"f11\"\n            },\n            \"workbench.action.debug.stepOut\": {\n                \"label\": \"Shift+F11\",\n                \"userSettingsLabel\": \"shift+f11\"\n            },\n            \"workbench.action.debug.continue\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"editor.debug.action.toggleBreakpoint\": {\n                \"label\": \"F9\",\n                \"userSettingsLabel\": \"f9\"\n            },\n            \"editor.debug.action.toggleInlineBreakpoint\": {\n                \"label\": \"Shift+F9\",\n                \"userSettingsLabel\": \"shift+f9\"\n            },\n            \"workbench.action.terminal.new\": {\n                \"label\": \"Ctrl+Shift+`\",\n                \"userSettingsLabel\": \"ctrl+shift+`\"\n            },\n            \"workbench.action.terminal.split\": {\n                \"label\": \"Cmd+\\\\\",\n                \"userSettingsLabel\": \"cmd+\\\\\"\n            },\n", "suffix": "            \"workbench.action.tasks.build\": {\n                \"label\": \"Shift+Cmd+B\",\n                \"userSettingsLabel\": \"shift+cmd+b\"\n            },\n            \"workbench.action.openWindsurfSettings\": {\n                \"label\": \"Cmd+,\",\n                \"userSettingsLabel\": \"cmd+,\"\n            }\n        }\n    },\n    \"theme\": \"vs-dark\",\n    \"themeBackground\": \"#1f1f1f\",\n    \"windowSplash\": {\n        \"zoomLevel\": 1,\n        \"baseTheme\": \"vs-dark\",\n        \"colorInfo\": {\n            \"foreground\": \"#cccccc\",\n            \"background\": \"#1f1f1f\",\n            \"editorBackground\": \"#1f1f1f\",\n            \"titleBarBackground\": \"#181818\",\n            \"titleBarBorder\": \"#2b2b2b\",\n            \"activityBarBackground\": \"#181818\",\n            \"activityBarBorder\": \"#2b2b2b\",\n            \"sideBarBackground\": \"#181818\",\n            \"sideBarBorder\": \"#2b2b2b\",\n            \"statusBarBackground\": \"#181818\",\n            \"statusBarBorder\": \"#2b2b2b\",\n            \"statusBarNoFolderBackground\": \"#1f1f1f\"\n        },\n        \"layoutInfo\": {\n            \"sideBarSide\": \"left\",\n            \"editorPartMinWidth\": 220,\n            \"titleBarHeight\": 35,\n            \"activityBarWidth\": 42,\n            \"sideBarWidth\": 213,\n            \"auxiliarySideBarWidth\": 523,\n            \"statusBarHeight\": 22,\n            \"windowBorder\": false\n        }\n    },\n    \"windowsState\": {\n        \"lastActiveWindow\": {\n            \"folder\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\",\n            \"backupPath\": \"/Users/<USER>/Library/Application Support/Windsurf/Backups/a159014097c8119daed6fdd2d20bad90\",\n            \"uiState\": {\n                \"mode\": 1,\n                \"x\": 33,\n                \"y\": 25,\n                \"width\": 1407,\n                \"height\": 875,\n                \"zoomLevel\": 0\n            }\n        },\n        \"openedWindows\": []\n    },\n    \"windowSplashWorkspaceOverride\": {\n        \"layoutInfo\": {\n            \"auxiliarySideBarWidth\": [\n                523,\n                [\n                    \"c59be2f483dcdc78296c3a4a55c688ec\"\n                ]\n            ]\n        }\n    },\n    \"telemetry.sessionId\": \"2034D9B9-B912-42D0-B6E7-393388AEF72A\",\n    \"telemetry.instanceId\": \"63372F8A-6B5E-45AF-BB7C-E786045FBDDB\",\n    \"telemetry.sqmUserId\": \"a137b0725136a02065862bd942e721af\",\n    \"telemetry.installationId\": \"FE358A14-E67F-4A20-B61F-42151D94EAAC\",\n    \"telemetry.firstSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"telemetry.lastSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"user.globalId\": \"E5D9C812-6AF1-41AE-8197-65B98780FCD6\",\n    \"installation.time\": \"2025-06-13T14:55:49.3NZ\",\n    \"sync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"settingsSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"userDataSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"marketplace.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"gallery.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"remote.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"tunnels.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\"\n}", "path": "User/globalStorage/storage.json", "language": "json", "prefixBegin": 0, "suffixEnd": 1}}], ["2025c1fd-a600-4f82-9db1-3fab814f4a99", {"value": {"selectedCode": "", "prefix": "{\n    \"telemetry.sqmId\": \"AuxiliaryActivityBarPositionMenu\",\n    \"telemetry.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"telemetry.devDeviceId\": \"7583883f-b598-424d-b907-67f448c00f85\",\n    \"backupWorkspaces\": {\n        \"workspaces\": [],\n        \"folders\": [\n            {\n                \"folderUri\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\"\n            }\n        ],\n        \"emptyWindows\": []\n    },\n    \"windowControlHeight\": 35,\n    \"profileAssociations\": {\n        \"workspaces\": {\n            \"file:///Users/<USER>/Library/Application%20Support/Windsurf\": \"__default__profile__\"\n        },\n        \"emptyWindows\": {}\n    },\n    \"lastKnownMenubarData\": {\n        \"menus\": {\n            \"File\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.files.newUntitledFile\",\n                        \"label\": \"&&New Text File\"\n                    },\n                    {\n                        \"id\": \"welcome.showNewFileEntries\",\n                        \"label\": \"New File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.newWindow\",\n                        \"label\": \"New &&Window\"\n                    },\n                    {\n                        \"id\": \"submenuitem.OpenProfile\",\n                        \"label\": \"New Window with Profile\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.createProfile\",\n                                    \"label\": \"New Profile...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFileFolder\",\n                        \"label\": \"&&Open...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFolder\",\n                        \"label\": \"Open &&Folder...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWorkspace\",\n                        \"label\": \"Open Wor&&kspace from File...\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarRecentMenu\",\n                        \"label\": \"Open &&Recent\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.reopenClosedEditor\",\n                                    \"label\": \"&&Reopen Closed Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"openRecentFolder\",\n                                    \"uri\": {\n                                        \"$mid\": 1,\n                                        \"path\": \"/Users/<USER>/Library/Application Support/Windsurf\",\n                                        \"scheme\": \"file\"\n                                    },\n                                    \"enabled\": true,\n                                    \"label\": \"~/Library/Application Support/Windsurf\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openRecent\",\n                                    \"label\": \"&&More...\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.clearRecentFiles\",\n                                    \"label\": \"&&Clear Recently Opened...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"addRootFolder\",\n                        \"label\": \"A&&dd Folder to Workspace...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.saveWorkspaceAs\",\n                        \"label\": \"Save Workspace As...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.duplicateWorkspaceInNewWindow\",\n                        \"label\": \"Duplicate Workspace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.save\",\n                        \"label\": \"&&Save\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.saveAs\",\n                        \"label\": \"Save &&As...\"\n                    },\n                    {\n                        \"id\": \"saveAll\",\n                        \"label\": \"Save A&&ll\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarShare\",\n                        \"label\": \"Share\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.exportProfile\",\n                                    \"label\": \"Export Profile (Default)...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleAutoSave\",\n                        \"label\": \"A&&uto Save\",\n                        \"checked\": true\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.revert\",\n                        \"label\": \"Re&&vert File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeActiveEditor\",\n                        \"label\": \"&&Close Editor\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeFolder\",\n                        \"label\": \"Close &&Folder\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeWindow\",\n                        \"label\": \"Clos&&e Window\"\n                    }\n                ]\n            },\n            \"Edit\": {\n                \"items\": [\n                    {\n                        \"id\": \"undo\",\n                        \"label\": \"&&Undo\"\n                    },\n                    {\n                        \"id\": \"redo\",\n                        \"label\": \"&&Redo\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCutAction\",\n                        \"label\": \"Cu&&t\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCopyAction\",\n                        \"label\": \"&&Copy\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardPasteAction\",\n                        \"label\": \"&&Paste\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"actions.find\",\n                        \"label\": \"&&Find\"\n                    },\n                    {\n                        \"id\": \"editor.action.startFindReplaceAction\",\n                        \"label\": \"&&Replace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.findInFiles\",\n                        \"label\": \"Find &&in Files\"\n                    },\n                    {\n                        \"id\": \"workbench.action.replaceInFiles\",\n                        \"label\": \"Replace in Files\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.commentLine\",\n                        \"label\": \"&&Toggle Line Comment\"\n                    },\n                    {\n                        \"id\": \"editor.action.blockComment\",\n                        \"label\": \"Toggle &&Block Comment\"\n                    },\n                    {\n                        \"id\": \"editor.emmet.action.expandAbbreviation\",\n                        \"label\": \"Emmet: E&&xpand Abbreviation\"\n                    }\n                ]\n            },\n            \"Selection\": {\n                \"items\": [\n                    {\n                        \"id\": \"editor.action.selectAll\",\n                        \"label\": \"&&Select All\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.expand\",\n                        \"label\": \"&&Expand Selection\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.shrink\",\n                        \"label\": \"&&Shrink Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesUpAction\",\n                        \"label\": \"&&Copy Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesDownAction\",\n                        \"label\": \"Co&&py Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesUpAction\",\n                        \"label\": \"Mo&&ve Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesDownAction\",\n                        \"label\": \"Move &&Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.duplicateSelection\",\n                        \"label\": \"&&Duplicate Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAbove\",\n                        \"label\": \"&&Add Cursor Above\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorBelow\",\n                        \"label\": \"A&&dd Cursor Below\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAtEndOfEachLineSelected\",\n                        \"label\": \"Add C&&ursors to Line Ends\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToNextFindMatch\",\n                        \"label\": \"Add &&Next Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToPreviousFindMatch\",\n                        \"label\": \"Add P&&revious Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.selectHighlights\",\n                        \"label\": \"Select All &&Occurrences\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleMultiCursorModifier\",\n                        \"label\": \"Switch to Cmd+Click for Multi-Cursor\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleColumnSelection\",\n                        \"label\": \"Column &&Selection Mode\"\n                    }\n                ]\n            },\n            \"View\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"&&Command Palette...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openView\",\n                        \"label\": \"&&Open View...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarAppearanceMenu\",\n                        \"label\": \"&&Appearance\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.toggleFullScreen\",\n                                    \"label\": \"&&Full Screen\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleZenMode\",\n                                    \"label\": \"Zen Mode\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleCenteredLayout\",\n                                    \"label\": \"&&Centered Layout\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarVisibility\",\n                                    \"label\": \"&&Primary Side Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleAuxiliaryBar\",\n                                    \"label\": \"Secondary Si&&de Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleStatusbarVisibility\",\n                                    \"label\": \"S&&tatus Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.togglePanel\",\n                                    \"label\": \"&&Panel\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarPosition\",\n                                    \"label\": \"&&Move Primary Side Bar Right\"\n                                },\n                                {\n                                    \"id\": \"submenuitem.ActivityBarPositionMenu\",\n                                    \"label\": \"Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.default\",\n                                                \"label\": \"&&Default\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.AuxiliaryActivityBarPositionMenu\",\n                                    \"label\": \"Secondary Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.default\",\n                                                \"label\": \"&&Default\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelPositionMenu\",\n                                    \"label\": \"Panel Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.positionPanelTop\",\n                                                \"label\": \"Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelRight\",\n                                                \"label\": \"Right\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelBottom\",\n                                                \"label\": \"Bottom\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelAlignmentMenu\",\n                                    \"label\": \"Align Panel\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.alignPanelCenter\",\n                                                \"label\": \"Center\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelJustify\",\n                                                \"label\": \"Justify\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelRight\",\n                                                \"label\": \"Right\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorTabsBarShowTabsSubmenu\",\n                                    \"label\": \"Tab Bar\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.showMultipleEditorTabs\",\n                                                \"label\": \"Multiple Tabs\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.showEditorTab\",\n                                                \"label\": \"Single Tab\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorTabs\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorActionsPositionSubmenu\",\n                                    \"label\": \"Editor Actions Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.editorActionsDefault\",\n                                                \"label\": \"Tab Bar\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.editorActionsTitleBar\",\n                                                \"label\": \"Title Bar\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorActions\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleMinimap\",\n                                    \"label\": \"&&Minimap\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"breadcrumbs.toggle\",\n                                    \"label\": \"Toggle &&Breadcrumbs\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleStickyScroll\",\n                                    \"label\": \"&&Sticky Scroll\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderWhitespace\",\n                                    \"label\": \"&&Render Whitespace\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderControlCharacter\",\n                                    \"label\": \"Render &&Control Characters\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomIn\",\n                                    \"label\": \"&&Zoom In\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomOut\",\n                                    \"label\": \"&&Zoom Out\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomReset\",\n                                    \"label\": \"&&Reset Zoom\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarLayoutMenu\",\n                        \"label\": \"Editor &&Layout\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.splitEditorUp\",\n                                    \"label\": \"Split &&Up\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorDown\",\n                                    \"label\": \"Split &&Down\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorLeft\",\n                                    \"label\": \"Split &&Left\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorRight\",\n                                    \"label\": \"Split &&Right\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorInGroup\",\n                                    \"label\": \"Split in &&Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.moveEditorToNewWindow\",\n                                    \"label\": \"&&Move Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.copyEditorToNewWindow\",\n                                    \"label\": \"&&Copy Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutSingle\",\n                                    \"label\": \"&&Single\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumns\",\n                                    \"label\": \"&&Two Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeColumns\",\n                                    \"label\": \"T&&hree Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRows\",\n                                    \"label\": \"T&&wo Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeRows\",\n                                    \"label\": \"Three &&Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoByTwoGrid\",\n                                    \"label\": \"&&Grid (2x2)\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRowsRight\",\n                                    \"label\": \"Two R&&ows Right\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumnsBottom\",\n                                    \"label\": \"Two &&Columns Bottom\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleEditorGroupLayout\",\n                                    \"label\": \"Flip &&Layout\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.view.explorer\",\n                        \"label\": \"&&Explorer\"\n                    },\n                    {\n                        \"id\": \"workbench.view.search\",\n                        \"label\": \"&&Search\"\n                    },\n                    {\n                        \"id\": \"workbench.view.scm\",\n                        \"label\": \"Source &&Control\"\n                    },\n                    {\n                        \"id\": \"workbench.view.debug\",\n                        \"label\": \"&&Run\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"E&&xtensions\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.actions.view.problems\",\n                        \"label\": \"&&Problems\"\n                    },\n                    {\n                        \"id\": \"workbench.action.output.toggleOutput\",\n                        \"label\": \"&&Output\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.action.toggleRepl\",\n                        \"label\": \"De&&bug Console\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.toggleTerminal\",\n                        \"label\": \"&&Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleWordWrap\",\n                        \"label\": \"&&Word Wrap\",\n                        \"checked\": true\n                    }\n                ]\n            },\n            \"Go\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.navigateBack\",\n                        \"label\": \"&&Back\"\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateForward\",\n                        \"label\": \"&&Forward\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateToLastEditLocation\",\n                        \"label\": \"&&Last Edit Location\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchEditorMenu\",\n                        \"label\": \"Switch &&Editor\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.nextEditor\",\n                                    \"label\": \"&&Next Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditor\",\n                                    \"label\": \"&&Previous Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditor\",\n                                    \"label\": \"&&Next Used Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditor\",\n                                    \"label\": \"&&Previous Used Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.nextEditorInGroup\",\n                                    \"label\": \"&&Next Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditorInGroup\",\n                                    \"label\": \"&&Previous Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Next Used Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Previous Used Editor in Group\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchGroupMenu\",\n                        \"label\": \"Switch &&Group\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.focusFirstEditorGroup\",\n                                    \"label\": \"Group &&1\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusSecondEditorGroup\",\n                                    \"label\": \"Group &&2\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusThirdEditorGroup\",\n                                    \"label\": \"Group &&3\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFourthEditorGroup\",\n                                    \"label\": \"Group &&4\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFifthEditorGroup\",\n                                    \"label\": \"Group &&5\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusNextGroup\",\n                                    \"label\": \"&&Next Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusPreviousGroup\",\n                                    \"label\": \"&&Previous Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusLeftGroup\",\n                                    \"label\": \"Group &&Left\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusRightGroup\",\n                                    \"label\": \"Group &&Right\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusAboveGroup\",\n                                    \"label\": \"Group &&Above\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusBelowGroup\",\n                                    \"label\": \"Group &&Below\",\n                                    \"enabled\": false\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.quickOpen\",\n                        \"label\": \"Go to &&File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showAllSymbols\",\n                        \"label\": \"Go to Symbol in &&Workspace...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoSymbol\",\n                        \"label\": \"Go to &&Symbol in Editor...\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDefinition\",\n                        \"label\": \"Go to &&Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDeclaration\",\n                        \"label\": \"Go to &&Declaration\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToTypeDefinition\",\n                        \"label\": \"Go to &&Type Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToImplementation\",\n                        \"label\": \"Go to &&Implementations\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToReferences\",\n                        \"label\": \"Go to &&References\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoLine\",\n                        \"label\": \"Go to &&Line/Column...\"\n                    },\n                    {\n                        \"id\": \"editor.action.jumpToBracket\",\n                        \"label\": \"Go to &&Bracket\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.nextInFiles\",\n                        \"label\": \"Next &&Problem\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.prevInFiles\",\n                        \"label\": \"Previous &&Problem\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.next\",\n                        \"label\": \"Next &&Change\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.previous\",\n                        \"label\": \"Previous &&Change\"\n                    }\n                ]\n            },\n            \"Run\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.debug.start\",\n                        \"label\": \"&&Start Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.run\",\n                        \"label\": \"Run &&Without Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stop\",\n                        \"label\": \"&&Stop Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.restart\",\n                        \"label\": \"&&Restart Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.configure\",\n                        \"label\": \"Open &&Configurations\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"debug.addConfiguration\",\n                        \"label\": \"A&&dd Configuration...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOver\",\n                        \"label\": \"Step &&Over\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepInto\",\n                        \"label\": \"Step &&Into\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOut\",\n                        \"label\": \"Step O&&ut\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.continue\",\n                        \"label\": \"&&Continue\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.debug.action.toggleBreakpoint\",\n                        \"label\": \"Toggle &&Breakpoint\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarNewBreakpointMenu\",\n                        \"label\": \"&&New Breakpoint\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"editor.debug.action.conditionalBreakpoint\",\n                                    \"label\": \"&&Conditional Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.editBreakpoint\",\n                                    \"label\": \"&&Edit Breakpoint\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.toggleInlineBreakpoint\",\n                                    \"label\": \"Inline Breakp&&oint\"\n                                },\n                                {\n                                    \"id\": \"workbench.debug.viewlet.action.addFunctionBreakpointAction\",\n                                    \"label\": \"&&Function Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.addLogPoint\",\n                                    \"label\": \"&&Logpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.triggerByBreakpoint\",\n                                    \"label\": \"&&Triggered Breakpoint...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.enableAllBreakpoints\",\n                        \"label\": \"&&Enable All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.disableAllBreakpoints\",\n                        \"label\": \"Disable A&&ll Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.removeAllBreakpoints\",\n                        \"label\": \"Remove &&All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"debug.installAdditionalDebuggers\",\n                        \"label\": \"&&Install Additional Debuggers...\"\n                    }\n                ]\n            },\n            \"Terminal\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.terminal.new\",\n                        \"label\": \"&&New Terminal\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.split\",\n                        \"label\": \"&&Split Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.runTask\",\n                        \"label\": \"&&Run Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.build\",\n                        \"label\": \"Run &&Build Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runActiveFile\",\n                        \"label\": \"Run &&Active File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runSelectedText\",\n                        \"label\": \"Run &&Selected Text\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.showTasks\",\n                        \"label\": \"Show Runnin&&g Tasks...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.restartTask\",\n                        \"label\": \"R&&estart Running Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.terminate\",\n                        \"label\": \"&&Terminate Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureTaskRunner\",\n                        \"label\": \"&&Configure Tasks...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureDefaultBuildTask\",\n                        \"label\": \"Configure De&&fault Build Task...\"\n                    }\n                ]\n            },\n            \"Help\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"Show All Commands\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showInteractivePlayground\",\n                        \"label\": \"Editor Playgrou&&nd\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openLicenseUrl\",\n                        \"label\": \"View &&License\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleDevTools\",\n                        \"label\": \"Toggle Developer Tools\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openProcessExplorer\",\n                        \"label\": \"Open &&Process Explorer\"\n                    }\n                ]\n            },\n            \"Preferences\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.openSettings\",\n                        \"label\": \"&&Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWindsurfSettings\",\n                        \"label\": \"Windsurf Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"&&Extensions\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openGlobalKeybindings\",\n                        \"label\": \"Keyboard Shortcuts\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openSnippets\",\n                        \"label\": \"Configure Snippets\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.openUserTasks\",\n                        \"label\": \"Tasks\"\n                    },\n                    {\n                        \"id\": \"submenuitem.ThemesSubMenu\",\n                        \"label\": \"&&Themes\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.selectTheme\",\n                                    \"label\": \"Color Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectIconTheme\",\n                                    \"label\": \"File Icon Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectProductIconTheme\",\n                                    \"label\": \"Product Icon Theme\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"settings.filterByOnline\",\n                        \"label\": \"&&Online Services Settings\"\n                    }\n                ]\n            }\n        },\n        \"keybindings\": {\n            \"workbench.action.quit\": {\n                \"label\": \"Cmd+Q\",\n                \"userSettingsLabel\": \"cmd+q\"\n            },\n            \"workbench.action.files.newUntitledFile\": {\n                \"label\": \"Cmd+N\",\n                \"userSettingsLabel\": \"cmd+n\"\n            },\n            \"welcome.showNewFileEntries\": {\n                \"label\": \"Ctrl+Alt+Cmd+N\",\n                \"userSettingsLabel\": \"ctrl+alt+cmd+n\"\n            },\n            \"workbench.action.newWindow\": {\n                \"label\": \"Shift+Cmd+N\",\n                \"userSettingsLabel\": \"shift+cmd+n\"\n            },\n            \"workbench.action.files.openFileFolder\": {\n                \"label\": \"Cmd+O\",\n                \"userSettingsLabel\": \"cmd+o\"\n            },\n            \"workbench.action.reopenClosedEditor\": {\n                \"label\": \"Shift+Cmd+T\",\n                \"userSettingsLabel\": \"shift+cmd+t\"\n            },\n            \"workbench.action.openRecent\": {\n                \"label\": \"Ctrl+R\",\n                \"userSettingsLabel\": \"ctrl+r\"\n            },\n            \"workbench.action.files.save\": {\n                \"label\": \"Cmd+S\",\n                \"userSettingsLabel\": \"cmd+s\"\n            },\n            \"workbench.action.files.saveAs\": {\n                \"label\": \"Shift+Cmd+S\",\n                \"userSettingsLabel\": \"shift+cmd+s\"\n            },\n            \"saveAll\": {\n                \"label\": \"Alt+Cmd+S\",\n                \"userSettingsLabel\": \"alt+cmd+s\"\n            },\n            \"workbench.action.closeActiveEditor\": {\n                \"label\": \"Cmd+W\",\n                \"userSettingsLabel\": \"cmd+w\"\n            },\n            \"workbench.action.closeWindow\": {\n                \"label\": \"Shift+Cmd+W\",\n                \"userSettingsLabel\": \"shift+cmd+w\"\n            },\n            \"undo\": {\n                \"label\": \"Cmd+Z\",\n                \"userSettingsLabel\": \"cmd+z\"\n            },\n            \"redo\": {\n                \"label\": \"Shift+Cmd+Z\",\n                \"userSettingsLabel\": \"shift+cmd+z\"\n            },\n            \"editor.action.clipboardCutAction\": {\n                \"label\": \"Cmd+X\",\n                \"userSettingsLabel\": \"cmd+x\"\n            },\n            \"editor.action.clipboardCopyAction\": {\n                \"label\": \"Cmd+C\",\n                \"userSettingsLabel\": \"cmd+c\"\n            },\n            \"editor.action.clipboardPasteAction\": {\n                \"label\": \"Cmd+V\",\n                \"userSettingsLabel\": \"cmd+v\"\n            },\n            \"actions.find\": {\n                \"label\": \"Cmd+F\",\n                \"userSettingsLabel\": \"cmd+f\"\n            },\n            \"editor.action.startFindReplaceAction\": {\n                \"label\": \"Alt+Cmd+F\",\n                \"userSettingsLabel\": \"alt+cmd+f\"\n            },\n            \"workbench.action.findInFiles\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.action.replaceInFiles\": {\n                \"label\": \"Shift+Cmd+H\",\n                \"userSettingsLabel\": \"shift+cmd+h\"\n            },\n            \"editor.action.commentLine\": {\n                \"label\": \"Cmd+/\",\n                \"userSettingsLabel\": \"cmd+/\"\n            },\n            \"editor.action.blockComment\": {\n                \"label\": \"Shift+Alt+A\",\n                \"userSettingsLabel\": \"shift+alt+a\"\n            },\n            \"editor.emmet.action.expandAbbreviation\": {\n                \"label\": \"Tab\",\n                \"userSettingsLabel\": \"tab\"\n            },\n            \"editor.action.selectAll\": {\n                \"label\": \"Cmd+A\",\n                \"userSettingsLabel\": \"cmd+a\"\n            },\n            \"editor.action.smartSelect.expand\": {\n                \"label\": \"Ctrl+Shift+Cmd+Right\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+right\"\n            },\n            \"editor.action.smartSelect.shrink\": {\n                \"label\": \"Ctrl+Shift+Cmd+Left\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+left\"\n            },\n            \"editor.action.copyLinesUpAction\": {\n                \"label\": \"Shift+Alt+Up\",\n                \"userSettingsLabel\": \"shift+alt+up\"\n            },\n            \"editor.action.copyLinesDownAction\": {\n                \"label\": \"Shift+Alt+Down\",\n                \"userSettingsLabel\": \"shift+alt+down\"\n            },\n            \"editor.action.moveLinesUpAction\": {\n                \"label\": \"Alt+Up\",\n                \"userSettingsLabel\": \"alt+up\"\n            },\n            \"editor.action.moveLinesDownAction\": {\n                \"label\": \"Alt+Down\",\n                \"userSettingsLabel\": \"alt+down\"\n            },\n            \"editor.action.insertCursorAbove\": {\n                \"label\": \"Alt+Cmd+Up\",\n                \"userSettingsLabel\": \"alt+cmd+up\"\n            },\n            \"editor.action.insertCursorBelow\": {\n                \"label\": \"Alt+Cmd+Down\",\n                \"userSettingsLabel\": \"alt+cmd+down\"\n            },\n            \"editor.action.insertCursorAtEndOfEachLineSelected\": {\n                \"label\": \"Shift+Alt+I\",\n                \"userSettingsLabel\": \"shift+alt+i\"\n            },\n            \"editor.action.addSelectionToNextFindMatch\": {\n                \"label\": \"Cmd+D\",\n                \"userSettingsLabel\": \"cmd+d\"\n            },\n            \"editor.action.selectHighlights\": {\n                \"label\": \"Shift+Cmd+L\",\n                \"userSettingsLabel\": \"shift+cmd+l\"\n            },\n            \"workbench.action.showCommands\": {\n                \"label\": \"Shift+Cmd+P\",\n                \"userSettingsLabel\": \"shift+cmd+p\"\n            },\n            \"workbench.action.toggleFullScreen\": {\n                \"label\": \"Ctrl+Cmd+F\",\n                \"userSettingsLabel\": \"ctrl+cmd+f\"\n            },\n            \"workbench.action.toggleSidebarVisibility\": {\n                \"label\": \"Cmd+B\",\n                \"userSettingsLabel\": \"cmd+b\"\n            },\n            \"workbench.action.toggleAuxiliaryBar\": {\n                \"label\": \"Alt+Cmd+B\",\n                \"userSettingsLabel\": \"alt+cmd+b\"\n            },\n            \"workbench.action.togglePanel\": {\n                \"label\": \"Cmd+J\",\n                \"userSettingsLabel\": \"cmd+j\"\n            },\n            \"workbench.action.zoomIn\": {\n                \"label\": \"Cmd+=\",\n                \"userSettingsLabel\": \"cmd+=\"\n            },\n            \"workbench.action.zoomOut\": {\n                \"label\": \"Cmd+-\",\n                \"userSettingsLabel\": \"cmd+-\"\n            },\n            \"workbench.action.zoomReset\": {\n                \"label\": \"⌘NumPad0\",\n                \"isNative\": false,\n                \"userSettingsLabel\": \"cmd+numpad0\"\n            },\n            \"workbench.action.toggleEditorGroupLayout\": {\n                \"label\": \"Alt+Cmd+0\",\n                \"userSettingsLabel\": \"alt+cmd+0\"\n            },\n            \"workbench.view.explorer\": {\n                \"label\": \"Shift+Cmd+E\",\n                \"userSettingsLabel\": \"shift+cmd+e\"\n            },\n            \"workbench.view.search\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.view.scm\": {\n                \"label\": \"Ctrl+Shift+G\",\n                \"userSettingsLabel\": \"ctrl+shift+g\"\n            },\n            \"workbench.view.debug\": {\n                \"label\": \"Shift+Cmd+D\",\n                \"userSettingsLabel\": \"shift+cmd+d\"\n            },\n            \"workbench.view.extensions\": {\n                \"label\": \"Shift+Cmd+X\",\n                \"userSettingsLabel\": \"shift+cmd+x\"\n            },\n            \"workbench.actions.view.problems\": {\n                \"label\": \"Shift+Cmd+M\",\n                \"userSettingsLabel\": \"shift+cmd+m\"\n            },\n            \"workbench.action.output.toggleOutput\": {\n                \"label\": \"Shift+Cmd+U\",\n                \"userSettingsLabel\": \"shift+cmd+u\"\n            },\n            \"workbench.debug.action.toggleRepl\": {\n                \"label\": \"Shift+Cmd+Y\",\n                \"userSettingsLabel\": \"shift+cmd+y\"\n            },\n            \"editor.action.toggleWordWrap\": {\n                \"label\": \"Alt+Z\",\n                \"userSettingsLabel\": \"alt+z\"\n            },\n            \"workbench.action.navigateBack\": {\n                \"label\": \"Ctrl+-\",\n                \"userSettingsLabel\": \"ctrl+-\"\n            },\n            \"workbench.action.navigateForward\": {\n                \"label\": \"Ctrl+Shift+-\",\n                \"userSettingsLabel\": \"ctrl+shift+-\"\n            },\n            \"workbench.action.nextEditor\": {\n                \"label\": \"Alt+Cmd+Right\",\n                \"userSettingsLabel\": \"alt+cmd+right\"\n            },\n            \"workbench.action.previousEditor\": {\n                \"label\": \"Alt+Cmd+Left\",\n                \"userSettingsLabel\": \"alt+cmd+left\"\n            },\n            \"workbench.action.focusFirstEditorGroup\": {\n                \"label\": \"Cmd+1\",\n                \"userSettingsLabel\": \"cmd+1\"\n            },\n            \"workbench.action.focusSecondEditorGroup\": {\n                \"label\": \"Cmd+2\",\n                \"userSettingsLabel\": \"cmd+2\"\n            },\n            \"workbench.action.focusThirdEditorGroup\": {\n                \"label\": \"Cmd+3\",\n                \"userSettingsLabel\": \"cmd+3\"\n            },\n            \"workbench.action.focusFourthEditorGroup\": {\n                \"label\": \"Cmd+4\",\n                \"userSettingsLabel\": \"cmd+4\"\n            },\n            \"workbench.action.focusFifthEditorGroup\": {\n                \"label\": \"Cmd+5\",\n                \"userSettingsLabel\": \"cmd+5\"\n            },\n            \"workbench.action.quickOpen\": {\n                \"label\": \"Cmd+P\",\n                \"userSettingsLabel\": \"cmd+p\"\n            },\n            \"workbench.action.showAllSymbols\": {\n                \"label\": \"Cmd+T\",\n                \"userSettingsLabel\": \"cmd+t\"\n            },\n            \"workbench.action.gotoSymbol\": {\n                \"label\": \"Shift+Cmd+O\",\n                \"userSettingsLabel\": \"shift+cmd+o\"\n            },\n            \"editor.action.revealDefinition\": {\n                \"label\": \"F12\",\n                \"userSettingsLabel\": \"f12\"\n            },\n            \"editor.action.goToImplementation\": {\n                \"label\": \"Cmd+F12\",\n                \"userSettingsLabel\": \"cmd+f12\"\n            },\n            \"editor.action.goToReferences\": {\n                \"label\": \"Shift+F12\",\n                \"userSettingsLabel\": \"shift+f12\"\n            },\n            \"workbench.action.gotoLine\": {\n                \"label\": \"Ctrl+G\",\n                \"userSettingsLabel\": \"ctrl+g\"\n            },\n            \"editor.action.marker.nextInFiles\": {\n                \"label\": \"F8\",\n                \"userSettingsLabel\": \"f8\"\n            },\n            \"editor.action.marker.prevInFiles\": {\n                \"label\": \"Shift+F8\",\n                \"userSettingsLabel\": \"shift+f8\"\n            },\n            \"editor.action.dirtydiff.next\": {\n                \"label\": \"Alt+F3\",\n                \"userSettingsLabel\": \"alt+f3\"\n            },\n            \"editor.action.dirtydiff.previous\": {\n                \"label\": \"Shift+Alt+F3\",\n                \"userSettingsLabel\": \"shift+alt+f3\"\n            },\n            \"workbench.action.debug.start\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"workbench.action.debug.run\": {\n                \"label\": \"Ctrl+F5\",\n                \"userSettingsLabel\": \"ctrl+f5\"\n            },\n            \"workbench.action.debug.stop\": {\n                \"label\": \"Shift+F5\",\n                \"userSettingsLabel\": \"shift+f5\"\n            },\n            \"workbench.action.debug.restart\": {\n                \"label\": \"Shift+Cmd+F5\",\n                \"userSettingsLabel\": \"shift+cmd+f5\"\n            },\n            \"workbench.action.debug.stepOver\": {\n                \"label\": \"F10\",\n                \"userSettingsLabel\": \"f10\"\n            },\n            \"workbench.action.debug.stepInto\": {\n                \"label\": \"F11\",\n                \"userSettingsLabel\": \"f11\"\n            },\n            \"workbench.action.debug.stepOut\": {\n                \"label\": \"Shift+F11\",\n                \"userSettingsLabel\": \"shift+f11\"\n            },\n            \"workbench.action.debug.continue\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"editor.debug.action.toggleBreakpoint\": {\n                \"label\": \"F9\",\n                \"userSettingsLabel\": \"f9\"\n            },\n            \"editor.debug.action.toggleInlineBreakpoint\": {\n                \"label\": \"Shift+F9\",\n                \"userSettingsLabel\": \"shift+f9\"\n            },\n            \"workbench.action.terminal.split\": {\n                \"label\": \"Ctrl+Shift+5\",\n                \"userSettingsLabel\": \"ctrl+shift+5\"\n            },\n", "suffix": "            \"workbench.action.tasks.build\": {\n                \"label\": \"Shift+Cmd+B\",\n                \"userSettingsLabel\": \"shift+cmd+b\"\n            },\n            \"workbench.action.openWindsurfSettings\": {\n                \"label\": \"Cmd+,\",\n                \"userSettingsLabel\": \"cmd+,\"\n            }\n        }\n    },\n    \"theme\": \"vs-dark\",\n    \"themeBackground\": \"#1f1f1f\",\n    \"windowSplash\": {\n        \"zoomLevel\": 1,\n        \"baseTheme\": \"vs-dark\",\n        \"colorInfo\": {\n            \"foreground\": \"#cccccc\",\n            \"background\": \"#1f1f1f\",\n            \"editorBackground\": \"#1f1f1f\",\n            \"titleBarBackground\": \"#181818\",\n            \"titleBarBorder\": \"#2b2b2b\",\n            \"activityBarBackground\": \"#181818\",\n            \"activityBarBorder\": \"#2b2b2b\",\n            \"sideBarBackground\": \"#181818\",\n            \"sideBarBorder\": \"#2b2b2b\",\n            \"statusBarBackground\": \"#181818\",\n            \"statusBarBorder\": \"#2b2b2b\",\n            \"statusBarNoFolderBackground\": \"#1f1f1f\"\n        },\n        \"layoutInfo\": {\n            \"sideBarSide\": \"left\",\n            \"editorPartMinWidth\": 220,\n            \"titleBarHeight\": 35,\n            \"activityBarWidth\": 42,\n            \"sideBarWidth\": 213,\n            \"auxiliarySideBarWidth\": 523,\n            \"statusBarHeight\": 22,\n            \"windowBorder\": false\n        }\n    },\n    \"windowsState\": {\n        \"lastActiveWindow\": {\n            \"folder\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\",\n            \"backupPath\": \"/Users/<USER>/Library/Application Support/Windsurf/Backups/a159014097c8119daed6fdd2d20bad90\",\n            \"uiState\": {\n                \"mode\": 1,\n                \"x\": 33,\n                \"y\": 25,\n                \"width\": 1407,\n                \"height\": 875,\n                \"zoomLevel\": 0\n            }\n        },\n        \"openedWindows\": []\n    },\n    \"windowSplashWorkspaceOverride\": {\n        \"layoutInfo\": {\n            \"auxiliarySideBarWidth\": [\n                523,\n                [\n                    \"c59be2f483dcdc78296c3a4a55c688ec\"\n                ]\n            ]\n        }\n    },\n    \"telemetry.sessionId\": \"2034D9B9-B912-42D0-B6E7-393388AEF72A\",\n    \"telemetry.instanceId\": \"63372F8A-6B5E-45AF-BB7C-E786045FBDDB\",\n    \"telemetry.sqmUserId\": \"a137b0725136a02065862bd942e721af\",\n    \"telemetry.installationId\": \"FE358A14-E67F-4A20-B61F-42151D94EAAC\",\n    \"telemetry.firstSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"telemetry.lastSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"user.globalId\": \"E5D9C812-6AF1-41AE-8197-65B98780FCD6\",\n    \"installation.time\": \"2025-06-13T14:55:49.3NZ\",\n    \"sync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"settingsSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"userDataSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"marketplace.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"gallery.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"remote.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"tunnels.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\"\n}", "path": "User/globalStorage/storage.json", "language": "json", "prefixBegin": 0, "suffixEnd": 1}}], ["868f91e8-5e0b-45af-ad29-a6d8bd581ba5", {"value": {"selectedCode": "", "prefix": "{\n    \"telemetry.sqmId\": \"AuxiliaryActivityBarPositionMenu\",\n    \"telemetry.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"telemetry.devDeviceId\": \"7583883f-b598-424d-b907-67f448c00f85\",\n    \"backupWorkspaces\": {\n        \"workspaces\": [],\n        \"folders\": [\n            {\n                \"folderUri\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\"\n            }\n        ],\n        \"emptyWindows\": []\n    },\n    \"windowControlHeight\": 35,\n    \"profileAssociations\": {\n        \"workspaces\": {\n            \"file:///Users/<USER>/Library/Application%20Support/Windsurf\": \"__default__profile__\"\n        },\n        \"emptyWindows\": {}\n    },\n    \"lastKnownMenubarData\": {\n        \"menus\": {\n            \"File\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.files.newUntitledFile\",\n                        \"label\": \"&&New Text File\"\n                    },\n                    {\n                        \"id\": \"welcome.showNewFileEntries\",\n                        \"label\": \"New File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.newWindow\",\n                        \"label\": \"New &&Window\"\n                    },\n                    {\n                        \"id\": \"submenuitem.OpenProfile\",\n                        \"label\": \"New Window with Profile\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.createProfile\",\n                                    \"label\": \"New Profile...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFileFolder\",\n                        \"label\": \"&&Open...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFolder\",\n                        \"label\": \"Open &&Folder...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWorkspace\",\n                        \"label\": \"Open Wor&&kspace from File...\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarRecentMenu\",\n                        \"label\": \"Open &&Recent\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.reopenClosedEditor\",\n                                    \"label\": \"&&Reopen Closed Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"openRecentFolder\",\n                                    \"uri\": {\n                                        \"$mid\": 1,\n                                        \"path\": \"/Users/<USER>/Library/Application Support/Windsurf\",\n                                        \"scheme\": \"file\"\n                                    },\n                                    \"enabled\": true,\n                                    \"label\": \"~/Library/Application Support/Windsurf\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openRecent\",\n                                    \"label\": \"&&More...\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.clearRecentFiles\",\n                                    \"label\": \"&&Clear Recently Opened...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"addRootFolder\",\n                        \"label\": \"A&&dd Folder to Workspace...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.saveWorkspaceAs\",\n                        \"label\": \"Save Workspace As...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.duplicateWorkspaceInNewWindow\",\n                        \"label\": \"Duplicate Workspace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.save\",\n                        \"label\": \"&&Save\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.saveAs\",\n                        \"label\": \"Save &&As...\"\n                    },\n                    {\n                        \"id\": \"saveAll\",\n                        \"label\": \"Save A&&ll\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarShare\",\n                        \"label\": \"Share\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.exportProfile\",\n                                    \"label\": \"Export Profile (Default)...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleAutoSave\",\n                        \"label\": \"A&&uto Save\",\n                        \"checked\": true\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.revert\",\n                        \"label\": \"Re&&vert File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeActiveEditor\",\n                        \"label\": \"&&Close Editor\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeFolder\",\n                        \"label\": \"Close &&Folder\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeWindow\",\n                        \"label\": \"Clos&&e Window\"\n                    }\n                ]\n            },\n            \"Edit\": {\n                \"items\": [\n                    {\n                        \"id\": \"undo\",\n                        \"label\": \"&&Undo\"\n                    },\n                    {\n                        \"id\": \"redo\",\n                        \"label\": \"&&Redo\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCutAction\",\n                        \"label\": \"Cu&&t\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCopyAction\",\n                        \"label\": \"&&Copy\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardPasteAction\",\n                        \"label\": \"&&Paste\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"actions.find\",\n                        \"label\": \"&&Find\"\n                    },\n                    {\n                        \"id\": \"editor.action.startFindReplaceAction\",\n                        \"label\": \"&&Replace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.findInFiles\",\n                        \"label\": \"Find &&in Files\"\n                    },\n                    {\n                        \"id\": \"workbench.action.replaceInFiles\",\n                        \"label\": \"Replace in Files\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.commentLine\",\n                        \"label\": \"&&Toggle Line Comment\"\n                    },\n                    {\n                        \"id\": \"editor.action.blockComment\",\n                        \"label\": \"Toggle &&Block Comment\"\n                    },\n                    {\n                        \"id\": \"editor.emmet.action.expandAbbreviation\",\n                        \"label\": \"Emmet: E&&xpand Abbreviation\"\n                    }\n                ]\n            },\n            \"Selection\": {\n                \"items\": [\n                    {\n                        \"id\": \"editor.action.selectAll\",\n                        \"label\": \"&&Select All\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.expand\",\n                        \"label\": \"&&Expand Selection\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.shrink\",\n                        \"label\": \"&&Shrink Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesUpAction\",\n                        \"label\": \"&&Copy Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesDownAction\",\n                        \"label\": \"Co&&py Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesUpAction\",\n                        \"label\": \"Mo&&ve Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesDownAction\",\n                        \"label\": \"Move &&Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.duplicateSelection\",\n                        \"label\": \"&&Duplicate Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAbove\",\n                        \"label\": \"&&Add Cursor Above\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorBelow\",\n                        \"label\": \"A&&dd Cursor Below\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAtEndOfEachLineSelected\",\n                        \"label\": \"Add C&&ursors to Line Ends\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToNextFindMatch\",\n                        \"label\": \"Add &&Next Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToPreviousFindMatch\",\n                        \"label\": \"Add P&&revious Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.selectHighlights\",\n                        \"label\": \"Select All &&Occurrences\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleMultiCursorModifier\",\n                        \"label\": \"Switch to Cmd+Click for Multi-Cursor\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleColumnSelection\",\n                        \"label\": \"Column &&Selection Mode\"\n                    }\n                ]\n            },\n            \"View\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"&&Command Palette...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openView\",\n                        \"label\": \"&&Open View...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarAppearanceMenu\",\n                        \"label\": \"&&Appearance\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.toggleFullScreen\",\n                                    \"label\": \"&&Full Screen\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleZenMode\",\n                                    \"label\": \"Zen Mode\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleCenteredLayout\",\n                                    \"label\": \"&&Centered Layout\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarVisibility\",\n                                    \"label\": \"&&Primary Side Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleAuxiliaryBar\",\n                                    \"label\": \"Secondary Si&&de Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleStatusbarVisibility\",\n                                    \"label\": \"S&&tatus Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.togglePanel\",\n                                    \"label\": \"&&Panel\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarPosition\",\n                                    \"label\": \"&&Move Primary Side Bar Right\"\n                                },\n                                {\n                                    \"id\": \"submenuitem.ActivityBarPositionMenu\",\n                                    \"label\": \"Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.default\",\n                                                \"label\": \"&&Default\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.AuxiliaryActivityBarPositionMenu\",\n                                    \"label\": \"Secondary Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.default\",\n                                                \"label\": \"&&Default\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelPositionMenu\",\n                                    \"label\": \"Panel Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.positionPanelTop\",\n                                                \"label\": \"Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelRight\",\n                                                \"label\": \"Right\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelBottom\",\n                                                \"label\": \"Bottom\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelAlignmentMenu\",\n                                    \"label\": \"Align Panel\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.alignPanelCenter\",\n                                                \"label\": \"Center\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelJustify\",\n                                                \"label\": \"Justify\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelRight\",\n                                                \"label\": \"Right\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorTabsBarShowTabsSubmenu\",\n                                    \"label\": \"Tab Bar\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.showMultipleEditorTabs\",\n                                                \"label\": \"Multiple Tabs\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.showEditorTab\",\n                                                \"label\": \"Single Tab\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorTabs\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorActionsPositionSubmenu\",\n                                    \"label\": \"Editor Actions Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.editorActionsDefault\",\n                                                \"label\": \"Tab Bar\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.editorActionsTitleBar\",\n                                                \"label\": \"Title Bar\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorActions\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleMinimap\",\n                                    \"label\": \"&&Minimap\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"breadcrumbs.toggle\",\n                                    \"label\": \"Toggle &&Breadcrumbs\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleStickyScroll\",\n                                    \"label\": \"&&Sticky Scroll\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderWhitespace\",\n                                    \"label\": \"&&Render Whitespace\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderControlCharacter\",\n                                    \"label\": \"Render &&Control Characters\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomIn\",\n                                    \"label\": \"&&Zoom In\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomOut\",\n                                    \"label\": \"&&Zoom Out\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomReset\",\n                                    \"label\": \"&&Reset Zoom\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarLayoutMenu\",\n                        \"label\": \"Editor &&Layout\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.splitEditorUp\",\n                                    \"label\": \"Split &&Up\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorDown\",\n                                    \"label\": \"Split &&Down\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorLeft\",\n                                    \"label\": \"Split &&Left\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorRight\",\n                                    \"label\": \"Split &&Right\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorInGroup\",\n                                    \"label\": \"Split in &&Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.moveEditorToNewWindow\",\n                                    \"label\": \"&&Move Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.copyEditorToNewWindow\",\n                                    \"label\": \"&&Copy Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutSingle\",\n                                    \"label\": \"&&Single\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumns\",\n                                    \"label\": \"&&Two Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeColumns\",\n                                    \"label\": \"T&&hree Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRows\",\n                                    \"label\": \"T&&wo Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeRows\",\n                                    \"label\": \"Three &&Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoByTwoGrid\",\n                                    \"label\": \"&&Grid (2x2)\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRowsRight\",\n                                    \"label\": \"Two R&&ows Right\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumnsBottom\",\n                                    \"label\": \"Two &&Columns Bottom\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleEditorGroupLayout\",\n                                    \"label\": \"Flip &&Layout\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.view.explorer\",\n                        \"label\": \"&&Explorer\"\n                    },\n                    {\n                        \"id\": \"workbench.view.search\",\n                        \"label\": \"&&Search\"\n                    },\n                    {\n                        \"id\": \"workbench.view.scm\",\n                        \"label\": \"Source &&Control\"\n                    },\n                    {\n                        \"id\": \"workbench.view.debug\",\n                        \"label\": \"&&Run\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"E&&xtensions\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.actions.view.problems\",\n                        \"label\": \"&&Problems\"\n                    },\n                    {\n                        \"id\": \"workbench.action.output.toggleOutput\",\n                        \"label\": \"&&Output\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.action.toggleRepl\",\n                        \"label\": \"De&&bug Console\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.toggleTerminal\",\n                        \"label\": \"&&Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleWordWrap\",\n                        \"label\": \"&&Word Wrap\",\n                        \"checked\": true\n                    }\n                ]\n            },\n            \"Go\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.navigateBack\",\n                        \"label\": \"&&Back\"\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateForward\",\n                        \"label\": \"&&Forward\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateToLastEditLocation\",\n                        \"label\": \"&&Last Edit Location\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchEditorMenu\",\n                        \"label\": \"Switch &&Editor\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.nextEditor\",\n                                    \"label\": \"&&Next Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditor\",\n                                    \"label\": \"&&Previous Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditor\",\n                                    \"label\": \"&&Next Used Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditor\",\n                                    \"label\": \"&&Previous Used Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.nextEditorInGroup\",\n                                    \"label\": \"&&Next Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditorInGroup\",\n                                    \"label\": \"&&Previous Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Next Used Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Previous Used Editor in Group\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchGroupMenu\",\n                        \"label\": \"Switch &&Group\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.focusFirstEditorGroup\",\n                                    \"label\": \"Group &&1\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusSecondEditorGroup\",\n                                    \"label\": \"Group &&2\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusThirdEditorGroup\",\n                                    \"label\": \"Group &&3\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFourthEditorGroup\",\n                                    \"label\": \"Group &&4\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFifthEditorGroup\",\n                                    \"label\": \"Group &&5\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusNextGroup\",\n                                    \"label\": \"&&Next Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusPreviousGroup\",\n                                    \"label\": \"&&Previous Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusLeftGroup\",\n                                    \"label\": \"Group &&Left\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusRightGroup\",\n                                    \"label\": \"Group &&Right\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusAboveGroup\",\n                                    \"label\": \"Group &&Above\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusBelowGroup\",\n                                    \"label\": \"Group &&Below\",\n                                    \"enabled\": false\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.quickOpen\",\n                        \"label\": \"Go to &&File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showAllSymbols\",\n                        \"label\": \"Go to Symbol in &&Workspace...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoSymbol\",\n                        \"label\": \"Go to &&Symbol in Editor...\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDefinition\",\n                        \"label\": \"Go to &&Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDeclaration\",\n                        \"label\": \"Go to &&Declaration\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToTypeDefinition\",\n                        \"label\": \"Go to &&Type Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToImplementation\",\n                        \"label\": \"Go to &&Implementations\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToReferences\",\n                        \"label\": \"Go to &&References\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoLine\",\n                        \"label\": \"Go to &&Line/Column...\"\n                    },\n                    {\n                        \"id\": \"editor.action.jumpToBracket\",\n                        \"label\": \"Go to &&Bracket\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.nextInFiles\",\n                        \"label\": \"Next &&Problem\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.prevInFiles\",\n                        \"label\": \"Previous &&Problem\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.next\",\n                        \"label\": \"Next &&Change\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.previous\",\n                        \"label\": \"Previous &&Change\"\n                    }\n                ]\n            },\n            \"Run\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.debug.start\",\n                        \"label\": \"&&Start Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.run\",\n                        \"label\": \"Run &&Without Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stop\",\n                        \"label\": \"&&Stop Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.restart\",\n                        \"label\": \"&&Restart Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.configure\",\n                        \"label\": \"Open &&Configurations\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"debug.addConfiguration\",\n                        \"label\": \"A&&dd Configuration...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOver\",\n                        \"label\": \"Step &&Over\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepInto\",\n                        \"label\": \"Step &&Into\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOut\",\n                        \"label\": \"Step O&&ut\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.continue\",\n                        \"label\": \"&&Continue\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.debug.action.toggleBreakpoint\",\n                        \"label\": \"Toggle &&Breakpoint\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarNewBreakpointMenu\",\n                        \"label\": \"&&New Breakpoint\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"editor.debug.action.conditionalBreakpoint\",\n                                    \"label\": \"&&Conditional Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.editBreakpoint\",\n                                    \"label\": \"&&Edit Breakpoint\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.toggleInlineBreakpoint\",\n                                    \"label\": \"Inline Breakp&&oint\"\n                                },\n                                {\n                                    \"id\": \"workbench.debug.viewlet.action.addFunctionBreakpointAction\",\n                                    \"label\": \"&&Function Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.addLogPoint\",\n                                    \"label\": \"&&Logpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.triggerByBreakpoint\",\n                                    \"label\": \"&&Triggered Breakpoint...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.enableAllBreakpoints\",\n                        \"label\": \"&&Enable All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.disableAllBreakpoints\",\n                        \"label\": \"Disable A&&ll Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.removeAllBreakpoints\",\n                        \"label\": \"Remove &&All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"debug.installAdditionalDebuggers\",\n                        \"label\": \"&&Install Additional Debuggers...\"\n                    }\n                ]\n            },\n            \"Terminal\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.terminal.new\",\n                        \"label\": \"&&New Terminal\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.split\",\n                        \"label\": \"&&Split Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.runTask\",\n                        \"label\": \"&&Run Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.build\",\n                        \"label\": \"Run &&Build Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runActiveFile\",\n                        \"label\": \"Run &&Active File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runSelectedText\",\n                        \"label\": \"Run &&Selected Text\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.showTasks\",\n                        \"label\": \"Show Runnin&&g Tasks...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.restartTask\",\n                        \"label\": \"R&&estart Running Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.terminate\",\n                        \"label\": \"&&Terminate Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureTaskRunner\",\n                        \"label\": \"&&Configure Tasks...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureDefaultBuildTask\",\n                        \"label\": \"Configure De&&fault Build Task...\"\n                    }\n                ]\n            },\n            \"Help\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"Show All Commands\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showInteractivePlayground\",\n                        \"label\": \"Editor Playgrou&&nd\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openLicenseUrl\",\n                        \"label\": \"View &&License\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleDevTools\",\n                        \"label\": \"Toggle Developer Tools\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openProcessExplorer\",\n                        \"label\": \"Open &&Process Explorer\"\n                    }\n                ]\n            },\n            \"Preferences\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.openSettings\",\n                        \"label\": \"&&Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWindsurfSettings\",\n                        \"label\": \"Windsurf Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"&&Extensions\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openGlobalKeybindings\",\n                        \"label\": \"Keyboard Shortcuts\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openSnippets\",\n                        \"label\": \"Configure Snippets\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.openUserTasks\",\n                        \"label\": \"Tasks\"\n                    },\n                    {\n                        \"id\": \"submenuitem.ThemesSubMenu\",\n                        \"label\": \"&&Themes\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.selectTheme\",\n                                    \"label\": \"Color Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectIconTheme\",\n                                    \"label\": \"File Icon Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectProductIconTheme\",\n                                    \"label\": \"Product Icon Theme\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"settings.filterByOnline\",\n                        \"label\": \"&&Online Services Settings\"\n                    }\n                ]\n            }\n        },\n        \"keybindings\": {\n            \"workbench.action.quit\": {\n                \"label\": \"Cmd+Q\",\n                \"userSettingsLabel\": \"cmd+q\"\n            },\n            \"workbench.action.files.newUntitledFile\": {\n                \"label\": \"Cmd+N\",\n                \"userSettingsLabel\": \"cmd+n\"\n            },\n            \"welcome.showNewFileEntries\": {\n                \"label\": \"Ctrl+Alt+Cmd+N\",\n                \"userSettingsLabel\": \"ctrl+alt+cmd+n\"\n            },\n            \"workbench.action.newWindow\": {\n                \"label\": \"Shift+Cmd+N\",\n                \"userSettingsLabel\": \"shift+cmd+n\"\n            },\n            \"workbench.action.files.openFileFolder\": {\n                \"label\": \"Cmd+O\",\n                \"userSettingsLabel\": \"cmd+o\"\n            },\n            \"workbench.action.reopenClosedEditor\": {\n                \"label\": \"Shift+Cmd+T\",\n                \"userSettingsLabel\": \"shift+cmd+t\"\n            },\n            \"workbench.action.openRecent\": {\n                \"label\": \"Ctrl+R\",\n                \"userSettingsLabel\": \"ctrl+r\"\n            },\n            \"workbench.action.files.save\": {\n                \"label\": \"Cmd+S\",\n                \"userSettingsLabel\": \"cmd+s\"\n            },\n            \"workbench.action.files.saveAs\": {\n                \"label\": \"Shift+Cmd+S\",\n                \"userSettingsLabel\": \"shift+cmd+s\"\n            },\n            \"saveAll\": {\n                \"label\": \"Alt+Cmd+S\",\n                \"userSettingsLabel\": \"alt+cmd+s\"\n            },\n            \"workbench.action.closeActiveEditor\": {\n                \"label\": \"Cmd+W\",\n                \"userSettingsLabel\": \"cmd+w\"\n            },\n            \"workbench.action.closeWindow\": {\n                \"label\": \"Shift+Cmd+W\",\n                \"userSettingsLabel\": \"shift+cmd+w\"\n            },\n            \"undo\": {\n                \"label\": \"Cmd+Z\",\n                \"userSettingsLabel\": \"cmd+z\"\n            },\n            \"redo\": {\n                \"label\": \"Shift+Cmd+Z\",\n                \"userSettingsLabel\": \"shift+cmd+z\"\n            },\n            \"editor.action.clipboardCutAction\": {\n                \"label\": \"Cmd+X\",\n                \"userSettingsLabel\": \"cmd+x\"\n            },\n            \"editor.action.clipboardCopyAction\": {\n                \"label\": \"Cmd+C\",\n                \"userSettingsLabel\": \"cmd+c\"\n            },\n            \"editor.action.clipboardPasteAction\": {\n                \"label\": \"Cmd+V\",\n                \"userSettingsLabel\": \"cmd+v\"\n            },\n            \"actions.find\": {\n                \"label\": \"Cmd+F\",\n                \"userSettingsLabel\": \"cmd+f\"\n            },\n            \"editor.action.startFindReplaceAction\": {\n                \"label\": \"Alt+Cmd+F\",\n                \"userSettingsLabel\": \"alt+cmd+f\"\n            },\n            \"workbench.action.findInFiles\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.action.replaceInFiles\": {\n                \"label\": \"Shift+Cmd+H\",\n                \"userSettingsLabel\": \"shift+cmd+h\"\n            },\n            \"editor.action.commentLine\": {\n                \"label\": \"Cmd+/\",\n                \"userSettingsLabel\": \"cmd+/\"\n            },\n            \"editor.action.blockComment\": {\n                \"label\": \"Shift+Alt+A\",\n                \"userSettingsLabel\": \"shift+alt+a\"\n            },\n            \"editor.emmet.action.expandAbbreviation\": {\n                \"label\": \"Tab\",\n                \"userSettingsLabel\": \"tab\"\n            },\n            \"editor.action.selectAll\": {\n                \"label\": \"Cmd+A\",\n                \"userSettingsLabel\": \"cmd+a\"\n            },\n            \"editor.action.smartSelect.expand\": {\n                \"label\": \"Ctrl+Shift+Cmd+Right\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+right\"\n            },\n            \"editor.action.smartSelect.shrink\": {\n                \"label\": \"Ctrl+Shift+Cmd+Left\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+left\"\n            },\n            \"editor.action.copyLinesUpAction\": {\n                \"label\": \"Shift+Alt+Up\",\n                \"userSettingsLabel\": \"shift+alt+up\"\n            },\n            \"editor.action.copyLinesDownAction\": {\n                \"label\": \"Shift+Alt+Down\",\n                \"userSettingsLabel\": \"shift+alt+down\"\n            },\n            \"editor.action.moveLinesUpAction\": {\n                \"label\": \"Alt+Up\",\n                \"userSettingsLabel\": \"alt+up\"\n            },\n            \"editor.action.moveLinesDownAction\": {\n                \"label\": \"Alt+Down\",\n                \"userSettingsLabel\": \"alt+down\"\n            },\n            \"editor.action.insertCursorAbove\": {\n                \"label\": \"Alt+Cmd+Up\",\n                \"userSettingsLabel\": \"alt+cmd+up\"\n            },\n            \"editor.action.insertCursorBelow\": {\n                \"label\": \"Alt+Cmd+Down\",\n                \"userSettingsLabel\": \"alt+cmd+down\"\n            },\n            \"editor.action.insertCursorAtEndOfEachLineSelected\": {\n                \"label\": \"Shift+Alt+I\",\n                \"userSettingsLabel\": \"shift+alt+i\"\n            },\n            \"editor.action.addSelectionToNextFindMatch\": {\n                \"label\": \"Cmd+D\",\n                \"userSettingsLabel\": \"cmd+d\"\n            },\n            \"editor.action.selectHighlights\": {\n                \"label\": \"Shift+Cmd+L\",\n                \"userSettingsLabel\": \"shift+cmd+l\"\n            },\n            \"workbench.action.showCommands\": {\n                \"label\": \"Shift+Cmd+P\",\n                \"userSettingsLabel\": \"shift+cmd+p\"\n            },\n            \"workbench.action.toggleFullScreen\": {\n                \"label\": \"Ctrl+Cmd+F\",\n                \"userSettingsLabel\": \"ctrl+cmd+f\"\n            },\n            \"workbench.action.toggleSidebarVisibility\": {\n                \"label\": \"Cmd+B\",\n                \"userSettingsLabel\": \"cmd+b\"\n            },\n            \"workbench.action.toggleAuxiliaryBar\": {\n                \"label\": \"Alt+Cmd+B\",\n                \"userSettingsLabel\": \"alt+cmd+b\"\n            },\n            \"workbench.action.togglePanel\": {\n                \"label\": \"Cmd+J\",\n                \"userSettingsLabel\": \"cmd+j\"\n            },\n            \"workbench.action.zoomIn\": {\n                \"label\": \"Cmd+=\",\n                \"userSettingsLabel\": \"cmd+=\"\n            },\n            \"workbench.action.zoomOut\": {\n                \"label\": \"Cmd+-\",\n                \"userSettingsLabel\": \"cmd+-\"\n            },\n            \"workbench.action.zoomReset\": {\n                \"label\": \"⌘NumPad0\",\n                \"isNative\": false,\n                \"userSettingsLabel\": \"cmd+numpad0\"\n            },\n            \"workbench.action.toggleEditorGroupLayout\": {\n                \"label\": \"Alt+Cmd+0\",\n                \"userSettingsLabel\": \"alt+cmd+0\"\n            },\n            \"workbench.view.explorer\": {\n                \"label\": \"Shift+Cmd+E\",\n                \"userSettingsLabel\": \"shift+cmd+e\"\n            },\n            \"workbench.view.search\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.view.scm\": {\n                \"label\": \"Ctrl+Shift+G\",\n                \"userSettingsLabel\": \"ctrl+shift+g\"\n            },\n            \"workbench.view.debug\": {\n                \"label\": \"Shift+Cmd+D\",\n                \"userSettingsLabel\": \"shift+cmd+d\"\n            },\n            \"workbench.view.extensions\": {\n                \"label\": \"Shift+Cmd+X\",\n                \"userSettingsLabel\": \"shift+cmd+x\"\n            },\n            \"workbench.actions.view.problems\": {\n                \"label\": \"Shift+Cmd+M\",\n                \"userSettingsLabel\": \"shift+cmd+m\"\n            },\n            \"workbench.action.output.toggleOutput\": {\n                \"label\": \"Shift+Cmd+U\",\n                \"userSettingsLabel\": \"shift+cmd+u\"\n            },\n            \"workbench.debug.action.toggleRepl\": {\n                \"label\": \"Shift+Cmd+Y\",\n                \"userSettingsLabel\": \"shift+cmd+y\"\n            },\n            \"workbench.action.terminal.toggleTerminal\": {\n                \"label\": \"Ctrl+`\",\n                \"userSettingsLabel\": \"ctrl+`\"\n            },\n            \"editor.action.toggleWordWrap\": {\n                \"label\": \"Alt+Z\",\n                \"userSettingsLabel\": \"alt+z\"\n            },\n            \"workbench.action.navigateBack\": {\n                \"label\": \"Ctrl+-\",\n                \"userSettingsLabel\": \"ctrl+-\"\n            },\n            \"workbench.action.navigateForward\": {\n                \"label\": \"Ctrl+Shift+-\",\n                \"userSettingsLabel\": \"ctrl+shift+-\"\n            },\n            \"workbench.action.nextEditor\": {\n                \"label\": \"Alt+Cmd+Right\",\n                \"userSettingsLabel\": \"alt+cmd+right\"\n            },\n            \"workbench.action.previousEditor\": {\n                \"label\": \"Alt+Cmd+Left\",\n                \"userSettingsLabel\": \"alt+cmd+left\"\n            },\n            \"workbench.action.focusFirstEditorGroup\": {\n                \"label\": \"Cmd+1\",\n                \"userSettingsLabel\": \"cmd+1\"\n            },\n            \"workbench.action.focusSecondEditorGroup\": {\n                \"label\": \"Cmd+2\",\n                \"userSettingsLabel\": \"cmd+2\"\n            },\n            \"workbench.action.focusThirdEditorGroup\": {\n                \"label\": \"Cmd+3\",\n                \"userSettingsLabel\": \"cmd+3\"\n            },\n            \"workbench.action.focusFourthEditorGroup\": {\n                \"label\": \"Cmd+4\",\n                \"userSettingsLabel\": \"cmd+4\"\n            },\n            \"workbench.action.focusFifthEditorGroup\": {\n                \"label\": \"Cmd+5\",\n                \"userSettingsLabel\": \"cmd+5\"\n            },\n            \"workbench.action.quickOpen\": {\n                \"label\": \"Cmd+P\",\n                \"userSettingsLabel\": \"cmd+p\"\n            },\n            \"workbench.action.showAllSymbols\": {\n                \"label\": \"Cmd+T\",\n                \"userSettingsLabel\": \"cmd+t\"\n            },\n            \"workbench.action.gotoSymbol\": {\n                \"label\": \"Shift+Cmd+O\",\n                \"userSettingsLabel\": \"shift+cmd+o\"\n            },\n            \"editor.action.revealDefinition\": {\n                \"label\": \"F12\",\n                \"userSettingsLabel\": \"f12\"\n            },\n            \"editor.action.goToImplementation\": {\n                \"label\": \"Cmd+F12\",\n                \"userSettingsLabel\": \"cmd+f12\"\n            },\n            \"editor.action.goToReferences\": {\n                \"label\": \"Shift+F12\",\n                \"userSettingsLabel\": \"shift+f12\"\n            },\n            \"workbench.action.gotoLine\": {\n                \"label\": \"Ctrl+G\",\n                \"userSettingsLabel\": \"ctrl+g\"\n            },\n            \"editor.action.jumpToBracket\": {\n                \"label\": \"Shift+Cmd+\\\\\",\n                \"userSettingsLabel\": \"shift+cmd+\\\\\"\n            },\n            \"editor.action.marker.nextInFiles\": {\n                \"label\": \"F8\",\n                \"userSettingsLabel\": \"f8\"\n            },\n            \"editor.action.marker.prevInFiles\": {\n                \"label\": \"Shift+F8\",\n                \"userSettingsLabel\": \"shift+f8\"\n            },\n            \"editor.action.dirtydiff.next\": {\n                \"label\": \"Alt+F3\",\n                \"userSettingsLabel\": \"alt+f3\"\n            },\n            \"editor.action.dirtydiff.previous\": {\n                \"label\": \"Shift+Alt+F3\",\n                \"userSettingsLabel\": \"shift+alt+f3\"\n            },\n            \"workbench.action.debug.start\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"workbench.action.debug.run\": {\n                \"label\": \"Ctrl+F5\",\n                \"userSettingsLabel\": \"ctrl+f5\"\n            },\n            \"workbench.action.debug.stop\": {\n                \"label\": \"Shift+F5\",\n                \"userSettingsLabel\": \"shift+f5\"\n            },\n            \"workbench.action.debug.restart\": {\n                \"label\": \"Shift+Cmd+F5\",\n                \"userSettingsLabel\": \"shift+cmd+f5\"\n            },\n            \"workbench.action.debug.stepOver\": {\n                \"label\": \"F10\",\n                \"userSettingsLabel\": \"f10\"\n            },\n            \"workbench.action.debug.stepInto\": {\n                \"label\": \"F11\",\n                \"userSettingsLabel\": \"f11\"\n            },\n            \"workbench.action.debug.stepOut\": {\n                \"label\": \"Shift+F11\",\n                \"userSettingsLabel\": \"shift+f11\"\n            },\n            \"workbench.action.debug.continue\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"editor.debug.action.toggleBreakpoint\": {\n                \"label\": \"F9\",\n                \"userSettingsLabel\": \"f9\"\n            },\n            \"editor.debug.action.toggleInlineBreakpoint\": {\n                \"label\": \"Shift+F9\",\n                \"userSettingsLabel\": \"shift+f9\"\n            },\n            \"workbench.action.terminal.new\": {\n                \"label\": \"Ctrl+Shift+`\",\n                \"userSettingsLabel\": \"ctrl+shift+`\"\n            },\n            \"workbench.action.terminal.split\": {\n                \"label\": \"Cmd+\\\\\",\n                \"userSettingsLabel\": \"cmd+\\\\\"\n            },\n", "suffix": "            \"workbench.action.tasks.build\": {\n                \"label\": \"Shift+Cmd+B\",\n                \"userSettingsLabel\": \"shift+cmd+b\"\n            },\n            \"workbench.action.openWindsurfSettings\": {\n                \"label\": \"Cmd+,\",\n                \"userSettingsLabel\": \"cmd+,\"\n            }\n        }\n    },\n    \"theme\": \"vs-dark\",\n    \"themeBackground\": \"#1f1f1f\",\n    \"windowSplash\": {\n        \"zoomLevel\": 1,\n        \"baseTheme\": \"vs-dark\",\n        \"colorInfo\": {\n            \"foreground\": \"#cccccc\",\n            \"background\": \"#1f1f1f\",\n            \"editorBackground\": \"#1f1f1f\",\n            \"titleBarBackground\": \"#181818\",\n            \"titleBarBorder\": \"#2b2b2b\",\n            \"activityBarBackground\": \"#181818\",\n            \"activityBarBorder\": \"#2b2b2b\",\n            \"sideBarBackground\": \"#181818\",\n            \"sideBarBorder\": \"#2b2b2b\",\n            \"statusBarBackground\": \"#181818\",\n            \"statusBarBorder\": \"#2b2b2b\",\n            \"statusBarNoFolderBackground\": \"#1f1f1f\"\n        },\n        \"layoutInfo\": {\n            \"sideBarSide\": \"left\",\n            \"editorPartMinWidth\": 220,\n            \"titleBarHeight\": 35,\n            \"activityBarWidth\": 42,\n            \"sideBarWidth\": 213,\n            \"auxiliarySideBarWidth\": 523,\n            \"statusBarHeight\": 22,\n            \"windowBorder\": false\n        }\n    },\n    \"windowsState\": {\n        \"lastActiveWindow\": {\n            \"folder\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\",\n            \"backupPath\": \"/Users/<USER>/Library/Application Support/Windsurf/Backups/a159014097c8119daed6fdd2d20bad90\",\n            \"uiState\": {\n                \"mode\": 1,\n                \"x\": 33,\n                \"y\": 25,\n                \"width\": 1407,\n                \"height\": 875,\n                \"zoomLevel\": 0\n            }\n        },\n        \"openedWindows\": []\n    },\n    \"windowSplashWorkspaceOverride\": {\n        \"layoutInfo\": {\n            \"auxiliarySideBarWidth\": [\n                523,\n                [\n                    \"c59be2f483dcdc78296c3a4a55c688ec\"\n                ]\n            ]\n        }\n    },\n    \"telemetry.sessionId\": \"2034D9B9-B912-42D0-B6E7-393388AEF72A\",\n    \"telemetry.instanceId\": \"63372F8A-6B5E-45AF-BB7C-E786045FBDDB\",\n    \"telemetry.sqmUserId\": \"a137b0725136a02065862bd942e721af\",\n    \"telemetry.installationId\": \"FE358A14-E67F-4A20-B61F-42151D94EAAC\",\n    \"telemetry.firstSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"telemetry.lastSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"user.globalId\": \"E5D9C812-6AF1-41AE-8197-65B98780FCD6\",\n    \"installation.time\": \"2025-06-13T14:55:49.3NZ\",\n    \"sync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"settingsSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"userDataSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"marketplace.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"gallery.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"remote.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"tunnels.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\"\n}", "path": "User/globalStorage/storage.json", "language": "json", "prefixBegin": 0, "suffixEnd": 1}}], ["8dd8f6cd-d272-4de8-88e5-ca2d9c815340", {"value": {"selectedCode": "", "prefix": "{\n    \"telemetry.sqmId\": \"AuxiliaryActivityBarPositionMenu\",\n    \"telemetry.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"telemetry.devDeviceId\": \"7583883f-b598-424d-b907-67f448c00f85\",\n    \"backupWorkspaces\": {\n        \"workspaces\": [],\n        \"folders\": [\n            {\n                \"folderUri\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\"\n            }\n        ],\n        \"emptyWindows\": []\n    },\n    \"windowControlHeight\": 35,\n    \"profileAssociations\": {\n        \"workspaces\": {\n            \"file:///Users/<USER>/Library/Application%20Support/Windsurf\": \"__default__profile__\"\n        },\n        \"emptyWindows\": {}\n    },\n    \"lastKnownMenubarData\": {\n        \"menus\": {\n            \"File\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.files.newUntitledFile\",\n                        \"label\": \"&&New Text File\"\n                    },\n                    {\n                        \"id\": \"welcome.showNewFileEntries\",\n                        \"label\": \"New File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.newWindow\",\n                        \"label\": \"New &&Window\"\n                    },\n                    {\n                        \"id\": \"submenuitem.OpenProfile\",\n                        \"label\": \"New Window with Profile\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.createProfile\",\n                                    \"label\": \"New Profile...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFileFolder\",\n                        \"label\": \"&&Open...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.openFolder\",\n                        \"label\": \"Open &&Folder...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWorkspace\",\n                        \"label\": \"Open Wor&&kspace from File...\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarRecentMenu\",\n                        \"label\": \"Open &&Recent\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.reopenClosedEditor\",\n                                    \"label\": \"&&Reopen Closed Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"openRecentFolder\",\n                                    \"uri\": {\n                                        \"$mid\": 1,\n                                        \"path\": \"/Users/<USER>/Library/Application Support/Windsurf\",\n                                        \"scheme\": \"file\"\n                                    },\n                                    \"enabled\": true,\n                                    \"label\": \"~/Library/Application Support/Windsurf\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openRecent\",\n                                    \"label\": \"&&More...\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.clearRecentFiles\",\n                                    \"label\": \"&&Clear Recently Opened...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"addRootFolder\",\n                        \"label\": \"A&&dd Folder to Workspace...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.saveWorkspaceAs\",\n                        \"label\": \"Save Workspace As...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.duplicateWorkspaceInNewWindow\",\n                        \"label\": \"Duplicate Workspace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.save\",\n                        \"label\": \"&&Save\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.saveAs\",\n                        \"label\": \"Save &&As...\"\n                    },\n                    {\n                        \"id\": \"saveAll\",\n                        \"label\": \"Save A&&ll\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarShare\",\n                        \"label\": \"Share\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.profiles.actions.exportProfile\",\n                                    \"label\": \"Export Profile (Default)...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleAutoSave\",\n                        \"label\": \"A&&uto Save\",\n                        \"checked\": true\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.files.revert\",\n                        \"label\": \"Re&&vert File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeActiveEditor\",\n                        \"label\": \"&&Close Editor\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeFolder\",\n                        \"label\": \"Close &&Folder\"\n                    },\n                    {\n                        \"id\": \"workbench.action.closeWindow\",\n                        \"label\": \"Clos&&e Window\"\n                    }\n                ]\n            },\n            \"Edit\": {\n                \"items\": [\n                    {\n                        \"id\": \"undo\",\n                        \"label\": \"&&Undo\"\n                    },\n                    {\n                        \"id\": \"redo\",\n                        \"label\": \"&&Redo\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCutAction\",\n                        \"label\": \"Cu&&t\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardCopyAction\",\n                        \"label\": \"&&Copy\"\n                    },\n                    {\n                        \"id\": \"editor.action.clipboardPasteAction\",\n                        \"label\": \"&&Paste\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"actions.find\",\n                        \"label\": \"&&Find\"\n                    },\n                    {\n                        \"id\": \"editor.action.startFindReplaceAction\",\n                        \"label\": \"&&Replace\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.findInFiles\",\n                        \"label\": \"Find &&in Files\"\n                    },\n                    {\n                        \"id\": \"workbench.action.replaceInFiles\",\n                        \"label\": \"Replace in Files\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.commentLine\",\n                        \"label\": \"&&Toggle Line Comment\"\n                    },\n                    {\n                        \"id\": \"editor.action.blockComment\",\n                        \"label\": \"Toggle &&Block Comment\"\n                    },\n                    {\n                        \"id\": \"editor.emmet.action.expandAbbreviation\",\n                        \"label\": \"Emmet: E&&xpand Abbreviation\"\n                    }\n                ]\n            },\n            \"Selection\": {\n                \"items\": [\n                    {\n                        \"id\": \"editor.action.selectAll\",\n                        \"label\": \"&&Select All\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.expand\",\n                        \"label\": \"&&Expand Selection\"\n                    },\n                    {\n                        \"id\": \"editor.action.smartSelect.shrink\",\n                        \"label\": \"&&Shrink Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesUpAction\",\n                        \"label\": \"&&Copy Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.copyLinesDownAction\",\n                        \"label\": \"Co&&py Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesUpAction\",\n                        \"label\": \"Mo&&ve Line Up\"\n                    },\n                    {\n                        \"id\": \"editor.action.moveLinesDownAction\",\n                        \"label\": \"Move &&Line Down\"\n                    },\n                    {\n                        \"id\": \"editor.action.duplicateSelection\",\n                        \"label\": \"&&Duplicate Selection\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAbove\",\n                        \"label\": \"&&Add Cursor Above\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorBelow\",\n                        \"label\": \"A&&dd Cursor Below\"\n                    },\n                    {\n                        \"id\": \"editor.action.insertCursorAtEndOfEachLineSelected\",\n                        \"label\": \"Add C&&ursors to Line Ends\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToNextFindMatch\",\n                        \"label\": \"Add &&Next Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.addSelectionToPreviousFindMatch\",\n                        \"label\": \"Add P&&revious Occurrence\"\n                    },\n                    {\n                        \"id\": \"editor.action.selectHighlights\",\n                        \"label\": \"Select All &&Occurrences\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleMultiCursorModifier\",\n                        \"label\": \"Switch to Cmd+Click for Multi-Cursor\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleColumnSelection\",\n                        \"label\": \"Column &&Selection Mode\"\n                    }\n                ]\n            },\n            \"View\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"&&Command Palette...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openView\",\n                        \"label\": \"&&Open View...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarAppearanceMenu\",\n                        \"label\": \"&&Appearance\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.toggleFullScreen\",\n                                    \"label\": \"&&Full Screen\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleZenMode\",\n                                    \"label\": \"Zen Mode\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleCenteredLayout\",\n                                    \"label\": \"&&Centered Layout\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarVisibility\",\n                                    \"label\": \"&&Primary Side Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleAuxiliaryBar\",\n                                    \"label\": \"Secondary Si&&de Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleStatusbarVisibility\",\n                                    \"label\": \"S&&tatus Bar\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"workbench.action.togglePanel\",\n                                    \"label\": \"&&Panel\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleSidebarPosition\",\n                                    \"label\": \"&&Move Primary Side Bar Right\"\n                                },\n                                {\n                                    \"id\": \"submenuitem.ActivityBarPositionMenu\",\n                                    \"label\": \"Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.default\",\n                                                \"label\": \"&&Default\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.activityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.AuxiliaryActivityBarPositionMenu\",\n                                    \"label\": \"Secondary Activity Bar Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.default\",\n                                                \"label\": \"&&Default\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.top\",\n                                                \"label\": \"&&Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.bottom\",\n                                                \"label\": \"&&Bottom\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.auxiliaryActivityBarLocation.hide\",\n                                                \"label\": \"&&Hidden\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelPositionMenu\",\n                                    \"label\": \"Panel Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.positionPanelTop\",\n                                                \"label\": \"Top\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelRight\",\n                                                \"label\": \"Right\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.positionPanelBottom\",\n                                                \"label\": \"Bottom\",\n                                                \"checked\": true\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.PanelAlignmentMenu\",\n                                    \"label\": \"Align Panel\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.alignPanelCenter\",\n                                                \"label\": \"Center\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelJustify\",\n                                                \"label\": \"Justify\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelLeft\",\n                                                \"label\": \"Left\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.alignPanelRight\",\n                                                \"label\": \"Right\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorTabsBarShowTabsSubmenu\",\n                                    \"label\": \"Tab Bar\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.showMultipleEditorTabs\",\n                                                \"label\": \"Multiple Tabs\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.showEditorTab\",\n                                                \"label\": \"Single Tab\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorTabs\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"submenuitem.EditorActionsPositionSubmenu\",\n                                    \"label\": \"Editor Actions Position\",\n                                    \"submenu\": {\n                                        \"items\": [\n                                            {\n                                                \"id\": \"workbench.action.editorActionsDefault\",\n                                                \"label\": \"Tab Bar\",\n                                                \"checked\": true\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.editorActionsTitleBar\",\n                                                \"label\": \"Title Bar\"\n                                            },\n                                            {\n                                                \"id\": \"workbench.action.hideEditorActions\",\n                                                \"label\": \"Hidden\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleMinimap\",\n                                    \"label\": \"&&Minimap\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"breadcrumbs.toggle\",\n                                    \"label\": \"Toggle &&Breadcrumbs\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleStickyScroll\",\n                                    \"label\": \"&&Sticky Scroll\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderWhitespace\",\n                                    \"label\": \"&&Render Whitespace\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"editor.action.toggleRenderControlCharacter\",\n                                    \"label\": \"Render &&Control Characters\",\n                                    \"checked\": true\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomIn\",\n                                    \"label\": \"&&Zoom In\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomOut\",\n                                    \"label\": \"&&Zoom Out\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.zoomReset\",\n                                    \"label\": \"&&Reset Zoom\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarLayoutMenu\",\n                        \"label\": \"Editor &&Layout\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.splitEditorUp\",\n                                    \"label\": \"Split &&Up\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorDown\",\n                                    \"label\": \"Split &&Down\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorLeft\",\n                                    \"label\": \"Split &&Left\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorRight\",\n                                    \"label\": \"Split &&Right\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.splitEditorInGroup\",\n                                    \"label\": \"Split in &&Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.moveEditorToNewWindow\",\n                                    \"label\": \"&&Move Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.copyEditorToNewWindow\",\n                                    \"label\": \"&&Copy Editor into New Window\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutSingle\",\n                                    \"label\": \"&&Single\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumns\",\n                                    \"label\": \"&&Two Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeColumns\",\n                                    \"label\": \"T&&hree Columns\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRows\",\n                                    \"label\": \"T&&wo Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutThreeRows\",\n                                    \"label\": \"Three &&Rows\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoByTwoGrid\",\n                                    \"label\": \"&&Grid (2x2)\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoRowsRight\",\n                                    \"label\": \"Two R&&ows Right\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.editorLayoutTwoColumnsBottom\",\n                                    \"label\": \"Two &&Columns Bottom\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.toggleEditorGroupLayout\",\n                                    \"label\": \"Flip &&Layout\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.view.explorer\",\n                        \"label\": \"&&Explorer\"\n                    },\n                    {\n                        \"id\": \"workbench.view.search\",\n                        \"label\": \"&&Search\"\n                    },\n                    {\n                        \"id\": \"workbench.view.scm\",\n                        \"label\": \"Source &&Control\"\n                    },\n                    {\n                        \"id\": \"workbench.view.debug\",\n                        \"label\": \"&&Run\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"E&&xtensions\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.actions.view.problems\",\n                        \"label\": \"&&Problems\"\n                    },\n                    {\n                        \"id\": \"workbench.action.output.toggleOutput\",\n                        \"label\": \"&&Output\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.action.toggleRepl\",\n                        \"label\": \"De&&bug Console\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.toggleTerminal\",\n                        \"label\": \"&&Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.toggleWordWrap\",\n                        \"label\": \"&&Word Wrap\",\n                        \"checked\": true\n                    }\n                ]\n            },\n            \"Go\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.navigateBack\",\n                        \"label\": \"&&Back\"\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateForward\",\n                        \"label\": \"&&Forward\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.navigateToLastEditLocation\",\n                        \"label\": \"&&Last Edit Location\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchEditorMenu\",\n                        \"label\": \"Switch &&Editor\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.nextEditor\",\n                                    \"label\": \"&&Next Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditor\",\n                                    \"label\": \"&&Previous Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditor\",\n                                    \"label\": \"&&Next Used Editor\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditor\",\n                                    \"label\": \"&&Previous Used Editor\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.nextEditorInGroup\",\n                                    \"label\": \"&&Next Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.previousEditorInGroup\",\n                                    \"label\": \"&&Previous Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openNextRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Next Used Editor in Group\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.openPreviousRecentlyUsedEditorInGroup\",\n                                    \"label\": \"&&Previous Used Editor in Group\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarSwitchGroupMenu\",\n                        \"label\": \"Switch &&Group\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.focusFirstEditorGroup\",\n                                    \"label\": \"Group &&1\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusSecondEditorGroup\",\n                                    \"label\": \"Group &&2\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusThirdEditorGroup\",\n                                    \"label\": \"Group &&3\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFourthEditorGroup\",\n                                    \"label\": \"Group &&4\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusFifthEditorGroup\",\n                                    \"label\": \"Group &&5\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusNextGroup\",\n                                    \"label\": \"&&Next Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusPreviousGroup\",\n                                    \"label\": \"&&Previous Group\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"vscode.menubar.separator\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusLeftGroup\",\n                                    \"label\": \"Group &&Left\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusRightGroup\",\n                                    \"label\": \"Group &&Right\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusAboveGroup\",\n                                    \"label\": \"Group &&Above\",\n                                    \"enabled\": false\n                                },\n                                {\n                                    \"id\": \"workbench.action.focusBelowGroup\",\n                                    \"label\": \"Group &&Below\",\n                                    \"enabled\": false\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.quickOpen\",\n                        \"label\": \"Go to &&File...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showAllSymbols\",\n                        \"label\": \"Go to Symbol in &&Workspace...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoSymbol\",\n                        \"label\": \"Go to &&Symbol in Editor...\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDefinition\",\n                        \"label\": \"Go to &&Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.revealDeclaration\",\n                        \"label\": \"Go to &&Declaration\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToTypeDefinition\",\n                        \"label\": \"Go to &&Type Definition\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToImplementation\",\n                        \"label\": \"Go to &&Implementations\"\n                    },\n                    {\n                        \"id\": \"editor.action.goToReferences\",\n                        \"label\": \"Go to &&References\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.gotoLine\",\n                        \"label\": \"Go to &&Line/Column...\"\n                    },\n                    {\n                        \"id\": \"editor.action.jumpToBracket\",\n                        \"label\": \"Go to &&Bracket\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.nextInFiles\",\n                        \"label\": \"Next &&Problem\"\n                    },\n                    {\n                        \"id\": \"editor.action.marker.prevInFiles\",\n                        \"label\": \"Previous &&Problem\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.next\",\n                        \"label\": \"Next &&Change\"\n                    },\n                    {\n                        \"id\": \"editor.action.dirtydiff.previous\",\n                        \"label\": \"Previous &&Change\"\n                    }\n                ]\n            },\n            \"Run\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.debug.start\",\n                        \"label\": \"&&Start Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.run\",\n                        \"label\": \"Run &&Without Debugging\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stop\",\n                        \"label\": \"&&Stop Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.restart\",\n                        \"label\": \"&&Restart Debugging\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.configure\",\n                        \"label\": \"Open &&Configurations\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"debug.addConfiguration\",\n                        \"label\": \"A&&dd Configuration...\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOver\",\n                        \"label\": \"Step &&Over\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepInto\",\n                        \"label\": \"Step &&Into\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.stepOut\",\n                        \"label\": \"Step O&&ut\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.debug.continue\",\n                        \"label\": \"&&Continue\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"editor.debug.action.toggleBreakpoint\",\n                        \"label\": \"Toggle &&Breakpoint\"\n                    },\n                    {\n                        \"id\": \"submenuitem.MenubarNewBreakpointMenu\",\n                        \"label\": \"&&New Breakpoint\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"editor.debug.action.conditionalBreakpoint\",\n                                    \"label\": \"&&Conditional Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.editBreakpoint\",\n                                    \"label\": \"&&Edit Breakpoint\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.toggleInlineBreakpoint\",\n                                    \"label\": \"Inline Breakp&&oint\"\n                                },\n                                {\n                                    \"id\": \"workbench.debug.viewlet.action.addFunctionBreakpointAction\",\n                                    \"label\": \"&&Function Breakpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.addLogPoint\",\n                                    \"label\": \"&&Logpoint...\"\n                                },\n                                {\n                                    \"id\": \"editor.debug.action.triggerByBreakpoint\",\n                                    \"label\": \"&&Triggered Breakpoint...\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.enableAllBreakpoints\",\n                        \"label\": \"&&Enable All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.disableAllBreakpoints\",\n                        \"label\": \"Disable A&&ll Breakpoints\"\n                    },\n                    {\n                        \"id\": \"workbench.debug.viewlet.action.removeAllBreakpoints\",\n                        \"label\": \"Remove &&All Breakpoints\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"debug.installAdditionalDebuggers\",\n                        \"label\": \"&&Install Additional Debuggers...\"\n                    }\n                ]\n            },\n            \"Terminal\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.terminal.new\",\n                        \"label\": \"&&New Terminal\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.split\",\n                        \"label\": \"&&Split Terminal\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.runTask\",\n                        \"label\": \"&&Run Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.build\",\n                        \"label\": \"Run &&Build Task...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runActiveFile\",\n                        \"label\": \"Run &&Active File\"\n                    },\n                    {\n                        \"id\": \"workbench.action.terminal.runSelectedText\",\n                        \"label\": \"Run &&Selected Text\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.showTasks\",\n                        \"label\": \"Show Runnin&&g Tasks...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.restartTask\",\n                        \"label\": \"R&&estart Running Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.terminate\",\n                        \"label\": \"&&Terminate Task...\",\n                        \"enabled\": false\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureTaskRunner\",\n                        \"label\": \"&&Configure Tasks...\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.configureDefaultBuildTask\",\n                        \"label\": \"Configure De&&fault Build Task...\"\n                    }\n                ]\n            },\n            \"Help\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.showCommands\",\n                        \"label\": \"Show All Commands\"\n                    },\n                    {\n                        \"id\": \"workbench.action.showInteractivePlayground\",\n                        \"label\": \"Editor Playgrou&&nd\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openLicenseUrl\",\n                        \"label\": \"View &&License\"\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"workbench.action.toggleDevTools\",\n                        \"label\": \"Toggle Developer Tools\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openProcessExplorer\",\n                        \"label\": \"Open &&Process Explorer\"\n                    }\n                ]\n            },\n            \"Preferences\": {\n                \"items\": [\n                    {\n                        \"id\": \"workbench.action.openSettings\",\n                        \"label\": \"&&Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openWindsurfSettings\",\n                        \"label\": \"Windsurf Settings\"\n                    },\n                    {\n                        \"id\": \"workbench.view.extensions\",\n                        \"label\": \"&&Extensions\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openGlobalKeybindings\",\n                        \"label\": \"Keyboard Shortcuts\"\n                    },\n                    {\n                        \"id\": \"workbench.action.openSnippets\",\n                        \"label\": \"Configure Snippets\"\n                    },\n                    {\n                        \"id\": \"workbench.action.tasks.openUserTasks\",\n                        \"label\": \"Tasks\"\n                    },\n                    {\n                        \"id\": \"submenuitem.ThemesSubMenu\",\n                        \"label\": \"&&Themes\",\n                        \"submenu\": {\n                            \"items\": [\n                                {\n                                    \"id\": \"workbench.action.selectTheme\",\n                                    \"label\": \"Color Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectIconTheme\",\n                                    \"label\": \"File Icon Theme\"\n                                },\n                                {\n                                    \"id\": \"workbench.action.selectProductIconTheme\",\n                                    \"label\": \"Product Icon Theme\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"id\": \"vscode.menubar.separator\"\n                    },\n                    {\n                        \"id\": \"settings.filterByOnline\",\n                        \"label\": \"&&Online Services Settings\"\n                    }\n                ]\n            }\n        },\n        \"keybindings\": {\n            \"workbench.action.quit\": {\n                \"label\": \"Cmd+Q\",\n                \"userSettingsLabel\": \"cmd+q\"\n            },\n            \"workbench.action.files.newUntitledFile\": {\n                \"label\": \"Cmd+N\",\n                \"userSettingsLabel\": \"cmd+n\"\n            },\n            \"welcome.showNewFileEntries\": {\n                \"label\": \"Ctrl+Alt+Cmd+N\",\n                \"userSettingsLabel\": \"ctrl+alt+cmd+n\"\n            },\n            \"workbench.action.newWindow\": {\n                \"label\": \"Shift+Cmd+N\",\n                \"userSettingsLabel\": \"shift+cmd+n\"\n            },\n            \"workbench.action.files.openFileFolder\": {\n                \"label\": \"Cmd+O\",\n                \"userSettingsLabel\": \"cmd+o\"\n            },\n            \"workbench.action.reopenClosedEditor\": {\n                \"label\": \"Shift+Cmd+T\",\n                \"userSettingsLabel\": \"shift+cmd+t\"\n            },\n            \"workbench.action.openRecent\": {\n                \"label\": \"Ctrl+R\",\n                \"userSettingsLabel\": \"ctrl+r\"\n            },\n            \"workbench.action.files.save\": {\n                \"label\": \"Cmd+S\",\n                \"userSettingsLabel\": \"cmd+s\"\n            },\n            \"workbench.action.files.saveAs\": {\n                \"label\": \"Shift+Cmd+S\",\n                \"userSettingsLabel\": \"shift+cmd+s\"\n            },\n            \"saveAll\": {\n                \"label\": \"Alt+Cmd+S\",\n                \"userSettingsLabel\": \"alt+cmd+s\"\n            },\n            \"workbench.action.closeActiveEditor\": {\n                \"label\": \"Cmd+W\",\n                \"userSettingsLabel\": \"cmd+w\"\n            },\n            \"workbench.action.closeWindow\": {\n                \"label\": \"Shift+Cmd+W\",\n                \"userSettingsLabel\": \"shift+cmd+w\"\n            },\n            \"undo\": {\n                \"label\": \"Cmd+Z\",\n                \"userSettingsLabel\": \"cmd+z\"\n            },\n            \"redo\": {\n                \"label\": \"Shift+Cmd+Z\",\n                \"userSettingsLabel\": \"shift+cmd+z\"\n            },\n            \"editor.action.clipboardCutAction\": {\n                \"label\": \"Cmd+X\",\n                \"userSettingsLabel\": \"cmd+x\"\n            },\n            \"editor.action.clipboardCopyAction\": {\n                \"label\": \"Cmd+C\",\n                \"userSettingsLabel\": \"cmd+c\"\n            },\n            \"editor.action.clipboardPasteAction\": {\n                \"label\": \"Cmd+V\",\n                \"userSettingsLabel\": \"cmd+v\"\n            },\n            \"actions.find\": {\n                \"label\": \"Cmd+F\",\n                \"userSettingsLabel\": \"cmd+f\"\n            },\n            \"editor.action.startFindReplaceAction\": {\n                \"label\": \"Alt+Cmd+F\",\n                \"userSettingsLabel\": \"alt+cmd+f\"\n            },\n            \"workbench.action.findInFiles\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.action.replaceInFiles\": {\n                \"label\": \"Shift+Cmd+H\",\n                \"userSettingsLabel\": \"shift+cmd+h\"\n            },\n            \"editor.action.commentLine\": {\n                \"label\": \"Cmd+/\",\n                \"userSettingsLabel\": \"cmd+/\"\n            },\n            \"editor.action.blockComment\": {\n                \"label\": \"Shift+Alt+A\",\n                \"userSettingsLabel\": \"shift+alt+a\"\n            },\n            \"editor.emmet.action.expandAbbreviation\": {\n                \"label\": \"Tab\",\n                \"userSettingsLabel\": \"tab\"\n            },\n            \"editor.action.selectAll\": {\n                \"label\": \"Cmd+A\",\n                \"userSettingsLabel\": \"cmd+a\"\n            },\n            \"editor.action.smartSelect.expand\": {\n                \"label\": \"Ctrl+Shift+Cmd+Right\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+right\"\n            },\n            \"editor.action.smartSelect.shrink\": {\n                \"label\": \"Ctrl+Shift+Cmd+Left\",\n                \"userSettingsLabel\": \"ctrl+shift+cmd+left\"\n            },\n            \"editor.action.copyLinesUpAction\": {\n                \"label\": \"Shift+Alt+Up\",\n                \"userSettingsLabel\": \"shift+alt+up\"\n            },\n            \"editor.action.copyLinesDownAction\": {\n                \"label\": \"Shift+Alt+Down\",\n                \"userSettingsLabel\": \"shift+alt+down\"\n            },\n            \"editor.action.moveLinesUpAction\": {\n                \"label\": \"Alt+Up\",\n                \"userSettingsLabel\": \"alt+up\"\n            },\n            \"editor.action.moveLinesDownAction\": {\n                \"label\": \"Alt+Down\",\n                \"userSettingsLabel\": \"alt+down\"\n            },\n            \"editor.action.insertCursorAbove\": {\n                \"label\": \"Alt+Cmd+Up\",\n                \"userSettingsLabel\": \"alt+cmd+up\"\n            },\n            \"editor.action.insertCursorBelow\": {\n                \"label\": \"Alt+Cmd+Down\",\n                \"userSettingsLabel\": \"alt+cmd+down\"\n            },\n            \"editor.action.insertCursorAtEndOfEachLineSelected\": {\n                \"label\": \"Shift+Alt+I\",\n                \"userSettingsLabel\": \"shift+alt+i\"\n            },\n            \"editor.action.addSelectionToNextFindMatch\": {\n                \"label\": \"Cmd+D\",\n                \"userSettingsLabel\": \"cmd+d\"\n            },\n            \"editor.action.selectHighlights\": {\n                \"label\": \"Shift+Cmd+L\",\n                \"userSettingsLabel\": \"shift+cmd+l\"\n            },\n            \"workbench.action.showCommands\": {\n                \"label\": \"Shift+Cmd+P\",\n                \"userSettingsLabel\": \"shift+cmd+p\"\n            },\n            \"workbench.action.toggleFullScreen\": {\n                \"label\": \"Ctrl+Cmd+F\",\n                \"userSettingsLabel\": \"ctrl+cmd+f\"\n            },\n            \"workbench.action.toggleSidebarVisibility\": {\n                \"label\": \"Cmd+B\",\n                \"userSettingsLabel\": \"cmd+b\"\n            },\n            \"workbench.action.toggleAuxiliaryBar\": {\n                \"label\": \"Alt+Cmd+B\",\n                \"userSettingsLabel\": \"alt+cmd+b\"\n            },\n            \"workbench.action.togglePanel\": {\n                \"label\": \"Cmd+J\",\n                \"userSettingsLabel\": \"cmd+j\"\n            },\n            \"workbench.action.zoomIn\": {\n                \"label\": \"Cmd+=\",\n                \"userSettingsLabel\": \"cmd+=\"\n            },\n            \"workbench.action.zoomOut\": {\n                \"label\": \"Cmd+-\",\n                \"userSettingsLabel\": \"cmd+-\"\n            },\n            \"workbench.action.zoomReset\": {\n                \"label\": \"⌘NumPad0\",\n                \"isNative\": false,\n                \"userSettingsLabel\": \"cmd+numpad0\"\n            },\n            \"workbench.action.toggleEditorGroupLayout\": {\n                \"label\": \"Alt+Cmd+0\",\n                \"userSettingsLabel\": \"alt+cmd+0\"\n            },\n            \"workbench.view.explorer\": {\n                \"label\": \"Shift+Cmd+E\",\n                \"userSettingsLabel\": \"shift+cmd+e\"\n            },\n            \"workbench.view.search\": {\n                \"label\": \"Shift+Cmd+F\",\n                \"userSettingsLabel\": \"shift+cmd+f\"\n            },\n            \"workbench.view.scm\": {\n                \"label\": \"Ctrl+Shift+G\",\n                \"userSettingsLabel\": \"ctrl+shift+g\"\n            },\n            \"workbench.view.debug\": {\n                \"label\": \"Shift+Cmd+D\",\n                \"userSettingsLabel\": \"shift+cmd+d\"\n            },\n            \"workbench.view.extensions\": {\n                \"label\": \"Shift+Cmd+X\",\n                \"userSettingsLabel\": \"shift+cmd+x\"\n            },\n            \"workbench.actions.view.problems\": {\n                \"label\": \"Shift+Cmd+M\",\n                \"userSettingsLabel\": \"shift+cmd+m\"\n            },\n            \"workbench.action.output.toggleOutput\": {\n                \"label\": \"Shift+Cmd+U\",\n                \"userSettingsLabel\": \"shift+cmd+u\"\n            },\n            \"workbench.debug.action.toggleRepl\": {\n                \"label\": \"Shift+Cmd+Y\",\n                \"userSettingsLabel\": \"shift+cmd+y\"\n            },\n            \"editor.action.toggleWordWrap\": {\n                \"label\": \"Alt+Z\",\n                \"userSettingsLabel\": \"alt+z\"\n            },\n            \"workbench.action.navigateBack\": {\n                \"label\": \"Ctrl+-\",\n                \"userSettingsLabel\": \"ctrl+-\"\n            },\n            \"workbench.action.navigateForward\": {\n                \"label\": \"Ctrl+Shift+-\",\n                \"userSettingsLabel\": \"ctrl+shift+-\"\n            },\n            \"workbench.action.nextEditor\": {\n                \"label\": \"Alt+Cmd+Right\",\n                \"userSettingsLabel\": \"alt+cmd+right\"\n            },\n            \"workbench.action.previousEditor\": {\n                \"label\": \"Alt+Cmd+Left\",\n                \"userSettingsLabel\": \"alt+cmd+left\"\n            },\n            \"workbench.action.focusFirstEditorGroup\": {\n                \"label\": \"Cmd+1\",\n                \"userSettingsLabel\": \"cmd+1\"\n            },\n            \"workbench.action.focusSecondEditorGroup\": {\n                \"label\": \"Cmd+2\",\n                \"userSettingsLabel\": \"cmd+2\"\n            },\n            \"workbench.action.focusThirdEditorGroup\": {\n                \"label\": \"Cmd+3\",\n                \"userSettingsLabel\": \"cmd+3\"\n            },\n            \"workbench.action.focusFourthEditorGroup\": {\n                \"label\": \"Cmd+4\",\n                \"userSettingsLabel\": \"cmd+4\"\n            },\n            \"workbench.action.focusFifthEditorGroup\": {\n                \"label\": \"Cmd+5\",\n                \"userSettingsLabel\": \"cmd+5\"\n            },\n            \"workbench.action.quickOpen\": {\n                \"label\": \"Cmd+P\",\n                \"userSettingsLabel\": \"cmd+p\"\n            },\n            \"workbench.action.showAllSymbols\": {\n                \"label\": \"Cmd+T\",\n                \"userSettingsLabel\": \"cmd+t\"\n            },\n            \"workbench.action.gotoSymbol\": {\n                \"label\": \"Shift+Cmd+O\",\n                \"userSettingsLabel\": \"shift+cmd+o\"\n            },\n            \"editor.action.revealDefinition\": {\n                \"label\": \"F12\",\n                \"userSettingsLabel\": \"f12\"\n            },\n            \"editor.action.goToImplementation\": {\n                \"label\": \"Cmd+F12\",\n                \"userSettingsLabel\": \"cmd+f12\"\n            },\n            \"editor.action.goToReferences\": {\n                \"label\": \"Shift+F12\",\n                \"userSettingsLabel\": \"shift+f12\"\n            },\n            \"workbench.action.gotoLine\": {\n                \"label\": \"Ctrl+G\",\n                \"userSettingsLabel\": \"ctrl+g\"\n            },\n            \"editor.action.marker.nextInFiles\": {\n                \"label\": \"F8\",\n                \"userSettingsLabel\": \"f8\"\n            },\n            \"editor.action.marker.prevInFiles\": {\n                \"label\": \"Shift+F8\",\n                \"userSettingsLabel\": \"shift+f8\"\n            },\n            \"editor.action.dirtydiff.next\": {\n                \"label\": \"Alt+F3\",\n                \"userSettingsLabel\": \"alt+f3\"\n            },\n            \"editor.action.dirtydiff.previous\": {\n                \"label\": \"Shift+Alt+F3\",\n                \"userSettingsLabel\": \"shift+alt+f3\"\n            },\n            \"workbench.action.debug.start\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"workbench.action.debug.run\": {\n                \"label\": \"Ctrl+F5\",\n                \"userSettingsLabel\": \"ctrl+f5\"\n            },\n            \"workbench.action.debug.stop\": {\n                \"label\": \"Shift+F5\",\n                \"userSettingsLabel\": \"shift+f5\"\n            },\n            \"workbench.action.debug.restart\": {\n                \"label\": \"Shift+Cmd+F5\",\n                \"userSettingsLabel\": \"shift+cmd+f5\"\n            },\n            \"workbench.action.debug.stepOver\": {\n                \"label\": \"F10\",\n                \"userSettingsLabel\": \"f10\"\n            },\n            \"workbench.action.debug.stepInto\": {\n                \"label\": \"F11\",\n                \"userSettingsLabel\": \"f11\"\n            },\n            \"workbench.action.debug.stepOut\": {\n                \"label\": \"Shift+F11\",\n                \"userSettingsLabel\": \"shift+f11\"\n            },\n            \"workbench.action.debug.continue\": {\n                \"label\": \"F5\",\n                \"userSettingsLabel\": \"f5\"\n            },\n            \"editor.debug.action.toggleBreakpoint\": {\n                \"label\": \"F9\",\n                \"userSettingsLabel\": \"f9\"\n            },\n            \"editor.debug.action.toggleInlineBreakpoint\": {\n                \"label\": \"Shift+F9\",\n                \"userSettingsLabel\": \"shift+f9\"\n            },\n            \"workbench.action.terminal.split\": {\n                \"label\": \"Ctrl+Shift+5\",\n                \"userSettingsLabel\": \"ctrl+shift+5\"\n            },\n", "suffix": "            \"workbench.action.tasks.build\": {\n                \"label\": \"Shift+Cmd+B\",\n                \"userSettingsLabel\": \"shift+cmd+b\"\n            },\n            \"workbench.action.openWindsurfSettings\": {\n                \"label\": \"Cmd+,\",\n                \"userSettingsLabel\": \"cmd+,\"\n            }\n        }\n    },\n    \"theme\": \"vs-dark\",\n    \"themeBackground\": \"#1f1f1f\",\n    \"windowSplash\": {\n        \"zoomLevel\": 1,\n        \"baseTheme\": \"vs-dark\",\n        \"colorInfo\": {\n            \"foreground\": \"#cccccc\",\n            \"background\": \"#1f1f1f\",\n            \"editorBackground\": \"#1f1f1f\",\n            \"titleBarBackground\": \"#181818\",\n            \"titleBarBorder\": \"#2b2b2b\",\n            \"activityBarBackground\": \"#181818\",\n            \"activityBarBorder\": \"#2b2b2b\",\n            \"sideBarBackground\": \"#181818\",\n            \"sideBarBorder\": \"#2b2b2b\",\n            \"statusBarBackground\": \"#181818\",\n            \"statusBarBorder\": \"#2b2b2b\",\n            \"statusBarNoFolderBackground\": \"#1f1f1f\"\n        },\n        \"layoutInfo\": {\n            \"sideBarSide\": \"left\",\n            \"editorPartMinWidth\": 220,\n            \"titleBarHeight\": 35,\n            \"activityBarWidth\": 42,\n            \"sideBarWidth\": 213,\n            \"auxiliarySideBarWidth\": 523,\n            \"statusBarHeight\": 22,\n            \"windowBorder\": false\n        }\n    },\n    \"windowsState\": {\n        \"lastActiveWindow\": {\n            \"folder\": \"file:///Users/<USER>/Library/Application%20Support/Windsurf\",\n            \"backupPath\": \"/Users/<USER>/Library/Application Support/Windsurf/Backups/a159014097c8119daed6fdd2d20bad90\",\n            \"uiState\": {\n                \"mode\": 1,\n                \"x\": 33,\n                \"y\": 25,\n                \"width\": 1407,\n                \"height\": 875,\n                \"zoomLevel\": 0\n            }\n        },\n        \"openedWindows\": []\n    },\n    \"windowSplashWorkspaceOverride\": {\n        \"layoutInfo\": {\n            \"auxiliarySideBarWidth\": [\n                523,\n                [\n                    \"c59be2f483dcdc78296c3a4a55c688ec\"\n                ]\n            ]\n        }\n    },\n    \"telemetry.sessionId\": \"2034D9B9-B912-42D0-B6E7-393388AEF72A\",\n    \"telemetry.instanceId\": \"63372F8A-6B5E-45AF-BB7C-E786045FBDDB\",\n    \"telemetry.sqmUserId\": \"a137b0725136a02065862bd942e721af\",\n    \"telemetry.installationId\": \"FE358A14-E67F-4A20-B61F-42151D94EAAC\",\n    \"telemetry.firstSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"telemetry.lastSessionDate\": \"2025-06-13T14:55:49.3NZ\",\n    \"user.globalId\": \"E5D9C812-6AF1-41AE-8197-65B98780FCD6\",\n    \"installation.time\": \"2025-06-13T14:55:49.3NZ\",\n    \"sync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"settingsSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"userDataSync.machineId\": \"6028937b5d9316213be41e7e53f6ce7db469c38f9b67e0928d756902a3de42ed\",\n    \"marketplace.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"gallery.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"remote.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\",\n    \"tunnels.machineId\": \"86fe5746e0299b9cd10b8431979b0be8f42d26ce534c48e70a9f7379df28a297\"\n}", "path": "User/globalStorage/storage.json", "language": "json", "prefixBegin": 0, "suffixEnd": 1}}]]