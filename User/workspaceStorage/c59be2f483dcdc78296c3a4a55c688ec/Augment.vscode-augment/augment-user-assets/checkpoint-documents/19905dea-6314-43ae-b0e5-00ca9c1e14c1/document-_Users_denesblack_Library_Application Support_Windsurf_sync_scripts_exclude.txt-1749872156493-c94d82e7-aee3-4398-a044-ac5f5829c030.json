{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/exclude.txt"}, "modifiedCode": "# 同步排除文件列表\n# 每行一个模式，支持通配符\n\n# 系统文件\n.DS_Store\nThumbs.db\ndesktop.ini\n.Spotlight-V100\n.Trashes\n.fseventsd\n.TemporaryItems\n.VolumeIcon.icns\n\n# 临时文件\n*.tmp\n*.temp\n*.swp\n*.swo\n*~\n.#*\n#*#\n\n# 日志文件\n*.log\n*.log.*\nlogs/\nlog/\n\n# 缓存目录\n.cache/\ncache/\n.npm/\n.yarn/\nnode_modules/\n__pycache__/\n*.pyc\n*.pyo\n.pytest_cache/\n\n# 版本控制\n.git/\n.svn/\n.hg/\n.bzr/\nCVS/\n\n# IDE和编辑器\n.vscode/\n.idea/\n*.sublime-*\n.atom/\n.brackets.json\n\n# 编译输出\nbuild/\ndist/\ntarget/\nbin/\nobj/\nout/\n*.o\n*.obj\n*.exe\n*.dll\n*.so\n*.dylib\n\n# 包管理器\nvendor/\nPods/\n.bundle/\n\n# 数据库文件\n*.db\n*.sqlite\n*.sqlite3\n\n# 媒体文件（可选，根据需要调整）\n# *.mp4\n# *.avi\n# *.mov\n# *.mp3\n# *.wav\n# *.jpg\n# *.jpeg\n# *.png\n# *.gif\n\n# 大文件（可选）\n# *.iso\n# *.dmg\n# *.zip\n# *.tar.gz\n# *.rar\n\n# 特定应用程序文件\n.dropbox\n.dropbox.attr\nIcon?\n.com.apple.timemachine.donotpresent\n\n# 开发环境文件\n.env\n.env.local\n.env.development\n.env.test\n.env.production\n\n# 备份文件\n*.bak\n*.backup\n*.old\n*_backup\n*_old\n"}