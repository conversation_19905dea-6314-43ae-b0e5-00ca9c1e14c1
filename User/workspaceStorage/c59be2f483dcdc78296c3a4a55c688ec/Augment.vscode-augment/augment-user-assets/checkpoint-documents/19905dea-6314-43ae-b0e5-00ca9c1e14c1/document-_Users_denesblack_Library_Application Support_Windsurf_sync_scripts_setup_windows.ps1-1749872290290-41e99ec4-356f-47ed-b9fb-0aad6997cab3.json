{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/setup_windows.ps1"}, "modifiedCode": "# Windows端同步环境安装脚本\n\nparam(\n    [switch]$Test,\n    [switch]$Help\n)\n\n$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path\n$ErrorActionPreference = \"Stop\"\n\n# 颜色输出函数\nfunction Write-ColorOutput {\n    param(\n        [string]$Message,\n        [string]$Color = \"White\"\n    )\n    \n    $colors = @{\n        \"Green\" = \"Green\"\n        \"Yellow\" = \"Yellow\" \n        \"Red\" = \"Red\"\n        \"Blue\" = \"Blue\"\n        \"White\" = \"White\"\n    }\n    \n    Write-Host $Message -ForegroundColor $colors[$Color]\n}\n\nfunction Write-Success {\n    param([string]$Message)\n    Write-ColorOutput \"✓ $Message\" \"Green\"\n}\n\nfunction Write-Warning {\n    param([string]$Message)\n    Write-ColorOutput \"⚠ $Message\" \"Yellow\"\n}\n\nfunction Write-Error {\n    param([string]$Message)\n    Write-ColorOutput \"✗ $Message\" \"Red\"\n}\n\nfunction Write-Info {\n    param([string]$Message)\n    Write-ColorOutput \"ℹ $Message\" \"Blue\"\n}\n\n# 显示帮助信息\nfunction Show-Help {\n    @\"\nWindows端文件同步环境安装脚本\n\n用法: .\\setup_windows.ps1 [参数]\n\n参数:\n    -Test    测试环境和连接\n    -Help    显示此帮助信息\n\n功能:\n    1. 检查系统要求\n    2. 安装WSL2和必要工具\n    3. 配置SSH服务\n    4. 创建配置文件\n    5. 测试连接\n\n前置要求:\n    - Windows 10 版本 2004 或更高\n    - 管理员权限\n    - 网络连接\n\n\"@\n}\n\n# 检查管理员权限\nfunction Test-AdminRights {\n    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()\n    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)\n    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)\n}\n\n# 检查Windows版本\nfunction Test-WindowsVersion {\n    $version = [System.Environment]::OSVersion.Version\n    if ($version.Major -lt 10) {\n        Write-Error \"需要Windows 10或更高版本\"\n        return $false\n    }\n    \n    $build = (Get-ItemProperty \"HKLM:SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\").CurrentBuild\n    if ([int]$build -lt 19041) {\n        Write-Warning \"建议升级到Windows 10版本2004或更高以获得最佳WSL2支持\"\n    }\n    \n    Write-Success \"Windows版本检查通过\"\n    return $true\n}\n\n# 检查WSL状态\nfunction Test-WSLStatus {\n    try {\n        $wslStatus = wsl --status 2>$null\n        if ($LASTEXITCODE -eq 0) {\n            Write-Success \"WSL已安装\"\n            return $true\n        }\n    }\n    catch {\n        Write-Info \"WSL未安装\"\n        return $false\n    }\n    return $false\n}\n\n# 安装WSL2\nfunction Install-WSL {\n    Write-Info \"正在安装WSL2...\"\n    \n    try {\n        # 启用WSL功能\n        dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart\n        dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart\n        \n        Write-Success \"WSL功能已启用\"\n        \n        # 安装WSL2\n        wsl --install --no-distribution\n        \n        Write-Success \"WSL2安装完成\"\n        Write-Warning \"需要重启系统以完成安装，重启后请重新运行此脚本\"\n        \n        $restart = Read-Host \"是否现在重启？(y/N)\"\n        if ($restart -eq 'y' -or $restart -eq 'Y') {\n            Restart-Computer -Force\n        }\n        \n        return $false\n    }\n    catch {\n        Write-Error \"WSL安装失败: $($_.Exception.Message)\"\n        return $false\n    }\n}\n\n# 安装Ubuntu发行版\nfunction Install-Ubuntu {\n    Write-Info \"检查Ubuntu发行版...\"\n    \n    $distributions = wsl --list --quiet\n    if ($distributions -contains \"Ubuntu\") {\n        Write-Success \"Ubuntu已安装\"\n        return $true\n    }\n    \n    Write-Info \"正在安装Ubuntu...\"\n    try {\n        wsl --install -d Ubuntu\n        Write-Success \"Ubuntu安装完成\"\n        Write-Info \"请按照提示设置Ubuntu用户名和密码\"\n        return $true\n    }\n    catch {\n        Write-Error \"Ubuntu安装失败: $($_.Exception.Message)\"\n        return $false\n    }\n}\n\n# 配置WSL环境\nfunction Setup-WSLEnvironment {\n    Write-Info \"配置WSL环境...\"\n    \n    $setupScript = @\"\n#!/bin/bash\nset -e\n\necho \"更新包列表...\"\nsudo apt update\n\necho \"安装必要工具...\"\nsudo apt install -y openssh-server rsync\n\necho \"配置SSH服务...\"\nsudo sed -i 's/#Port 22/Port 22/' /etc/ssh/sshd_config\nsudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config\nsudo sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config\n\necho \"启动SSH服务...\"\nsudo service ssh start\n\necho \"设置SSH服务自启动...\"\necho 'sudo service ssh start' >> ~/.bashrc\n\necho \"创建SSH目录...\"\nmkdir -p ~/.ssh\nchmod 700 ~/.ssh\n\necho \"WSL环境配置完成\"\n\"@\n    \n    $tempScript = [System.IO.Path]::GetTempFileName() + \".sh\"\n    $setupScript | Out-File -FilePath $tempScript -Encoding UTF8\n    \n    try {\n        wsl bash $tempScript\n        Remove-Item $tempScript\n        Write-Success \"WSL环境配置完成\"\n        return $true\n    }\n    catch {\n        Write-Error \"WSL环境配置失败: $($_.Exception.Message)\"\n        Remove-Item $tempScript -ErrorAction SilentlyContinue\n        return $false\n    }\n}\n\n# 创建配置文件\nfunction New-ConfigFile {\n    $configFile = Join-Path $ScriptDir \"sync_config.json\"\n    \n    if (Test-Path $configFile) {\n        $overwrite = Read-Host \"配置文件已存在，是否覆盖？(y/N)\"\n        if ($overwrite -ne 'y' -and $overwrite -ne 'Y') {\n            Write-Info \"跳过配置文件创建\"\n            return\n        }\n    }\n    \n    Write-Info \"创建配置文件...\"\n    \n    # 获取用户输入\n    $localDir = Read-Host \"本地同步目录路径 (例: C:\\Users\\<USER>\\Documents\\project)\"\n    $remoteHost = Read-Host \"Mac主机IP地址\"\n    $remoteUser = Read-Host \"Mac用户名\"\n    $remoteDir = Read-Host \"Mac端目录路径 (例: /Users/<USER>/Documents/project)\"\n    \n    # 验证本地目录\n    if (!(Test-Path $localDir)) {\n        $create = Read-Host \"本地目录不存在，是否创建？(y/N)\"\n        if ($create -eq 'y' -or $create -eq 'Y') {\n            New-Item -ItemType Directory -Path $localDir -Force | Out-Null\n            Write-Success \"本地目录创建完成\"\n        }\n    }\n    \n    # 生成配置文件\n    $config = @{\n        localDir = $localDir\n        remoteHost = $remoteHost\n        remoteUser = $remoteUser\n        remoteDir = $remoteDir\n        syncDelay = 2\n        maxRetries = 3\n        showProgress = $false\n        sshPort = 22\n        connectionTimeout = 10\n        compressLevel = 6\n        logLevel = \"INFO\"\n        logRetentionDays = 7\n        excludePatterns = @(\n            \"*.tmp\", \"*.temp\", \"~*\", \".DS_Store\", \"Thumbs.db\",\n            \"desktop.ini\", \"node_modules\\\", \".git\\\", \".vscode\\\"\n        )\n        notifications = @{\n            enabled = $true\n            onError = $true\n        }\n        backup = @{\n            enabled = $true\n            maxBackups = 5\n        }\n    }\n    \n    $config | ConvertTo-Json -Depth 3 | Out-File -FilePath $configFile -Encoding UTF8\n    Write-Success \"配置文件创建完成: $configFile\"\n}\n\n# 设置SSH密钥\nfunction Setup-SSHKey {\n    Write-Info \"设置SSH密钥...\"\n    \n    $sshDir = \"$env:USERPROFILE\\.ssh\"\n    if (!(Test-Path $sshDir)) {\n        New-Item -ItemType Directory -Path $sshDir -Force | Out-Null\n    }\n    \n    $keyFile = Join-Path $sshDir \"id_rsa\"\n    if (Test-Path $keyFile) {\n        Write-Success \"SSH密钥已存在\"\n    } else {\n        $email = Read-Host \"请输入您的邮箱地址\"\n        \n        # 在WSL中生成密钥\n        wsl ssh-keygen -t rsa -b 4096 -C \"$email\" -f \"/mnt/c/Users/<USER>/.ssh/id_rsa\" -N '\"\"'\n        Write-Success \"SSH密钥生成完成\"\n    }\n    \n    # 显示公钥\n    Write-Info \"您的SSH公钥：\"\n    Write-Host \"----------------------------------------\" -ForegroundColor Yellow\n    Get-Content \"$keyFile.pub\"\n    Write-Host \"----------------------------------------\" -ForegroundColor Yellow\n    Write-Warning \"请将上述公钥添加到Mac端的~/.ssh/authorized_keys文件中\"\n}\n\n# 测试连接\nfunction Test-Connection {\n    Write-Info \"测试连接...\"\n    \n    $configFile = Join-Path $ScriptDir \"sync_config.json\"\n    if (!(Test-Path $configFile)) {\n        Write-Error \"配置文件不存在，请先运行安装\"\n        return $false\n    }\n    \n    $config = Get-Content $configFile | ConvertFrom-Json\n    \n    # 测试SSH连接\n    try {\n        $testCmd = \"ssh -o ConnectTimeout=10 -o BatchMode=yes $($config.remoteUser)@$($config.remoteHost) 'echo SSH连接成功'\"\n        $result = wsl bash -c $testCmd\n        \n        if ($LASTEXITCODE -eq 0) {\n            Write-Success \"SSH连接测试成功\"\n        } else {\n            Write-Error \"SSH连接失败\"\n            return $false\n        }\n    }\n    catch {\n        Write-Error \"SSH连接测试异常: $($_.Exception.Message)\"\n        return $false\n    }\n    \n    # 测试远程目录\n    try {\n        $testDirCmd = \"ssh $($config.remoteUser)@$($config.remoteHost) 'test -d `\"$($config.remoteDir)`\"'\"\n        wsl bash -c $testDirCmd\n        \n        if ($LASTEXITCODE -eq 0) {\n            Write-Success \"远程目录访问成功\"\n        } else {\n            Write-Warning \"远程目录不存在，尝试创建...\"\n            $createDirCmd = \"ssh $($config.remoteUser)@$($config.remoteHost) 'mkdir -p `\"$($config.remoteDir)`\"'\"\n            wsl bash -c $createDirCmd\n            \n            if ($LASTEXITCODE -eq 0) {\n                Write-Success \"远程目录创建成功\"\n            } else {\n                Write-Error \"远程目录创建失败\"\n                return $false\n            }\n        }\n    }\n    catch {\n        Write-Error \"远程目录测试异常: $($_.Exception.Message)\"\n        return $false\n    }\n    \n    return $true\n}\n\n# 创建启动脚本\nfunction New-LaunchScript {\n    $launchScript = Join-Path $ScriptDir \"start_sync.ps1\"\n    \n    $scriptContent = @\"\n# Windows端同步服务启动脚本\n\n`$ScriptDir = Split-Path -Parent `$MyInvocation.MyCommand.Path\n`$SyncScript = Join-Path `$ScriptDir \"windows_realtime_sync.ps1\"\n\nif (!(Test-Path `$SyncScript)) {\n    Write-Error \"同步脚本不存在: `$SyncScript\"\n    exit 1\n}\n\nWrite-Host \"启动实时同步服务...\" -ForegroundColor Green\n& `$SyncScript @args\n\"@\n    \n    $scriptContent | Out-File -FilePath $launchScript -Encoding UTF8\n    Write-Success \"启动脚本创建完成: $launchScript\"\n}\n\n# 显示完成信息\nfunction Show-Completion {\n    Write-Success \"Windows端安装完成！\"\n    Write-Host \"\"\n    Write-Info \"下一步操作：\"\n    Write-Host \"1. 将SSH公钥添加到Mac端的~/.ssh/authorized_keys文件\"\n    Write-Host \"2. 运行测试: .\\setup_windows.ps1 -Test\"\n    Write-Host \"3. 启动同步: .\\start_sync.ps1\"\n    Write-Host \"\"\n    Write-Info \"配置文件位置: $ScriptDir\\sync_config.json\"\n    Write-Info \"日志文件位置: $ScriptDir\\logs\\\"\n}\n\n# 主函数\nfunction Main {\n    if ($Help) {\n        Show-Help\n        return\n    }\n    \n    if ($Test) {\n        Test-Connection\n        return\n    }\n    \n    Write-Info \"开始安装Windows端文件同步环境...\"\n    \n    # 检查管理员权限\n    if (!(Test-AdminRights)) {\n        Write-Error \"需要管理员权限，请以管理员身份运行PowerShell\"\n        return\n    }\n    \n    # 检查Windows版本\n    if (!(Test-WindowsVersion)) {\n        return\n    }\n    \n    # 检查并安装WSL\n    if (!(Test-WSLStatus)) {\n        if (!(Install-WSL)) {\n            return\n        }\n    }\n    \n    # 安装Ubuntu\n    if (!(Install-Ubuntu)) {\n        return\n    }\n    \n    # 配置WSL环境\n    if (!(Setup-WSLEnvironment)) {\n        return\n    }\n    \n    # 创建配置文件\n    New-ConfigFile\n    \n    # 设置SSH密钥\n    Setup-SSHKey\n    \n    # 创建启动脚本\n    New-LaunchScript\n    \n    # 显示完成信息\n    Show-Completion\n}\n\n# 运行主函数\nMain\n"}