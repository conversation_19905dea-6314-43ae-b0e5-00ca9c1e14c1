{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/speed_optimized_config.conf"}, "modifiedCode": "# 速度优化配置文件 - Mac端\n# 针对局域网环境的极速同步配置\n\n# 基本配置\nLOCAL_DIR=\"/Users/<USER>/Documents/project\"\nREMOTE_HOST=\"*************\"  # 使用局域网IP，避免DNS解析延迟\nREMOTE_USER=\"windowsuser\"\nREMOTE_DIR=\"/mnt/c/Users/<USER>/Documents/project\"\n\n# 速度优化设置\nSYNC_DELAY=0.5           # 最小延迟，适合高速网络\nMAX_RETRIES=2            # 减少重试次数，快速失败\nSHOW_PROGRESS=false      # 关闭进度显示，减少开销\n\n# 网络优化\nSSH_PORT=22\nCONNECTION_TIMEOUT=5     # 快速连接超时\nCOMPRESS_LEVEL=1         # 低压缩级别，CPU换网络\nPARALLEL_TRANSFERS=4     # 并行传输，适合千兆网络\nBANDWIDTH_LIMIT=\"\"       # 不限制带宽\n\n# rsync优化参数\nRSYNC_EXTRA_OPTS=\"--whole-file --inplace --no-compress\"\n# --whole-file: 局域网下传输整个文件更快\n# --inplace: 就地更新，减少磁盘IO\n# --no-compress: 局域网下不压缩更快\n\n# SSH优化\nSSH_CIPHER=\"aes128-ctr\"  # 快速加密算法\nSSH_COMPRESSION=false    # 关闭SSH压缩\nSSH_MULTIPLEXING=true    # 启用连接复用\n\n# 监控优化\nHEALTH_CHECK_INTERVAL=60 # 减少健康检查频率\nAUTO_RESTART=true\nLOG_LEVEL=\"WARN\"         # 减少日志输出\n\n# 文件系统优化\nFSWATCH_LATENCY=0.1      # 最小监控延迟\nBATCH_SIZE=100           # 批量处理文件变化\n\n# 高级优化选项\nTCP_WINDOW_SIZE=65536    # TCP窗口大小\nSOCKET_BUFFER_SIZE=262144 # 套接字缓冲区大小\nDISABLE_NAGLE=true       # 禁用Nagle算法，减少延迟\n"}