{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/setup.sh"}, "originalCode": "#!/bin/bash\n\n# Mac端同步环境安装脚本\n\nset -e\n\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nRED='\\033[0;31m'\nNC='\\033[0m' # No Color\n\n# 打印彩色消息\nprint_message() {\n    local color=$1\n    local message=$2\n    echo -e \"${color}${message}${NC}\"\n}\n\nprint_success() {\n    print_message \"$GREEN\" \"✓ $1\"\n}\n\nprint_warning() {\n    print_message \"$YELLOW\" \"⚠ $1\"\n}\n\nprint_error() {\n    print_message \"$RED\" \"✗ $1\"\n}\n\nprint_info() {\n    echo \"ℹ $1\"\n}\n\n# 检查是否为Mac系统\ncheck_macos() {\n    if [[ \"$OSTYPE\" != \"darwin\"* ]]; then\n        print_error \"此脚本仅适用于macOS系统\"\n        exit 1\n    fi\n    print_success \"检测到macOS系统\"\n}\n\n# 检查并安装Homebrew\ninstall_homebrew() {\n    if command -v brew &> /dev/null; then\n        print_success \"Homebrew已安装\"\n    else\n        print_info \"正在安装Homebrew...\"\n        /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"\n        print_success \"Homebrew安装完成\"\n    fi\n}\n\n# 安装必要工具\ninstall_tools() {\n    local tools=(\"fswatch\" \"rsync\")\n    \n    for tool in \"${tools[@]}\"; do\n        if command -v \"$tool\" &> /dev/null; then\n            print_success \"$tool 已安装\"\n        else\n            print_info \"正在安装 $tool...\"\n            brew install \"$tool\"\n            print_success \"$tool 安装完成\"\n        fi\n    done\n}\n\n# 生成SSH密钥\nsetup_ssh() {\n    local ssh_dir=\"$HOME/.ssh\"\n    local key_file=\"$ssh_dir/id_rsa\"\n    \n    if [[ ! -d \"$ssh_dir\" ]]; then\n        mkdir -p \"$ssh_dir\"\n        chmod 700 \"$ssh_dir\"\n    fi\n    \n    if [[ -f \"$key_file\" ]]; then\n        print_success \"SSH密钥已存在\"\n    else\n        print_info \"生成SSH密钥...\"\n        read -p \"请输入您的邮箱地址: \" email\n        ssh-keygen -t rsa -b 4096 -C \"$email\" -f \"$key_file\" -N \"\"\n        print_success \"SSH密钥生成完成\"\n    fi\n    \n    # 显示公钥\n    print_info \"您的SSH公钥：\"\n    echo \"----------------------------------------\"\n    cat \"$key_file.pub\"\n    echo \"----------------------------------------\"\n    print_warning \"请将上述公钥添加到Windows端的authorized_keys文件中\"\n}\n\n# 创建配置文件\ncreate_config() {\n    local config_file=\"$SCRIPT_DIR/sync_config.conf\"\n    \n    if [[ -f \"$config_file\" ]]; then\n        print_warning \"配置文件已存在，是否覆盖？(y/N)\"\n        read -r response\n        if [[ ! \"$response\" =~ ^[Yy]$ ]]; then\n            print_info \"跳过配置文件创建\"\n            return\n        fi\n    fi\n    \n    print_info \"创建配置文件...\"\n    \n    # 获取用户输入\n    read -p \"本地同步目录路径: \" local_dir\n    read -p \"Windows主机IP地址: \" remote_host\n    read -p \"Windows用户名: \" remote_user\n    read -p \"Windows端目录路径: \" remote_dir\n    \n    # 验证本地目录\n    if [[ ! -d \"$local_dir\" ]]; then\n        print_warning \"本地目录不存在，是否创建？(y/N)\"\n        read -r response\n        if [[ \"$response\" =~ ^[Yy]$ ]]; then\n            mkdir -p \"$local_dir\"\n            print_success \"本地目录创建完成\"\n        fi\n    fi\n    \n    # 生成配置文件\n    cat > \"$config_file\" << EOF\n# Mac端同步配置文件\nLOCAL_DIR=\"$local_dir\"\nREMOTE_HOST=\"$remote_host\"\nREMOTE_USER=\"$remote_user\"\nREMOTE_DIR=\"$remote_dir\"\nSYNC_DELAY=2\nMAX_RETRIES=3\nSHOW_PROGRESS=false\nSSH_PORT=22\nCONNECTION_TIMEOUT=10\nCOMPRESS_LEVEL=6\nLOG_LEVEL=\"INFO\"\nLOG_RETENTION_DAYS=7\nEOF\n    \n    print_success \"配置文件创建完成: $config_file\"\n}\n\n# 测试连接\ntest_connection() {\n    print_info \"测试SSH连接...\"\n    \n    # 加载配置\n    source \"$SCRIPT_DIR/sync_config.conf\"\n    \n    if ssh -o ConnectTimeout=10 -o BatchMode=yes \"$REMOTE_USER@$REMOTE_HOST\" \"echo 'SSH连接成功'\" 2>/dev/null; then\n        print_success \"SSH连接测试成功\"\n    else\n        print_error \"SSH连接失败\"\n        print_info \"请确保：\"\n        echo \"  1. Windows端已安装SSH服务\"\n        echo \"  2. SSH公钥已添加到Windows端\"\n        echo \"  3. 网络连接正常\"\n        echo \"  4. 防火墙允许SSH连接\"\n        return 1\n    fi\n    \n    # 测试目录\n    if ssh \"$REMOTE_USER@$REMOTE_HOST\" \"test -d '$REMOTE_DIR'\" 2>/dev/null; then\n        print_success \"远程目录访问成功\"\n    else\n        print_warning \"远程目录不存在，尝试创建...\"\n        if ssh \"$REMOTE_USER@$REMOTE_HOST\" \"mkdir -p '$REMOTE_DIR'\" 2>/dev/null; then\n            print_success \"远程目录创建成功\"\n        else\n            print_error \"远程目录创建失败\"\n            return 1\n        fi\n    fi\n}\n\n# 创建启动脚本\ncreate_launcher() {\n    local launcher_file=\"$SCRIPT_DIR/start_sync.sh\"\n    \n    cat > \"$launcher_file\" << 'EOF'\n#!/bin/bash\n\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nSYNC_SCRIPT=\"$SCRIPT_DIR/mac_realtime_sync.sh\"\n\n# 检查同步脚本是否存在\nif [[ ! -f \"$SYNC_SCRIPT\" ]]; then\n    echo \"错误: 同步脚本不存在: $SYNC_SCRIPT\"\n    exit 1\nfi\n\n# 启动同步服务\necho \"启动实时同步服务...\"\nbash \"$SYNC_SCRIPT\" \"$@\"\nEOF\n    \n    chmod +x \"$launcher_file\"\n    chmod +x \"$SCRIPT_DIR/mac_realtime_sync.sh\"\n    \n    print_success \"启动脚本创建完成: $launcher_file\"\n}\n\n# 创建系统服务（可选）\ncreate_service() {\n    print_info \"是否创建系统服务以便开机自启动？(y/N)\"\n    read -r response\n    \n    if [[ ! \"$response\" =~ ^[Yy]$ ]]; then\n        return\n    fi\n    \n    local service_file=\"$HOME/Library/LaunchAgents/com.filesync.realtime.plist\"\n    local script_path=\"$SCRIPT_DIR/mac_realtime_sync.sh\"\n    \n    cat > \"$service_file\" << EOF\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n<plist version=\"1.0\">\n<dict>\n    <key>Label</key>\n    <string>com.filesync.realtime</string>\n    <key>ProgramArguments</key>\n    <array>\n        <string>/bin/bash</string>\n        <string>$script_path</string>\n    </array>\n    <key>RunAtLoad</key>\n    <true/>\n    <key>KeepAlive</key>\n    <true/>\n    <key>StandardOutPath</key>\n    <string>$SCRIPT_DIR/logs/service.log</string>\n    <key>StandardErrorPath</key>\n    <string>$SCRIPT_DIR/logs/service_error.log</string>\n    <key>WorkingDirectory</key>\n    <string>$SCRIPT_DIR</string>\n</dict>\n</plist>\nEOF\n    \n    print_success \"系统服务文件创建完成\"\n    print_info \"使用以下命令管理服务：\"\n    echo \"  启动: launchctl load $service_file\"\n    echo \"  停止: launchctl unload $service_file\"\n}\n\n# 显示完成信息\nshow_completion() {\n    print_success \"安装完成！\"\n    echo\n    print_info \"下一步操作：\"\n    echo \"1. 确保Windows端已配置SSH服务\"\n    echo \"2. 将SSH公钥添加到Windows端\"\n    echo \"3. 运行测试: ./setup.sh --test\"\n    echo \"4. 启动同步: ./start_sync.sh\"\n    echo\n    print_info \"配置文件位置: $SCRIPT_DIR/sync_config.conf\"\n    print_info \"日志文件位置: $SCRIPT_DIR/logs/\"\n}\n\n# 主函数\nmain() {\n    case \"${1:-}\" in\n        --test)\n            test_connection\n            ;;\n        --help|-h)\n            echo \"用法: $0 [选项]\"\n            echo \"选项:\"\n            echo \"  --test    测试连接\"\n            echo \"  --help    显示帮助\"\n            ;;\n        *)\n            print_info \"开始安装Mac端文件同步环境...\"\n            check_macos\n            install_homebrew\n            install_tools\n            setup_ssh\n            create_config\n            create_launcher\n            create_service\n            show_completion\n            ;;\n    esac\n}\n\nmain \"$@\"\n", "modifiedCode": "#!/bin/bash\n\n# Mac端同步环境安装脚本\n\nset -e\n\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nRED='\\033[0;31m'\nNC='\\033[0m' # No Color\n\n# 打印彩色消息\nprint_message() {\n    local color=$1\n    local message=$2\n    echo -e \"${color}${message}${NC}\"\n}\n\nprint_success() {\n    print_message \"$GREEN\" \"✓ $1\"\n}\n\nprint_warning() {\n    print_message \"$YELLOW\" \"⚠ $1\"\n}\n\nprint_error() {\n    print_message \"$RED\" \"✗ $1\"\n}\n\nprint_info() {\n    echo \"ℹ $1\"\n}\n\n# 检查是否为Mac系统\ncheck_macos() {\n    if [[ \"$OSTYPE\" != \"darwin\"* ]]; then\n        print_error \"此脚本仅适用于macOS系统\"\n        exit 1\n    fi\n    print_success \"检测到macOS系统\"\n}\n\n# 检查并安装Homebrew\ninstall_homebrew() {\n    if command -v brew &> /dev/null; then\n        print_success \"Homebrew已安装\"\n    else\n        print_info \"正在安装Homebrew...\"\n        /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"\n        print_success \"Homebrew安装完成\"\n    fi\n}\n\n# 安装必要工具\ninstall_tools() {\n    local tools=(\"fswatch\" \"rsync\")\n    \n    for tool in \"${tools[@]}\"; do\n        if command -v \"$tool\" &> /dev/null; then\n            print_success \"$tool 已安装\"\n        else\n            print_info \"正在安装 $tool...\"\n            brew install \"$tool\"\n            print_success \"$tool 安装完成\"\n        fi\n    done\n}\n\n# 生成SSH密钥\nsetup_ssh() {\n    local ssh_dir=\"$HOME/.ssh\"\n    local key_file=\"$ssh_dir/id_rsa\"\n    \n    if [[ ! -d \"$ssh_dir\" ]]; then\n        mkdir -p \"$ssh_dir\"\n        chmod 700 \"$ssh_dir\"\n    fi\n    \n    if [[ -f \"$key_file\" ]]; then\n        print_success \"SSH密钥已存在\"\n    else\n        print_info \"生成SSH密钥...\"\n        read -p \"请输入您的邮箱地址: \" email\n        ssh-keygen -t rsa -b 4096 -C \"$email\" -f \"$key_file\" -N \"\"\n        print_success \"SSH密钥生成完成\"\n    fi\n    \n    # 显示公钥\n    print_info \"您的SSH公钥：\"\n    echo \"----------------------------------------\"\n    cat \"$key_file.pub\"\n    echo \"----------------------------------------\"\n    print_warning \"请将上述公钥添加到Windows端的authorized_keys文件中\"\n}\n\n# 创建配置文件\ncreate_config() {\n    local config_file=\"$SCRIPT_DIR/sync_config.conf\"\n    \n    if [[ -f \"$config_file\" ]]; then\n        print_warning \"配置文件已存在，是否覆盖？(y/N)\"\n        read -r response\n        if [[ ! \"$response\" =~ ^[Yy]$ ]]; then\n            print_info \"跳过配置文件创建\"\n            return\n        fi\n    fi\n    \n    print_info \"创建配置文件...\"\n    \n    # 获取用户输入\n    read -p \"本地同步目录路径: \" local_dir\n    read -p \"Windows主机IP地址: \" remote_host\n    read -p \"Windows用户名: \" remote_user\n    read -p \"Windows端目录路径: \" remote_dir\n    \n    # 验证本地目录\n    if [[ ! -d \"$local_dir\" ]]; then\n        print_warning \"本地目录不存在，是否创建？(y/N)\"\n        read -r response\n        if [[ \"$response\" =~ ^[Yy]$ ]]; then\n            mkdir -p \"$local_dir\"\n            print_success \"本地目录创建完成\"\n        fi\n    fi\n    \n    # 生成配置文件\n    cat > \"$config_file\" << EOF\n# Mac端同步配置文件\nLOCAL_DIR=\"$local_dir\"\nREMOTE_HOST=\"$remote_host\"\nREMOTE_USER=\"$remote_user\"\nREMOTE_DIR=\"$remote_dir\"\nSYNC_DELAY=2\nMAX_RETRIES=3\nSHOW_PROGRESS=false\nSSH_PORT=22\nCONNECTION_TIMEOUT=10\nCOMPRESS_LEVEL=6\nLOG_LEVEL=\"INFO\"\nLOG_RETENTION_DAYS=7\nEOF\n    \n    print_success \"配置文件创建完成: $config_file\"\n}\n\n# 测试连接\ntest_connection() {\n    print_info \"测试SSH连接...\"\n    \n    # 加载配置\n    source \"$SCRIPT_DIR/sync_config.conf\"\n    \n    if ssh -o ConnectTimeout=10 -o BatchMode=yes \"$REMOTE_USER@$REMOTE_HOST\" \"echo 'SSH连接成功'\" 2>/dev/null; then\n        print_success \"SSH连接测试成功\"\n    else\n        print_error \"SSH连接失败\"\n        print_info \"请确保：\"\n        echo \"  1. Windows端已安装SSH服务\"\n        echo \"  2. SSH公钥已添加到Windows端\"\n        echo \"  3. 网络连接正常\"\n        echo \"  4. 防火墙允许SSH连接\"\n        return 1\n    fi\n    \n    # 测试目录\n    if ssh \"$REMOTE_USER@$REMOTE_HOST\" \"test -d '$REMOTE_DIR'\" 2>/dev/null; then\n        print_success \"远程目录访问成功\"\n    else\n        print_warning \"远程目录不存在，尝试创建...\"\n        if ssh \"$REMOTE_USER@$REMOTE_HOST\" \"mkdir -p '$REMOTE_DIR'\" 2>/dev/null; then\n            print_success \"远程目录创建成功\"\n        else\n            print_error \"远程目录创建失败\"\n            return 1\n        fi\n    fi\n}\n\n# 创建启动脚本\ncreate_launcher() {\n    local launcher_file=\"$SCRIPT_DIR/start_sync.sh\"\n    \n    cat > \"$launcher_file\" << 'EOF'\n#!/bin/bash\n\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nSYNC_SCRIPT=\"$SCRIPT_DIR/mac_realtime_sync.sh\"\n\n# 检查同步脚本是否存在\nif [[ ! -f \"$SYNC_SCRIPT\" ]]; then\n    echo \"错误: 同步脚本不存在: $SYNC_SCRIPT\"\n    exit 1\nfi\n\n# 启动同步服务\necho \"启动实时同步服务...\"\nbash \"$SYNC_SCRIPT\" \"$@\"\nEOF\n    \n    chmod +x \"$launcher_file\"\n    chmod +x \"$SCRIPT_DIR/mac_realtime_sync.sh\"\n    \n    print_success \"启动脚本创建完成: $launcher_file\"\n}\n\n# 创建系统服务（可选）\ncreate_service() {\n    print_info \"是否创建系统服务以便开机自启动？(y/N)\"\n    read -r response\n    \n    if [[ ! \"$response\" =~ ^[Yy]$ ]]; then\n        return\n    fi\n    \n    local service_file=\"$HOME/Library/LaunchAgents/com.filesync.realtime.plist\"\n    local script_path=\"$SCRIPT_DIR/mac_realtime_sync.sh\"\n    \n    cat > \"$service_file\" << EOF\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n<plist version=\"1.0\">\n<dict>\n    <key>Label</key>\n    <string>com.filesync.realtime</string>\n    <key>ProgramArguments</key>\n    <array>\n        <string>/bin/bash</string>\n        <string>$script_path</string>\n    </array>\n    <key>RunAtLoad</key>\n    <true/>\n    <key>KeepAlive</key>\n    <true/>\n    <key>StandardOutPath</key>\n    <string>$SCRIPT_DIR/logs/service.log</string>\n    <key>StandardErrorPath</key>\n    <string>$SCRIPT_DIR/logs/service_error.log</string>\n    <key>WorkingDirectory</key>\n    <string>$SCRIPT_DIR</string>\n</dict>\n</plist>\nEOF\n    \n    print_success \"系统服务文件创建完成\"\n    print_info \"使用以下命令管理服务：\"\n    echo \"  启动: launchctl load $service_file\"\n    echo \"  停止: launchctl unload $service_file\"\n}\n\n# 显示完成信息\nshow_completion() {\n    print_success \"安装完成！\"\n    echo\n    print_info \"下一步操作：\"\n    echo \"1. 确保Windows端已配置SSH服务\"\n    echo \"2. 将SSH公钥添加到Windows端\"\n    echo \"3. 运行测试: ./setup.sh --test\"\n    echo \"4. 启动同步: ./start_sync.sh\"\n    echo\n    print_info \"配置文件位置: $SCRIPT_DIR/sync_config.conf\"\n    print_info \"日志文件位置: $SCRIPT_DIR/logs/\"\n}\n\n# 主函数\nmain() {\n    case \"${1:-}\" in\n        --test)\n            test_connection\n            ;;\n        --help|-h)\n            echo \"用法: $0 [选项]\"\n            echo \"选项:\"\n            echo \"  --test    测试连接\"\n            echo \"  --help    显示帮助\"\n            ;;\n        *)\n            print_info \"开始安装Mac端文件同步环境...\"\n            check_macos\n            install_homebrew\n            install_tools\n            setup_ssh\n            create_config\n            create_launcher\n            create_service\n            show_completion\n            ;;\n    esac\n}\n\nmain \"$@\"\n"}