{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/mac_realtime_sync.sh"}, "originalCode": "#!/bin/bash\n\n# Mac端实时同步脚本\n# 使用fswatch监控文件变化，通过rsync实现实时同步\n\n# 配置参数\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nCONFIG_FILE=\"$SCRIPT_DIR/sync_config.conf\"\nLOG_FILE=\"$SCRIPT_DIR/logs/sync_$(date +%Y%m%d).log\"\n\n# 创建日志目录\nmkdir -p \"$SCRIPT_DIR/logs\"\n\n# 日志函数\nlog() {\n    echo \"[$(date '+%Y-%m-%d %H:%M:%S')] $1\" | tee -a \"$LOG_FILE\"\n}\n\n# 加载配置文件\nif [[ -f \"$CONFIG_FILE\" ]]; then\n    source \"$CONFIG_FILE\"\nelse\n    log \"错误: 配置文件 $CONFIG_FILE 不存在\"\n    exit 1\nfi\n\n# 验证必要参数\nif [[ -z \"$LOCAL_DIR\" || -z \"$REMOTE_HOST\" || -z \"$REMOTE_USER\" || -z \"$REMOTE_DIR\" ]]; then\n    log \"错误: 配置参数不完整\"\n    exit 1\nfi\n\n# 检查本地目录\nif [[ ! -d \"$LOCAL_DIR\" ]]; then\n    log \"错误: 本地目录 $LOCAL_DIR 不存在\"\n    exit 1\nfi\n\n# 检查依赖工具\ncheck_dependencies() {\n    local deps=(\"fswatch\" \"rsync\" \"ssh\")\n    for dep in \"${deps[@]}\"; do\n        if ! command -v \"$dep\" &> /dev/null; then\n            log \"错误: 缺少依赖工具 $dep\"\n            echo \"请运行: brew install $dep\"\n            exit 1\n        fi\n    done\n}\n\n# 测试远程连接\ntest_connection() {\n    log \"测试远程连接...\"\n    if ssh -o ConnectTimeout=10 \"$REMOTE_USER@$REMOTE_HOST\" \"test -d '$REMOTE_DIR'\"; then\n        log \"远程连接测试成功\"\n        return 0\n    else\n        log \"错误: 无法连接到远程主机或目录不存在\"\n        return 1\n    fi\n}\n\n# 执行同步\nsync_files() {\n    local sync_start=$(date +%s)\n    log \"开始同步: $LOCAL_DIR -> $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR\"\n    \n    # 构建rsync命令\n    local rsync_cmd=\"rsync -avz --delete --compress-level=6\"\n    \n    # 添加排除规则\n    if [[ -f \"$SCRIPT_DIR/exclude.txt\" ]]; then\n        rsync_cmd=\"$rsync_cmd --exclude-from=$SCRIPT_DIR/exclude.txt\"\n    fi\n    \n    # 添加进度显示（可选）\n    if [[ \"$SHOW_PROGRESS\" == \"true\" ]]; then\n        rsync_cmd=\"$rsync_cmd --progress\"\n    fi\n    \n    # 执行同步\n    if $rsync_cmd \"$LOCAL_DIR/\" \"$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/\"; then\n        local sync_end=$(date +%s)\n        local duration=$((sync_end - sync_start))\n        log \"同步完成，耗时: ${duration}秒\"\n        return 0\n    else\n        log \"同步失败\"\n        return 1\n    fi\n}\n\n# 初始同步\ninitial_sync() {\n    log \"执行初始同步...\"\n    if sync_files; then\n        log \"初始同步完成\"\n    else\n        log \"初始同步失败，退出\"\n        exit 1\n    fi\n}\n\n# 文件变化处理\nhandle_change() {\n    local changed_file=\"$1\"\n    log \"检测到文件变化: $changed_file\"\n    \n    # 防抖动：等待一段时间确保文件写入完成\n    sleep \"${SYNC_DELAY:-2}\"\n    \n    # 重试机制\n    local max_retries=\"${MAX_RETRIES:-3}\"\n    local retry_count=0\n    \n    while [[ $retry_count -lt $max_retries ]]; do\n        if sync_files; then\n            break\n        else\n            retry_count=$((retry_count + 1))\n            log \"同步失败，重试 $retry_count/$max_retries\"\n            sleep 5\n        fi\n    done\n    \n    if [[ $retry_count -eq $max_retries ]]; then\n        log \"同步失败，已达到最大重试次数\"\n    fi\n}\n\n# 信号处理\ncleanup() {\n    log \"收到退出信号，正在清理...\"\n    if [[ -n \"$FSWATCH_PID\" ]]; then\n        kill \"$FSWATCH_PID\" 2>/dev/null\n    fi\n    log \"同步服务已停止\"\n    exit 0\n}\n\n# 主函数\nmain() {\n    log \"启动Mac端实时同步服务\"\n    log \"监控目录: $LOCAL_DIR\"\n    log \"远程目标: $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR\"\n    \n    # 检查依赖\n    check_dependencies\n    \n    # 测试连接\n    if ! test_connection; then\n        exit 1\n    fi\n    \n    # 初始同步\n    initial_sync\n    \n    # 设置信号处理\n    trap cleanup SIGINT SIGTERM\n    \n    # 启动文件监控\n    log \"启动文件监控...\"\n    fswatch -o \"$LOCAL_DIR\" | while read f; do\n        handle_change \"$f\"\n    done &\n    \n    FSWATCH_PID=$!\n    log \"文件监控已启动 (PID: $FSWATCH_PID)\"\n    \n    # 保持脚本运行\n    wait $FSWATCH_PID\n}\n\n# 显示帮助信息\nshow_help() {\n    cat << EOF\nMac端实时文件同步脚本\n\n用法: $0 [选项]\n\n选项:\n    -h, --help      显示此帮助信息\n    -c, --config    指定配置文件路径\n    -t, --test      仅测试连接，不启动同步\n    -i, --init      仅执行初始同步\n    -v, --verbose   详细输出模式\n\n配置文件示例:\n    LOCAL_DIR=\"/Users/<USER>/Documents/project\"\n    REMOTE_HOST=\"*************\"\n    REMOTE_USER=\"windowsuser\"\n    REMOTE_DIR=\"/mnt/c/Users/<USER>/Documents/project\"\n    SYNC_DELAY=2\n    MAX_RETRIES=3\n    SHOW_PROGRESS=false\n\nEOF\n}\n\n# 解析命令行参数\nwhile [[ $# -gt 0 ]]; do\n    case $1 in\n        -h|--help)\n            show_help\n            exit 0\n            ;;\n        -c|--config)\n            CONFIG_FILE=\"$2\"\n            shift 2\n            ;;\n        -t|--test)\n            check_dependencies\n            test_connection\n            exit $?\n            ;;\n        -i|--init)\n            check_dependencies\n            test_connection && initial_sync\n            exit $?\n            ;;\n        -v|--verbose)\n            set -x\n            shift\n            ;;\n        *)\n            echo \"未知选项: $1\"\n            show_help\n            exit 1\n            ;;\n    esac\ndone\n\n# 运行主函数\nmain\n", "modifiedCode": "#!/bin/bash\n\n# Mac端实时同步脚本\n# 使用fswatch监控文件变化，通过rsync实现实时同步\n\n# 配置参数\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nCONFIG_FILE=\"$SCRIPT_DIR/sync_config.conf\"\nLOG_FILE=\"$SCRIPT_DIR/logs/sync_$(date +%Y%m%d).log\"\n\n# 创建日志目录\nmkdir -p \"$SCRIPT_DIR/logs\"\n\n# 日志函数\nlog() {\n    echo \"[$(date '+%Y-%m-%d %H:%M:%S')] $1\" | tee -a \"$LOG_FILE\"\n}\n\n# 加载配置文件\nif [[ -f \"$CONFIG_FILE\" ]]; then\n    source \"$CONFIG_FILE\"\nelse\n    log \"错误: 配置文件 $CONFIG_FILE 不存在\"\n    exit 1\nfi\n\n# 验证必要参数\nif [[ -z \"$LOCAL_DIR\" || -z \"$REMOTE_HOST\" || -z \"$REMOTE_USER\" || -z \"$REMOTE_DIR\" ]]; then\n    log \"错误: 配置参数不完整\"\n    exit 1\nfi\n\n# 检查本地目录\nif [[ ! -d \"$LOCAL_DIR\" ]]; then\n    log \"错误: 本地目录 $LOCAL_DIR 不存在\"\n    exit 1\nfi\n\n# 检查依赖工具\ncheck_dependencies() {\n    local deps=(\"fswatch\" \"rsync\" \"ssh\")\n    for dep in \"${deps[@]}\"; do\n        if ! command -v \"$dep\" &> /dev/null; then\n            log \"错误: 缺少依赖工具 $dep\"\n            echo \"请运行: brew install $dep\"\n            exit 1\n        fi\n    done\n}\n\n# 测试远程连接\ntest_connection() {\n    log \"测试远程连接...\"\n    if ssh -o ConnectTimeout=10 \"$REMOTE_USER@$REMOTE_HOST\" \"test -d '$REMOTE_DIR'\"; then\n        log \"远程连接测试成功\"\n        return 0\n    else\n        log \"错误: 无法连接到远程主机或目录不存在\"\n        return 1\n    fi\n}\n\n# 执行同步\nsync_files() {\n    local sync_start=$(date +%s)\n    log \"开始同步: $LOCAL_DIR -> $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR\"\n    \n    # 构建rsync命令\n    local rsync_cmd=\"rsync -avz --delete --compress-level=6\"\n    \n    # 添加排除规则\n    if [[ -f \"$SCRIPT_DIR/exclude.txt\" ]]; then\n        rsync_cmd=\"$rsync_cmd --exclude-from=$SCRIPT_DIR/exclude.txt\"\n    fi\n    \n    # 添加进度显示（可选）\n    if [[ \"$SHOW_PROGRESS\" == \"true\" ]]; then\n        rsync_cmd=\"$rsync_cmd --progress\"\n    fi\n    \n    # 执行同步\n    if $rsync_cmd \"$LOCAL_DIR/\" \"$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/\"; then\n        local sync_end=$(date +%s)\n        local duration=$((sync_end - sync_start))\n        log \"同步完成，耗时: ${duration}秒\"\n        return 0\n    else\n        log \"同步失败\"\n        return 1\n    fi\n}\n\n# 初始同步\ninitial_sync() {\n    log \"执行初始同步...\"\n    if sync_files; then\n        log \"初始同步完成\"\n    else\n        log \"初始同步失败，退出\"\n        exit 1\n    fi\n}\n\n# 文件变化处理\nhandle_change() {\n    local changed_file=\"$1\"\n    log \"检测到文件变化: $changed_file\"\n    \n    # 防抖动：等待一段时间确保文件写入完成\n    sleep \"${SYNC_DELAY:-2}\"\n    \n    # 重试机制\n    local max_retries=\"${MAX_RETRIES:-3}\"\n    local retry_count=0\n    \n    while [[ $retry_count -lt $max_retries ]]; do\n        if sync_files; then\n            break\n        else\n            retry_count=$((retry_count + 1))\n            log \"同步失败，重试 $retry_count/$max_retries\"\n            sleep 5\n        fi\n    done\n    \n    if [[ $retry_count -eq $max_retries ]]; then\n        log \"同步失败，已达到最大重试次数\"\n    fi\n}\n\n# 信号处理\ncleanup() {\n    log \"收到退出信号，正在清理...\"\n    if [[ -n \"$FSWATCH_PID\" ]]; then\n        kill \"$FSWATCH_PID\" 2>/dev/null\n    fi\n    log \"同步服务已停止\"\n    exit 0\n}\n\n# 主函数\nmain() {\n    log \"启动Mac端实时同步服务\"\n    log \"监控目录: $LOCAL_DIR\"\n    log \"远程目标: $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR\"\n    \n    # 检查依赖\n    check_dependencies\n    \n    # 测试连接\n    if ! test_connection; then\n        exit 1\n    fi\n    \n    # 初始同步\n    initial_sync\n    \n    # 设置信号处理\n    trap cleanup SIGINT SIGTERM\n    \n    # 启动文件监控\n    log \"启动文件监控...\"\n    fswatch -o \"$LOCAL_DIR\" | while read f; do\n        handle_change \"$f\"\n    done &\n    \n    FSWATCH_PID=$!\n    log \"文件监控已启动 (PID: $FSWATCH_PID)\"\n    \n    # 保持脚本运行\n    wait $FSWATCH_PID\n}\n\n# 显示帮助信息\nshow_help() {\n    cat << EOF\nMac端实时文件同步脚本\n\n用法: $0 [选项]\n\n选项:\n    -h, --help      显示此帮助信息\n    -c, --config    指定配置文件路径\n    -t, --test      仅测试连接，不启动同步\n    -i, --init      仅执行初始同步\n    -v, --verbose   详细输出模式\n\n配置文件示例:\n    LOCAL_DIR=\"/Users/<USER>/Documents/project\"\n    REMOTE_HOST=\"*************\"\n    REMOTE_USER=\"windowsuser\"\n    REMOTE_DIR=\"/mnt/c/Users/<USER>/Documents/project\"\n    SYNC_DELAY=2\n    MAX_RETRIES=3\n    SHOW_PROGRESS=false\n\nEOF\n}\n\n# 解析命令行参数\nwhile [[ $# -gt 0 ]]; do\n    case $1 in\n        -h|--help)\n            show_help\n            exit 0\n            ;;\n        -c|--config)\n            CONFIG_FILE=\"$2\"\n            shift 2\n            ;;\n        -t|--test)\n            check_dependencies\n            test_connection\n            exit $?\n            ;;\n        -i|--init)\n            check_dependencies\n            test_connection && initial_sync\n            exit $?\n            ;;\n        -v|--verbose)\n            set -x\n            shift\n            ;;\n        *)\n            echo \"未知选项: $1\"\n            show_help\n            exit 1\n            ;;\n    esac\ndone\n\n# 运行主函数\nmain\n"}