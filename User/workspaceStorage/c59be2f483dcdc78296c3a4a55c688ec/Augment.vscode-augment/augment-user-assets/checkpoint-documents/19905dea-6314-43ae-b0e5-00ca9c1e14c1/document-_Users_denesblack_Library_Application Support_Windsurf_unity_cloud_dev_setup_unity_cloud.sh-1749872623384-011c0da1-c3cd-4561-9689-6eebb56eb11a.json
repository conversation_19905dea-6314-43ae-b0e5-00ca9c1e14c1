{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "unity_cloud_dev/setup_unity_cloud.sh"}, "modifiedCode": "#!/bin/bash\n\n# Unity云端开发环境安装脚本\n# 适用于Ubuntu 20.04/22.04云服务器\n\nset -e\n\n# 颜色输出\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m'\n\nprint_header() {\n    echo -e \"${BLUE}================================${NC}\"\n    echo -e \"${BLUE}$1${NC}\"\n    echo -e \"${BLUE}================================${NC}\"\n}\n\nprint_success() {\n    echo -e \"${GREEN}✓ $1${NC}\"\n}\n\nprint_warning() {\n    echo -e \"${YELLOW}⚠ $1${NC}\"\n}\n\nprint_error() {\n    echo -e \"${RED}✗ $1${NC}\"\n}\n\nprint_info() {\n    echo -e \"${BLUE}ℹ $1${NC}\"\n}\n\n# 检查系统要求\ncheck_system() {\n    print_header \"检查系统环境\"\n    \n    # 检查操作系统\n    if [[ ! -f /etc/os-release ]]; then\n        print_error \"无法检测操作系统\"\n        exit 1\n    fi\n    \n    source /etc/os-release\n    if [[ \"$ID\" != \"ubuntu\" ]]; then\n        print_warning \"此脚本主要针对Ubuntu系统，其他系统可能需要调整\"\n    fi\n    \n    print_success \"操作系统: $PRETTY_NAME\"\n    \n    # 检查内存\n    total_mem=$(free -m | awk 'NR==2{printf \"%.1f\", $2/1024}')\n    if (( $(echo \"$total_mem < 4.0\" | bc -l) )); then\n        print_warning \"内存不足4GB，建议升级配置\"\n    else\n        print_success \"内存: ${total_mem}GB\"\n    fi\n    \n    # 检查磁盘空间\n    disk_space=$(df -h / | awk 'NR==2 {print $4}')\n    print_success \"可用磁盘空间: $disk_space\"\n    \n    # 检查网络\n    if ping -c 1 google.com &> /dev/null; then\n        print_success \"网络连接正常\"\n    else\n        print_error \"网络连接异常\"\n        exit 1\n    fi\n}\n\n# 更新系统\nupdate_system() {\n    print_header \"更新系统包\"\n    \n    sudo apt update\n    sudo apt upgrade -y\n    \n    # 安装基础工具\n    sudo apt install -y \\\n        curl \\\n        wget \\\n        git \\\n        git-lfs \\\n        unzip \\\n        build-essential \\\n        software-properties-common \\\n        apt-transport-https \\\n        ca-certificates \\\n        gnupg \\\n        lsb-release \\\n        bc\n    \n    print_success \"系统更新完成\"\n}\n\n# 安装Docker (用于容器化开发环境)\ninstall_docker() {\n    print_header \"安装Docker\"\n    \n    if command -v docker &> /dev/null; then\n        print_success \"Docker已安装\"\n        return\n    fi\n    \n    # 添加Docker官方GPG密钥\n    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg\n    \n    # 添加Docker仓库\n    echo \"deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null\n    \n    # 安装Docker\n    sudo apt update\n    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin\n    \n    # 添加用户到docker组\n    sudo usermod -aG docker $USER\n    \n    print_success \"Docker安装完成\"\n    print_warning \"请重新登录以使docker组权限生效\"\n}\n\n# 安装Node.js (用于一些构建工具)\ninstall_nodejs() {\n    print_header \"安装Node.js\"\n    \n    if command -v node &> /dev/null; then\n        print_success \"Node.js已安装: $(node --version)\"\n        return\n    fi\n    \n    # 安装NodeSource仓库\n    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -\n    sudo apt install -y nodejs\n    \n    print_success \"Node.js安装完成: $(node --version)\"\n}\n\n# 安装VS Code Server\ninstall_vscode_server() {\n    print_header \"安装VS Code Server\"\n    \n    # 下载并安装code-server\n    curl -fsSL https://code-server.dev/install.sh | sh\n    \n    # 创建配置目录\n    mkdir -p ~/.config/code-server\n    \n    # 生成配置文件\n    cat > ~/.config/code-server/config.yaml << EOF\nbind-addr: 0.0.0.0:8080\nauth: password\npassword: $(openssl rand -base64 32)\ncert: false\nEOF\n    \n    print_success \"VS Code Server安装完成\"\n    print_info \"配置文件位置: ~/.config/code-server/config.yaml\"\n    print_warning \"请记录密码并配置防火墙规则\"\n}\n\n# 安装Unity Hub (如果需要在云端运行Unity Editor)\ninstall_unity_hub() {\n    print_header \"安装Unity Hub (可选)\"\n    \n    read -p \"是否安装Unity Hub到云端？(需要GUI支持) [y/N]: \" install_unity\n    \n    if [[ \"$install_unity\" =~ ^[Yy]$ ]]; then\n        # 安装图形界面支持\n        sudo apt install -y ubuntu-desktop-minimal\n        \n        # 下载Unity Hub\n        wget -O unity-hub.AppImage \"https://public-cdn.cloud.unity3d.com/hub/prod/UnityHub.AppImage\"\n        chmod +x unity-hub.AppImage\n        sudo mv unity-hub.AppImage /usr/local/bin/unity-hub\n        \n        print_success \"Unity Hub安装完成\"\n        print_warning \"需要重启系统以启用图形界面\"\n    else\n        print_info \"跳过Unity Hub安装\"\n    fi\n}\n\n# 配置Git和Git LFS\nsetup_git() {\n    print_header \"配置Git环境\"\n    \n    # 初始化Git LFS\n    git lfs install\n    \n    # 设置Git配置 (如果还没有设置)\n    if [[ -z \"$(git config --global user.name)\" ]]; then\n        read -p \"请输入Git用户名: \" git_username\n        git config --global user.name \"$git_username\"\n    fi\n    \n    if [[ -z \"$(git config --global user.email)\" ]]; then\n        read -p \"请输入Git邮箱: \" git_email\n        git config --global user.email \"$git_email\"\n    fi\n    \n    # 配置Git LFS跟踪Unity文件\n    cat > .gitattributes << EOF\n# Unity文件类型\n*.fbx filter=lfs diff=lfs merge=lfs -text\n*.FBX filter=lfs diff=lfs merge=lfs -text\n*.psd filter=lfs diff=lfs merge=lfs -text\n*.PSD filter=lfs diff=lfs merge=lfs -text\n*.tga filter=lfs diff=lfs merge=lfs -text\n*.TGA filter=lfs diff=lfs merge=lfs -text\n*.tif filter=lfs diff=lfs merge=lfs -text\n*.TIF filter=lfs diff=lfs merge=lfs -text\n*.tiff filter=lfs diff=lfs merge=lfs -text\n*.TIFF filter=lfs diff=lfs merge=lfs -text\n*.exr filter=lfs diff=lfs merge=lfs -text\n*.EXR filter=lfs diff=lfs merge=lfs -text\n\n# 音频文件\n*.mp3 filter=lfs diff=lfs merge=lfs -text\n*.MP3 filter=lfs diff=lfs merge=lfs -text\n*.ogg filter=lfs diff=lfs merge=lfs -text\n*.OGG filter=lfs diff=lfs merge=lfs -text\n*.wav filter=lfs diff=lfs merge=lfs -text\n*.WAV filter=lfs diff=lfs merge=lfs -text\n\n# 视频文件\n*.mp4 filter=lfs diff=lfs merge=lfs -text\n*.MP4 filter=lfs diff=lfs merge=lfs -text\n*.mov filter=lfs diff=lfs merge=lfs -text\n*.MOV filter=lfs diff=lfs merge=lfs -text\n\n# 其他大文件\n*.zip filter=lfs diff=lfs merge=lfs -text\n*.ZIP filter=lfs diff=lfs merge=lfs -text\n*.rar filter=lfs diff=lfs merge=lfs -text\n*.RAR filter=lfs diff=lfs merge=lfs -text\n*.7z filter=lfs diff=lfs merge=lfs -text\n*.7Z filter=lfs diff=lfs merge=lfs -text\nEOF\n    \n    print_success \"Git环境配置完成\"\n}\n\n# 安装文件同步工具\ninstall_sync_tools() {\n    print_header \"安装文件同步工具\"\n    \n    # 安装rsync\n    sudo apt install -y rsync\n    \n    # 安装Syncthing\n    curl -s https://syncthing.net/release-key.txt | sudo apt-key add -\n    echo \"deb https://apt.syncthing.net/ syncthing stable\" | sudo tee /etc/apt/sources.list.d/syncthing.list\n    sudo apt update\n    sudo apt install -y syncthing\n    \n    print_success \"同步工具安装完成\"\n}\n\n# 创建Unity项目模板\ncreate_unity_template() {\n    print_header \"创建Unity项目模板\"\n    \n    mkdir -p ~/unity-projects/template\n    cd ~/unity-projects/template\n    \n    # 创建基本目录结构\n    mkdir -p Assets/{Scripts,Prefabs,Materials,Textures,Audio,Scenes}\n    mkdir -p ProjectSettings\n    \n    # 创建.gitignore文件\n    cat > .gitignore << EOF\n# Unity生成的文件\n[Ll]ibrary/\n[Tt]emp/\n[Oo]bj/\n[Bb]uild/\n[Bb]uilds/\n[Ll]ogs/\n[Uu]ser[Ss]ettings/\n\n# MemoryCaptures可以非常大\n[Mm]emoryCaptures/\n\n# 资源存储\n[Aa]ssets/AssetStoreTools*\n\n# Autogenerated Jetbrains Rider plugin\n[Aa]ssets/Plugins/Editor/JetBrains*\n\n# Visual Studio缓存文件\n.vs/\n\n# Gradle文件\n.gradle/\n\n# Autogenerated VS/MD/Consulo solution and project files\nExportedObj/\n.consulo/\n*.csproj\n*.unityproj\n*.sln\n*.suo\n*.tmp\n*.user\n*.userprefs\n*.pidb\n*.booproj\n*.svd\n*.pdb\n*.mdb\n*.opendb\n*.VC.db\n\n# Unity3D生成的元文件\n*.pidb.meta\n*.pdb.meta\n*.mdb.meta\n\n# Unity3D生成的文件\nTemp/\nObj/\nUnityGenerated/\nLibrary/\n\n# 系统文件\n.DS_Store\n.DS_Store?\n._*\n.Spotlight-V100\n.Trashes\nehthumbs.db\nThumbs.db\nEOF\n    \n    # 初始化Git仓库\n    git init\n    git add .\n    git commit -m \"Initial Unity project template\"\n    \n    print_success \"Unity项目模板创建完成\"\n}\n\n# 配置防火墙\nsetup_firewall() {\n    print_header \"配置防火墙\"\n    \n    # 启用UFW\n    sudo ufw --force enable\n    \n    # 允许SSH\n    sudo ufw allow ssh\n    \n    # 允许VS Code Server\n    sudo ufw allow 8080\n    \n    # 允许Syncthing\n    sudo ufw allow 8384\n    sudo ufw allow 22000\n    \n    print_success \"防火墙配置完成\"\n    sudo ufw status\n}\n\n# 创建启动脚本\ncreate_startup_scripts() {\n    print_header \"创建启动脚本\"\n    \n    # VS Code Server启动脚本\n    cat > ~/start-vscode.sh << 'EOF'\n#!/bin/bash\necho \"启动VS Code Server...\"\ncode-server --bind-addr 0.0.0.0:8080 ~/unity-projects\nEOF\n    chmod +x ~/start-vscode.sh\n    \n    # Syncthing启动脚本\n    cat > ~/start-syncthing.sh << 'EOF'\n#!/bin/bash\necho \"启动Syncthing...\"\nsyncthing -no-browser -gui-address=0.0.0.0:8384\nEOF\n    chmod +x ~/start-syncthing.sh\n    \n    # 综合启动脚本\n    cat > ~/start-dev-env.sh << 'EOF'\n#!/bin/bash\necho \"启动Unity云端开发环境...\"\n\n# 启动VS Code Server\nnohup code-server --bind-addr 0.0.0.0:8080 ~/unity-projects > ~/vscode.log 2>&1 &\n\n# 启动Syncthing\nnohup syncthing -no-browser -gui-address=0.0.0.0:8384 > ~/syncthing.log 2>&1 &\n\necho \"开发环境已启动\"\necho \"VS Code Server: http://$(curl -s ifconfig.me):8080\"\necho \"Syncthing: http://$(curl -s ifconfig.me):8384\"\necho \"\"\necho \"日志文件:\"\necho \"  VS Code: ~/vscode.log\"\necho \"  Syncthing: ~/syncthing.log\"\nEOF\n    chmod +x ~/start-dev-env.sh\n    \n    print_success \"启动脚本创建完成\"\n}\n\n# 显示完成信息\nshow_completion() {\n    print_header \"安装完成\"\n    \n    echo -e \"${GREEN}Unity云端开发环境安装完成！${NC}\"\n    echo \"\"\n    echo \"下一步操作：\"\n    echo \"1. 重新登录以使docker组权限生效\"\n    echo \"2. 运行 ~/start-dev-env.sh 启动开发环境\"\n    echo \"3. 访问 VS Code Server: http://$(curl -s ifconfig.me):8080\"\n    echo \"4. 访问 Syncthing: http://$(curl -s ifconfig.me):8384\"\n    echo \"\"\n    echo \"重要文件位置：\"\n    echo \"  VS Code配置: ~/.config/code-server/config.yaml\"\n    echo \"  Unity项目: ~/unity-projects/\"\n    echo \"  启动脚本: ~/start-dev-env.sh\"\n    echo \"\"\n    echo \"安全提醒：\"\n    echo \"  - 请修改VS Code Server密码\"\n    echo \"  - 配置Syncthing设备认证\"\n    echo \"  - 定期备份项目文件\"\n    echo \"\"\n    print_warning \"请记录VS Code Server密码: $(grep password ~/.config/code-server/config.yaml | cut -d' ' -f2)\"\n}\n\n# 主函数\nmain() {\n    print_header \"Unity云端开发环境安装\"\n    \n    check_system\n    update_system\n    install_docker\n    install_nodejs\n    install_vscode_server\n    install_unity_hub\n    setup_git\n    install_sync_tools\n    create_unity_template\n    setup_firewall\n    create_startup_scripts\n    show_completion\n}\n\n# 运行主函数\nmain \"$@\"\n"}