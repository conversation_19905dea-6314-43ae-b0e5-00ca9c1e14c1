{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "文件同步方案.md"}, "originalCode": "# Mac与Windows实时文件同步方案\n\n## 方案概述\n设计一个双向实时文件同步系统，确保Mac（A端）和Windows（B端）之间的文件保持一致，任一端修改文件后立即同步到另一端。\n\n## 速度对比分析\n\n### 🚀 最快方案：局域网直连同步（0.5-2秒）\n**适用场景：** Mac和Windows在同一局域网内\n\n**技术方案：**\n- 自建rsync + SSH（我提供的脚本）\n- Syncthing P2P同步\n- Resilio Sync\n\n**优势：**\n- 直接局域网传输，无需经过互联网\n- 千兆网络下可达100MB/s传输速度\n- 延迟极低，通常1-2秒内完成同步\n- 完全私有，数据不经过第三方\n\n### ⚡ 次快方案：专业同步工具（1-5秒）\n\n#### Syncthing（开源免费）\n- P2P技术，支持局域网发现\n- 自动选择最快路径（局域网优先）\n- 增量同步，只传输变化部分\n\n#### Resilio Sync（商业版）\n- BitTorrent技术，速度极快\n- 支持局域网和互联网混合同步\n- 企业级功能\n\n### 🌐 中等速度：云存储同步（2-10秒）\n\n## 推荐方案\n\n### 方案一：基于云存储的实时同步\n\n#### 1. Google Drive File Stream / OneDrive\n**优点：**\n- 官方支持，稳定可靠\n- 真正的实时同步（几秒内）\n- 自动冲突解决\n- 版本历史功能\n- 跨平台支持完善\n\n**实施步骤：**\n1. 在两台设备上安装Google Drive桌面版或OneDrive\n2. 将需要同步的文件夹移动到云盘目录\n3. 启用\"实时同步\"模式\n4. 配置同步规则和排除文件\n\n#### 2. Dropbox\n**优点：**\n- 同步速度最快（通常1-3秒）\n- 智能同步技术\n- 优秀的冲突处理\n- 支持局域网同步加速\n\n### 方案二：专业同步工具\n\n#### 1. Syncthing（开源免费）\n**优点：**\n- 完全去中心化，无需云服务\n- 端到端加密\n- 支持局域网直连\n- 开源免费\n\n**配置步骤：**\n```bash\n# Mac安装\nbrew install syncthing\n\n# Windows下载安装包\n# https://syncthing.net/downloads/\n```\n\n#### 2. Resilio Sync（商业版）\n**优点：**\n- P2P技术，速度极快\n- 支持局域网和互联网同步\n- 强大的权限控制\n\n### 方案三：自建同步服务\n\n#### 使用rsync + inotify实现实时同步\n\n**Mac端脚本：**\n```bash\n#!/bin/bash\n# mac_sync.sh\n\nWATCH_DIR=\"/path/to/sync/folder\"\nREMOTE_HOST=\"windows_ip\"\nREMOTE_USER=\"username\"\nREMOTE_DIR=\"/path/to/windows/folder\"\n\n# 安装fswatch\nbrew install fswatch\n\n# 监控文件变化并同步\nfswatch -o \"$WATCH_DIR\" | while read f; do\n    rsync -avz --delete \"$WATCH_DIR/\" \"$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/\"\n    echo \"$(date): 同步完成\"\ndone\n```\n\n**Windows端脚本（PowerShell）：**\n```powershell\n# windows_sync.ps1\n\n$watchDir = \"C:\\path\\to\\sync\\folder\"\n$remoteHost = \"mac_ip\"\n$remoteUser = \"username\"\n$remoteDir = \"/path/to/mac/folder\"\n\n# 使用FileSystemWatcher监控变化\n$watcher = New-Object System.IO.FileSystemWatcher\n$watcher.Path = $watchDir\n$watcher.IncludeSubdirectories = $true\n$watcher.EnableRaisingEvents = $true\n\n# 注册事件处理\nRegister-ObjectEvent -InputObject $watcher -EventName \"Changed\" -Action {\n    Start-Sleep -Seconds 1  # 防止重复触发\n    & rsync -avz --delete \"$watchDir/\" \"$remoteUser@$remoteHost:$remoteDir/\"\n    Write-Host \"$(Get-Date): 同步完成\"\n}\n```\n\n## 详细实施方案\n\n### 第一阶段：环境准备\n\n#### Mac端配置\n1. 安装必要工具\n```bash\n# 安装Homebrew（如果没有）\n/bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"\n\n# 安装同步工具\nbrew install rsync fswatch\n```\n\n2. 配置SSH密钥认证\n```bash\n# 生成SSH密钥\nssh-keygen -t rsa -b 4096 -C \"<EMAIL>\"\n\n# 复制公钥到Windows\nssh-copy-id username@windows_ip\n```\n\n#### Windows端配置\n1. 安装WSL2或使用Windows版rsync\n```powershell\n# 启用WSL2\nwsl --install\n\n# 或安装cwRsync\n# 下载：https://www.itefix.net/cwrsync\n```\n\n2. 配置SSH服务\n```bash\n# 在WSL中安装openssh-server\nsudo apt update\nsudo apt install openssh-server\n\n# 启动SSH服务\nsudo service ssh start\n```\n\n### 第二阶段：同步脚本部署\n\n#### 创建监控脚本\n```bash\n# 创建同步目录\nmkdir -p ~/sync_project\ncd ~/sync_project\n```\n\n#### 配置文件示例\n```yaml\n# sync_config.yaml\nsync:\n  local_path: \"/Users/<USER>/Documents/project\"\n  remote_host: \"*************\"\n  remote_user: \"windowsuser\"\n  remote_path: \"/mnt/c/Users/<USER>/Documents/project\"\n  exclude_patterns:\n    - \"*.tmp\"\n    - \".DS_Store\"\n    - \"Thumbs.db\"\n    - \"node_modules/\"\n    - \".git/\"\n  sync_delay: 2  # 秒\n  max_retries: 3\n```\n\n### 第三阶段：高级功能\n\n#### 冲突检测与解决\n```python\n# conflict_resolver.py\nimport os\nimport hashlib\nfrom datetime import datetime\n\ndef detect_conflict(local_file, remote_file):\n    \"\"\"检测文件冲突\"\"\"\n    local_hash = get_file_hash(local_file)\n    remote_hash = get_file_hash(remote_file)\n    \n    if local_hash != remote_hash:\n        return True\n    return False\n\ndef resolve_conflict(local_file, remote_file, strategy=\"timestamp\"):\n    \"\"\"解决冲突\"\"\"\n    if strategy == \"timestamp\":\n        local_time = os.path.getmtime(local_file)\n        remote_time = os.path.getmtime(remote_file)\n        \n        if local_time > remote_time:\n            return \"use_local\"\n        else:\n            return \"use_remote\"\n    \n    elif strategy == \"backup_both\":\n        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n        backup_name = f\"{local_file}.conflict_{timestamp}\"\n        os.rename(local_file, backup_name)\n        return \"backup_created\"\n```\n\n#### 性能优化\n```bash\n# 使用增量同步\nrsync -avz --delete --checksum --compress-level=9 \\\n      --exclude-from=exclude.txt \\\n      \"$SOURCE/\" \"$DESTINATION/\"\n\n# 启用并行传输\nrsync -avz --delete --parallel=4 \"$SOURCE/\" \"$DESTINATION/\"\n```\n\n## 监控与日志\n\n### 日志记录\n```bash\n# 创建日志目录\nmkdir -p ~/sync_logs\n\n# 日志轮转配置\ncat > ~/sync_logs/logrotate.conf << EOF\n~/sync_logs/sync.log {\n    daily\n    rotate 7\n    compress\n    missingok\n    notifempty\n}\nEOF\n```\n\n### 状态监控\n```python\n# sync_monitor.py\nimport psutil\nimport time\nimport json\n\ndef monitor_sync_status():\n    \"\"\"监控同步状态\"\"\"\n    status = {\n        \"timestamp\": time.time(),\n        \"cpu_usage\": psutil.cpu_percent(),\n        \"memory_usage\": psutil.virtual_memory().percent,\n        \"disk_io\": psutil.disk_io_counters(),\n        \"network_io\": psutil.net_io_counters()\n    }\n    \n    with open(\"sync_status.json\", \"w\") as f:\n        json.dump(status, f, indent=2)\n```\n\n## 故障排除\n\n### 常见问题\n1. **网络连接问题**\n   - 检查防火墙设置\n   - 验证SSH连接\n   - 测试网络延迟\n\n2. **权限问题**\n   - 确保用户有读写权限\n   - 检查SSH密钥配置\n   - 验证目录权限\n\n3. **同步延迟**\n   - 调整监控间隔\n   - 优化网络配置\n   - 使用压缩传输\n\n### 性能调优\n```bash\n# 网络优化\necho 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf\necho 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf\n\n# SSH优化\necho 'Compression yes' >> ~/.ssh/config\necho 'CompressionLevel 6' >> ~/.ssh/config\n```\n\n## 安全考虑\n\n1. **加密传输**：使用SSH或TLS加密\n2. **访问控制**：限制IP访问范围\n3. **备份策略**：定期创建备份点\n4. **监控告警**：异常情况及时通知\n\n## 总结\n\n推荐使用**Google Drive File Stream**或**Syncthing**作为主要方案：\n- Google Drive：适合对稳定性要求高的场景\n- Syncthing：适合对隐私和控制要求高的场景\n- 自建方案：适合有特殊需求的技术团队\n\n选择方案时需要考虑：网络环境、文件大小、同步频率、安全要求等因素。\n", "modifiedCode": "# Mac与Windows实时文件同步方案\n\n## 方案概述\n设计一个双向实时文件同步系统，确保Mac（A端）和Windows（B端）之间的文件保持一致，任一端修改文件后立即同步到另一端。\n\n## 速度对比分析\n\n### 🚀 最快方案：局域网直连同步（0.5-2秒）\n**适用场景：** Mac和Windows在同一局域网内\n\n**技术方案：**\n- 自建rsync + SSH（我提供的脚本）\n- Syncthing P2P同步\n- Resilio Sync\n\n**优势：**\n- 直接局域网传输，无需经过互联网\n- 千兆网络下可达100MB/s传输速度\n- 延迟极低，通常1-2秒内完成同步\n- 完全私有，数据不经过第三方\n\n### ⚡ 次快方案：专业同步工具（1-5秒）\n\n#### Syncthing（开源免费）\n- P2P技术，支持局域网发现\n- 自动选择最快路径（局域网优先）\n- 增量同步，只传输变化部分\n\n#### Resilio Sync（商业版）\n- BitTorrent技术，速度极快\n- 支持局域网和互联网混合同步\n- 企业级功能\n\n### 🌐 中等速度：云存储同步（2-10秒）\n\n## 推荐方案\n\n### 方案一：基于云存储的实时同步\n\n#### 1. Google Drive File Stream / OneDrive\n**优点：**\n- 官方支持，稳定可靠\n- 真正的实时同步（几秒内）\n- 自动冲突解决\n- 版本历史功能\n- 跨平台支持完善\n\n**实施步骤：**\n1. 在两台设备上安装Google Drive桌面版或OneDrive\n2. 将需要同步的文件夹移动到云盘目录\n3. 启用\"实时同步\"模式\n4. 配置同步规则和排除文件\n\n#### 2. Dropbox\n**优点：**\n- 同步速度最快（通常1-3秒）\n- 智能同步技术\n- 优秀的冲突处理\n- 支持局域网同步加速\n\n### 方案二：专业同步工具\n\n#### 1. Syncthing（开源免费）\n**优点：**\n- 完全去中心化，无需云服务\n- 端到端加密\n- 支持局域网直连\n- 开源免费\n\n**配置步骤：**\n```bash\n# Mac安装\nbrew install syncthing\n\n# Windows下载安装包\n# https://syncthing.net/downloads/\n```\n\n#### 2. Resilio Sync（商业版）\n**优点：**\n- P2P技术，速度极快\n- 支持局域网和互联网同步\n- 强大的权限控制\n\n### 方案三：自建同步服务\n\n#### 使用rsync + inotify实现实时同步\n\n**Mac端脚本：**\n```bash\n#!/bin/bash\n# mac_sync.sh\n\nWATCH_DIR=\"/path/to/sync/folder\"\nREMOTE_HOST=\"windows_ip\"\nREMOTE_USER=\"username\"\nREMOTE_DIR=\"/path/to/windows/folder\"\n\n# 安装fswatch\nbrew install fswatch\n\n# 监控文件变化并同步\nfswatch -o \"$WATCH_DIR\" | while read f; do\n    rsync -avz --delete \"$WATCH_DIR/\" \"$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/\"\n    echo \"$(date): 同步完成\"\ndone\n```\n\n**Windows端脚本（PowerShell）：**\n```powershell\n# windows_sync.ps1\n\n$watchDir = \"C:\\path\\to\\sync\\folder\"\n$remoteHost = \"mac_ip\"\n$remoteUser = \"username\"\n$remoteDir = \"/path/to/mac/folder\"\n\n# 使用FileSystemWatcher监控变化\n$watcher = New-Object System.IO.FileSystemWatcher\n$watcher.Path = $watchDir\n$watcher.IncludeSubdirectories = $true\n$watcher.EnableRaisingEvents = $true\n\n# 注册事件处理\nRegister-ObjectEvent -InputObject $watcher -EventName \"Changed\" -Action {\n    Start-Sleep -Seconds 1  # 防止重复触发\n    & rsync -avz --delete \"$watchDir/\" \"$remoteUser@$remoteHost:$remoteDir/\"\n    Write-Host \"$(Get-Date): 同步完成\"\n}\n```\n\n## 详细实施方案\n\n### 第一阶段：环境准备\n\n#### Mac端配置\n1. 安装必要工具\n```bash\n# 安装Homebrew（如果没有）\n/bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"\n\n# 安装同步工具\nbrew install rsync fswatch\n```\n\n2. 配置SSH密钥认证\n```bash\n# 生成SSH密钥\nssh-keygen -t rsa -b 4096 -C \"<EMAIL>\"\n\n# 复制公钥到Windows\nssh-copy-id username@windows_ip\n```\n\n#### Windows端配置\n1. 安装WSL2或使用Windows版rsync\n```powershell\n# 启用WSL2\nwsl --install\n\n# 或安装cwRsync\n# 下载：https://www.itefix.net/cwrsync\n```\n\n2. 配置SSH服务\n```bash\n# 在WSL中安装openssh-server\nsudo apt update\nsudo apt install openssh-server\n\n# 启动SSH服务\nsudo service ssh start\n```\n\n### 第二阶段：同步脚本部署\n\n#### 创建监控脚本\n```bash\n# 创建同步目录\nmkdir -p ~/sync_project\ncd ~/sync_project\n```\n\n#### 配置文件示例\n```yaml\n# sync_config.yaml\nsync:\n  local_path: \"/Users/<USER>/Documents/project\"\n  remote_host: \"*************\"\n  remote_user: \"windowsuser\"\n  remote_path: \"/mnt/c/Users/<USER>/Documents/project\"\n  exclude_patterns:\n    - \"*.tmp\"\n    - \".DS_Store\"\n    - \"Thumbs.db\"\n    - \"node_modules/\"\n    - \".git/\"\n  sync_delay: 2  # 秒\n  max_retries: 3\n```\n\n### 第三阶段：高级功能\n\n#### 冲突检测与解决\n```python\n# conflict_resolver.py\nimport os\nimport hashlib\nfrom datetime import datetime\n\ndef detect_conflict(local_file, remote_file):\n    \"\"\"检测文件冲突\"\"\"\n    local_hash = get_file_hash(local_file)\n    remote_hash = get_file_hash(remote_file)\n    \n    if local_hash != remote_hash:\n        return True\n    return False\n\ndef resolve_conflict(local_file, remote_file, strategy=\"timestamp\"):\n    \"\"\"解决冲突\"\"\"\n    if strategy == \"timestamp\":\n        local_time = os.path.getmtime(local_file)\n        remote_time = os.path.getmtime(remote_file)\n        \n        if local_time > remote_time:\n            return \"use_local\"\n        else:\n            return \"use_remote\"\n    \n    elif strategy == \"backup_both\":\n        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n        backup_name = f\"{local_file}.conflict_{timestamp}\"\n        os.rename(local_file, backup_name)\n        return \"backup_created\"\n```\n\n#### 性能优化\n```bash\n# 使用增量同步\nrsync -avz --delete --checksum --compress-level=9 \\\n      --exclude-from=exclude.txt \\\n      \"$SOURCE/\" \"$DESTINATION/\"\n\n# 启用并行传输\nrsync -avz --delete --parallel=4 \"$SOURCE/\" \"$DESTINATION/\"\n```\n\n## 监控与日志\n\n### 日志记录\n```bash\n# 创建日志目录\nmkdir -p ~/sync_logs\n\n# 日志轮转配置\ncat > ~/sync_logs/logrotate.conf << EOF\n~/sync_logs/sync.log {\n    daily\n    rotate 7\n    compress\n    missingok\n    notifempty\n}\nEOF\n```\n\n### 状态监控\n```python\n# sync_monitor.py\nimport psutil\nimport time\nimport json\n\ndef monitor_sync_status():\n    \"\"\"监控同步状态\"\"\"\n    status = {\n        \"timestamp\": time.time(),\n        \"cpu_usage\": psutil.cpu_percent(),\n        \"memory_usage\": psutil.virtual_memory().percent,\n        \"disk_io\": psutil.disk_io_counters(),\n        \"network_io\": psutil.net_io_counters()\n    }\n    \n    with open(\"sync_status.json\", \"w\") as f:\n        json.dump(status, f, indent=2)\n```\n\n## 故障排除\n\n### 常见问题\n1. **网络连接问题**\n   - 检查防火墙设置\n   - 验证SSH连接\n   - 测试网络延迟\n\n2. **权限问题**\n   - 确保用户有读写权限\n   - 检查SSH密钥配置\n   - 验证目录权限\n\n3. **同步延迟**\n   - 调整监控间隔\n   - 优化网络配置\n   - 使用压缩传输\n\n### 性能调优\n```bash\n# 网络优化\necho 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf\necho 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf\n\n# SSH优化\necho 'Compression yes' >> ~/.ssh/config\necho 'CompressionLevel 6' >> ~/.ssh/config\n```\n\n## 安全考虑\n\n1. **加密传输**：使用SSH或TLS加密\n2. **访问控制**：限制IP访问范围\n3. **备份策略**：定期创建备份点\n4. **监控告警**：异常情况及时通知\n\n## 总结\n\n推荐使用**Google Drive File Stream**或**Syncthing**作为主要方案：\n- Google Drive：适合对稳定性要求高的场景\n- Syncthing：适合对隐私和控制要求高的场景\n- 自建方案：适合有特殊需求的技术团队\n\n选择方案时需要考虑：网络环境、文件大小、同步频率、安全要求等因素。\n"}