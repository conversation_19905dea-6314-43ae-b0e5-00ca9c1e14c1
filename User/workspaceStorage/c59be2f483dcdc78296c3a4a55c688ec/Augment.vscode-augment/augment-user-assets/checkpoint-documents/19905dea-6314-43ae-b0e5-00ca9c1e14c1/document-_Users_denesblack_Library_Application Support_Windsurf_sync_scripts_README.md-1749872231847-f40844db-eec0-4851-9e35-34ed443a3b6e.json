{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/README.md"}, "modifiedCode": "# Mac与Windows实时文件同步工具\n\n这是一套完整的Mac与Windows双向实时文件同步解决方案，支持秒级同步，自动冲突处理，断线重连等功能。\n\n## 功能特性\n\n- ✅ **实时同步**: 文件变化后2秒内完成同步\n- ✅ **双向支持**: Mac ↔ Windows 双向同步\n- ✅ **自动重试**: 网络异常时自动重试\n- ✅ **冲突处理**: 智能处理文件冲突\n- ✅ **排除规则**: 支持自定义排除文件/目录\n- ✅ **日志记录**: 详细的同步日志\n- ✅ **断线重连**: 网络恢复后自动重连\n- ✅ **系统服务**: 支持开机自启动\n\n## 系统要求\n\n### Mac端\n- macOS 10.12 或更高版本\n- Homebrew 包管理器\n- 网络连接\n\n### Windows端\n- Windows 10 或更高版本\n- WSL2 或 cwRsync\n- PowerShell 5.0 或更高版本\n- SSH 服务\n\n## 快速开始\n\n### 1. Mac端安装\n\n```bash\n# 克隆或下载脚本文件\ncd sync_scripts\n\n# 运行安装脚本\nchmod +x setup.sh\n./setup.sh\n```\n\n安装脚本会自动：\n- 安装 Homebrew（如果未安装）\n- 安装 fswatch 和 rsync\n- 生成 SSH 密钥对\n- 创建配置文件\n- 设置启动脚本\n\n### 2. Windows端配置\n\n#### 方法一：使用WSL2（推荐）\n\n```powershell\n# 安装WSL2\nwsl --install\n\n# 在WSL中安装SSH服务\nsudo apt update\nsudo apt install openssh-server rsync\n\n# 启动SSH服务\nsudo service ssh start\n\n# 设置开机自启动\nsudo systemctl enable ssh\n```\n\n#### 方法二：使用cwRsync\n\n1. 下载并安装 [cwRsync](https://www.itefix.net/cwrsync)\n2. 配置环境变量\n3. 安装SSH服务\n\n### 3. SSH密钥配置\n\n在Mac端运行安装脚本后，会显示SSH公钥，需要将其添加到Windows端：\n\n```bash\n# Windows端（在WSL中执行）\nmkdir -p ~/.ssh\necho \"你的SSH公钥内容\" >> ~/.ssh/authorized_keys\nchmod 600 ~/.ssh/authorized_keys\nchmod 700 ~/.ssh\n```\n\n### 4. 配置同步参数\n\n编辑配置文件：\n\n**Mac端** (`sync_config.conf`):\n```bash\nLOCAL_DIR=\"/Users/<USER>/Documents/project\"\nREMOTE_HOST=\"*************\"\nREMOTE_USER=\"windowsuser\"\nREMOTE_DIR=\"/mnt/c/Users/<USER>/Documents/project\"\n```\n\n**Windows端** (`sync_config.json`):\n```json\n{\n  \"localDir\": \"C:\\\\Users\\\\<USER>\\\\Documents\\\\project\",\n  \"remoteHost\": \"*************\",\n  \"remoteUser\": \"macuser\",\n  \"remoteDir\": \"/Users/<USER>/Documents/project\"\n}\n```\n\n### 5. 测试连接\n\n```bash\n# Mac端测试\n./setup.sh --test\n\n# Windows端测试\n.\\windows_realtime_sync.ps1 -Test\n```\n\n### 6. 启动同步\n\n```bash\n# Mac端启动\n./start_sync.sh\n\n# Windows端启动\n.\\windows_realtime_sync.ps1\n```\n\n## 详细配置\n\n### 排除文件规则\n\n编辑 `exclude.txt` 文件来设置不需要同步的文件：\n\n```\n# 系统文件\n.DS_Store\nThumbs.db\n\n# 临时文件\n*.tmp\n*.temp\n\n# 开发文件\nnode_modules/\n.git/\n.vscode/\n```\n\n### 高级配置选项\n\n#### Mac端配置 (`sync_config.conf`)\n\n```bash\nSYNC_DELAY=2          # 同步延迟（秒）\nMAX_RETRIES=3         # 最大重试次数\nCOMPRESS_LEVEL=6      # 压缩级别（1-9）\nSHOW_PROGRESS=false   # 显示进度\nLOG_LEVEL=\"INFO\"      # 日志级别\n```\n\n#### Windows端配置 (`sync_config.json`)\n\n```json\n{\n  \"syncDelay\": 2,\n  \"maxRetries\": 3,\n  \"compressLevel\": 6,\n  \"showProgress\": false,\n  \"logLevel\": \"INFO\",\n  \"notifications\": {\n    \"enabled\": true,\n    \"onError\": true\n  },\n  \"backup\": {\n    \"enabled\": true,\n    \"maxBackups\": 5\n  }\n}\n```\n\n## 使用说明\n\n### 启动同步服务\n\n```bash\n# Mac端\n./mac_realtime_sync.sh\n\n# 或使用启动器\n./start_sync.sh\n\n# Windows端\n.\\windows_realtime_sync.ps1\n\n# 后台运行\n.\\windows_realtime_sync.ps1 &\n```\n\n### 命令行选项\n\n#### Mac端选项\n\n```bash\n./mac_realtime_sync.sh [选项]\n\n选项:\n  -h, --help      显示帮助信息\n  -c, --config    指定配置文件\n  -t, --test      仅测试连接\n  -i, --init      仅执行初始同步\n  -v, --verbose   详细输出\n```\n\n#### Windows端选项\n\n```powershell\n.\\windows_realtime_sync.ps1 [参数]\n\n参数:\n  -ConfigFile     指定配置文件\n  -Test          仅测试连接\n  -InitialSync   仅执行初始同步\n  -Help          显示帮助信息\n  -Verbose       详细输出\n```\n\n### 系统服务配置\n\n#### Mac端（LaunchAgent）\n\n```bash\n# 加载服务\nlaunchctl load ~/Library/LaunchAgents/com.filesync.realtime.plist\n\n# 卸载服务\nlaunchctl unload ~/Library/LaunchAgents/com.filesync.realtime.plist\n\n# 查看服务状态\nlaunchctl list | grep filesync\n```\n\n#### Windows端（计划任务）\n\n```powershell\n# 创建计划任务\n$action = New-ScheduledTaskAction -Execute \"PowerShell.exe\" -Argument \"-File C:\\path\\to\\windows_realtime_sync.ps1\"\n$trigger = New-ScheduledTaskTrigger -AtStartup\nRegister-ScheduledTask -TaskName \"FileSync\" -Action $action -Trigger $trigger\n```\n\n## 监控和日志\n\n### 日志文件位置\n\n- **Mac端**: `sync_scripts/logs/sync_YYYYMMDD.log`\n- **Windows端**: `sync_scripts/logs/sync_YYYYMMDD.log`\n\n### 日志级别\n\n- `DEBUG`: 详细调试信息\n- `INFO`: 一般信息\n- `WARN`: 警告信息\n- `ERROR`: 错误信息\n\n### 监控同步状态\n\n```bash\n# 实时查看日志\ntail -f logs/sync_$(date +%Y%m%d).log\n\n# 查看同步统计\ngrep \"同步完成\" logs/sync_$(date +%Y%m%d).log | wc -l\n```\n\n## 故障排除\n\n### 常见问题\n\n1. **SSH连接失败**\n   ```bash\n   # 检查SSH服务状态\n   ssh -v username@hostname\n   \n   # 检查密钥权限\n   chmod 600 ~/.ssh/id_rsa\n   chmod 644 ~/.ssh/id_rsa.pub\n   ```\n\n2. **同步延迟过高**\n   ```bash\n   # 检查网络延迟\n   ping remote_host\n   \n   # 调整同步参数\n   SYNC_DELAY=1\n   COMPRESS_LEVEL=3\n   ```\n\n3. **文件冲突**\n   ```bash\n   # 查看冲突文件\n   find . -name \"*.conflict_*\"\n   \n   # 手动解决冲突\n   # 选择保留的版本，删除冲突文件\n   ```\n\n### 性能优化\n\n1. **网络优化**\n   ```bash\n   # 启用SSH压缩\n   echo \"Compression yes\" >> ~/.ssh/config\n   \n   # 调整TCP窗口大小\n   echo \"net.core.rmem_max = 16777216\" >> /etc/sysctl.conf\n   ```\n\n2. **同步优化**\n   ```bash\n   # 减少同步延迟\n   SYNC_DELAY=1\n   \n   # 启用并行传输\n   rsync --parallel=4\n   ```\n\n## 安全建议\n\n1. **使用密钥认证**: 禁用密码登录\n2. **限制IP访问**: 配置防火墙规则\n3. **定期备份**: 启用自动备份功能\n4. **监控日志**: 定期检查异常活动\n\n## 许可证\n\nMIT License\n\n## 支持\n\n如有问题，请查看：\n1. 日志文件中的错误信息\n2. 网络连接状态\n3. SSH配置是否正确\n4. 文件权限设置\n\n---\n\n**注意**: 首次使用前请仔细阅读配置说明，确保网络和权限设置正确。\n"}