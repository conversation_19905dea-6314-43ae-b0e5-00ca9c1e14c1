{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/sync_config.json"}, "modifiedCode": "{\n  \"localDir\": \"C:\\\\Users\\\\<USER>\\\\Documents\\\\project\",\n  \"remoteHost\": \"*************\",\n  \"remoteUser\": \"macuser\",\n  \"remoteDir\": \"/Users/<USER>/Documents/project\",\n  \"syncDelay\": 2,\n  \"maxRetries\": 3,\n  \"showProgress\": false,\n  \"sshPort\": 22,\n  \"connectionTimeout\": 10,\n  \"compressLevel\": 6,\n  \"parallelTransfers\": 1,\n  \"bandwidthLimit\": \"\",\n  \"logLevel\": \"INFO\",\n  \"logRetentionDays\": 7,\n  \"healthCheckInterval\": 300,\n  \"autoRestart\": true,\n  \"excludePatterns\": [\n    \"*.tmp\",\n    \"*.temp\",\n    \"~*\",\n    \".DS_Store\",\n    \"Thumbs.db\",\n    \"desktop.ini\",\n    \"node_modules\\\\\",\n    \".git\\\\\",\n    \".svn\\\\\",\n    \".vscode\\\\\",\n    \"*.log\",\n    \"*.cache\"\n  ],\n  \"includePatterns\": [\n    \"*.txt\",\n    \"*.md\",\n    \"*.js\",\n    \"*.ts\",\n    \"*.py\",\n    \"*.java\",\n    \"*.cpp\",\n    \"*.h\",\n    \"*.css\",\n    \"*.html\",\n    \"*.json\",\n    \"*.xml\",\n    \"*.yaml\",\n    \"*.yml\"\n  ],\n  \"notifications\": {\n    \"enabled\": true,\n    \"onSuccess\": false,\n    \"onError\": true,\n    \"onConflict\": true\n  },\n  \"backup\": {\n    \"enabled\": true,\n    \"maxBackups\": 5,\n    \"backupDir\": \"C:\\\\Users\\\\<USER>\\\\Documents\\\\sync_backups\"\n  },\n  \"security\": {\n    \"encryptTransfer\": true,\n    \"verifyChecksums\": true,\n    \"allowedHosts\": [\n      \"*************\",\n      \"macbook.local\"\n    ]\n  }\n}\n"}