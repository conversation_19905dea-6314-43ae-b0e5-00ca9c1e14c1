{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/speed_test.sh"}, "modifiedCode": "#!/bin/bash\n\n# 同步速度测试脚本\n# 用于测试不同配置下的同步性能\n\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nTEST_DIR=\"$SCRIPT_DIR/speed_test\"\nRESULTS_FILE=\"$SCRIPT_DIR/speed_test_results.txt\"\n\n# 颜色输出\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m'\n\nprint_header() {\n    echo -e \"${BLUE}================================${NC}\"\n    echo -e \"${BLUE}$1${NC}\"\n    echo -e \"${BLUE}================================${NC}\"\n}\n\nprint_success() {\n    echo -e \"${GREEN}✓ $1${NC}\"\n}\n\nprint_warning() {\n    echo -e \"${YELLOW}⚠ $1${NC}\"\n}\n\nprint_error() {\n    echo -e \"${RED}✗ $1${NC}\"\n}\n\n# 创建测试环境\nsetup_test_env() {\n    print_header \"设置测试环境\"\n    \n    # 创建测试目录\n    mkdir -p \"$TEST_DIR\"\n    cd \"$TEST_DIR\"\n    \n    # 创建不同大小的测试文件\n    echo \"创建测试文件...\"\n    \n    # 小文件 (1KB)\n    for i in {1..10}; do\n        echo \"这是测试文件 $i $(date)\" > \"small_file_$i.txt\"\n    done\n    \n    # 中等文件 (100KB)\n    for i in {1..5}; do\n        head -c 100000 /dev/urandom > \"medium_file_$i.bin\"\n    done\n    \n    # 大文件 (1MB)\n    for i in {1..2}; do\n        head -c 1000000 /dev/urandom > \"large_file_$i.bin\"\n    done\n    \n    print_success \"测试文件创建完成\"\n    ls -lh\n}\n\n# 测试网络延迟\ntest_network_latency() {\n    print_header \"网络延迟测试\"\n    \n    if [[ -f \"$SCRIPT_DIR/sync_config.conf\" ]]; then\n        source \"$SCRIPT_DIR/sync_config.conf\"\n        \n        echo \"测试到 $REMOTE_HOST 的网络延迟...\"\n        \n        # ping测试\n        ping_result=$(ping -c 5 \"$REMOTE_HOST\" | tail -1 | awk -F'/' '{print $5}')\n        echo \"平均延迟: ${ping_result}ms\"\n        \n        # SSH连接测试\n        ssh_start=$(date +%s.%N)\n        ssh \"$REMOTE_USER@$REMOTE_HOST\" \"echo 'SSH连接测试'\" > /dev/null 2>&1\n        ssh_end=$(date +%s.%N)\n        ssh_latency=$(echo \"$ssh_end - $ssh_start\" | bc)\n        echo \"SSH连接延迟: ${ssh_latency}秒\"\n        \n        # 带宽测试（简单）\n        echo \"测试传输带宽...\"\n        dd if=/dev/zero bs=1M count=10 2>/dev/null | ssh \"$REMOTE_USER@$REMOTE_HOST\" \"cat > /dev/null\"\n        \n    else\n        print_error \"配置文件不存在\"\n    fi\n}\n\n# 测试同步速度\ntest_sync_speed() {\n    local config_file=\"$1\"\n    local test_name=\"$2\"\n    \n    print_header \"同步速度测试: $test_name\"\n    \n    if [[ ! -f \"$config_file\" ]]; then\n        print_error \"配置文件不存在: $config_file\"\n        return 1\n    fi\n    \n    # 备份原配置\n    cp \"$SCRIPT_DIR/sync_config.conf\" \"$SCRIPT_DIR/sync_config.conf.backup\" 2>/dev/null\n    \n    # 使用测试配置\n    cp \"$config_file\" \"$SCRIPT_DIR/sync_config.conf\"\n    \n    # 记录开始时间\n    start_time=$(date +%s.%N)\n    \n    # 执行同步\n    echo \"开始同步测试...\"\n    bash \"$SCRIPT_DIR/mac_realtime_sync.sh\" --init > /dev/null 2>&1\n    \n    if [[ $? -eq 0 ]]; then\n        # 记录结束时间\n        end_time=$(date +%s.%N)\n        duration=$(echo \"$end_time - $start_time\" | bc)\n        \n        # 计算文件大小\n        total_size=$(du -sb \"$TEST_DIR\" | cut -f1)\n        total_size_mb=$(echo \"scale=2; $total_size / 1024 / 1024\" | bc)\n        \n        # 计算速度\n        speed_mbps=$(echo \"scale=2; $total_size_mb / $duration\" | bc)\n        \n        print_success \"同步完成\"\n        echo \"总大小: ${total_size_mb}MB\"\n        echo \"耗时: ${duration}秒\"\n        echo \"速度: ${speed_mbps}MB/s\"\n        \n        # 记录结果\n        echo \"[$test_name] 大小:${total_size_mb}MB 耗时:${duration}s 速度:${speed_mbps}MB/s\" >> \"$RESULTS_FILE\"\n        \n    else\n        print_error \"同步失败\"\n        echo \"[$test_name] 同步失败\" >> \"$RESULTS_FILE\"\n    fi\n    \n    # 恢复原配置\n    mv \"$SCRIPT_DIR/sync_config.conf.backup\" \"$SCRIPT_DIR/sync_config.conf\" 2>/dev/null\n}\n\n# 测试文件变化检测速度\ntest_file_change_detection() {\n    print_header \"文件变化检测速度测试\"\n    \n    # 创建测试文件\n    test_file=\"$TEST_DIR/change_test.txt\"\n    echo \"初始内容\" > \"$test_file\"\n    \n    # 启动文件监控\n    fswatch -o \"$TEST_DIR\" &\n    fswatch_pid=$!\n    \n    sleep 1\n    \n    # 记录开始时间\n    start_time=$(date +%s.%N)\n    \n    # 修改文件\n    echo \"修改内容 $(date)\" >> \"$test_file\"\n    \n    # 等待检测到变化\n    sleep 0.5\n    \n    # 停止监控\n    kill $fswatch_pid 2>/dev/null\n    \n    end_time=$(date +%s.%N)\n    detection_time=$(echo \"$end_time - $start_time\" | bc)\n    \n    echo \"文件变化检测时间: ${detection_time}秒\"\n}\n\n# 比较不同配置的性能\ncompare_configurations() {\n    print_header \"配置性能对比\"\n    \n    # 清空结果文件\n    > \"$RESULTS_FILE\"\n    \n    # 测试标准配置\n    if [[ -f \"$SCRIPT_DIR/sync_config.conf\" ]]; then\n        test_sync_speed \"$SCRIPT_DIR/sync_config.conf\" \"标准配置\"\n    fi\n    \n    # 测试速度优化配置\n    if [[ -f \"$SCRIPT_DIR/speed_optimized_config.conf\" ]]; then\n        test_sync_speed \"$SCRIPT_DIR/speed_optimized_config.conf\" \"速度优化配置\"\n    fi\n    \n    # 显示对比结果\n    print_header \"测试结果对比\"\n    cat \"$RESULTS_FILE\"\n}\n\n# 生成性能报告\ngenerate_report() {\n    print_header \"生成性能报告\"\n    \n    local report_file=\"$SCRIPT_DIR/performance_report_$(date +%Y%m%d_%H%M%S).md\"\n    \n    cat > \"$report_file\" << EOF\n# 文件同步性能测试报告\n\n**测试时间:** $(date)\n**测试环境:** $(uname -a)\n\n## 网络环境\n\n$(test_network_latency 2>&1)\n\n## 同步性能测试\n\n$(cat \"$RESULTS_FILE\" 2>/dev/null)\n\n## 文件变化检测\n\n$(test_file_change_detection 2>&1)\n\n## 优化建议\n\n### 局域网环境优化\n- 使用 --whole-file 参数\n- 禁用压缩 (--no-compress)\n- 增加并行传输数量\n- 使用快速加密算法\n\n### 互联网环境优化\n- 启用压缩 (--compress-level=6)\n- 使用增量同步\n- 限制带宽使用\n- 启用断点续传\n\n### 系统优化\n- 调整TCP窗口大小\n- 优化SSH配置\n- 使用SSD存储\n- 增加内存缓冲区\n\nEOF\n\n    print_success \"性能报告已生成: $report_file\"\n}\n\n# 清理测试环境\ncleanup() {\n    print_header \"清理测试环境\"\n    \n    if [[ -d \"$TEST_DIR\" ]]; then\n        rm -rf \"$TEST_DIR\"\n        print_success \"测试目录已清理\"\n    fi\n    \n    if [[ -f \"$RESULTS_FILE\" ]]; then\n        rm -f \"$RESULTS_FILE\"\n        print_success \"测试结果已清理\"\n    fi\n}\n\n# 显示帮助信息\nshow_help() {\n    cat << EOF\n文件同步速度测试工具\n\n用法: $0 [选项]\n\n选项:\n    -s, --setup        设置测试环境\n    -n, --network      测试网络延迟\n    -c, --compare      比较不同配置性能\n    -d, --detection    测试文件变化检测速度\n    -r, --report       生成性能报告\n    -C, --cleanup      清理测试环境\n    -h, --help         显示帮助信息\n\n示例:\n    $0 --setup         # 设置测试环境\n    $0 --compare       # 运行性能对比测试\n    $0 --report        # 生成完整报告\n    $0 --cleanup       # 清理测试文件\n\nEOF\n}\n\n# 主函数\nmain() {\n    case \"${1:-}\" in\n        -s|--setup)\n            setup_test_env\n            ;;\n        -n|--network)\n            test_network_latency\n            ;;\n        -c|--compare)\n            setup_test_env\n            compare_configurations\n            ;;\n        -d|--detection)\n            setup_test_env\n            test_file_change_detection\n            ;;\n        -r|--report)\n            setup_test_env\n            compare_configurations\n            generate_report\n            ;;\n        -C|--cleanup)\n            cleanup\n            ;;\n        -h|--help)\n            show_help\n            ;;\n        *)\n            echo \"运行完整性能测试...\"\n            setup_test_env\n            test_network_latency\n            compare_configurations\n            test_file_change_detection\n            generate_report\n            ;;\n    esac\n}\n\nmain \"$@\"\n"}