{"path": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/windows_realtime_sync.ps1"}, "originalCode": "# Windows端实时同步脚本\n# 使用FileSystemWatcher监控文件变化，通过rsync实现实时同步\n\nparam(\n    [string]$ConfigFile = \"sync_config.json\",\n    [switch]$Test,\n    [switch]$InitialSync,\n    [switch]$Help,\n    [switch]$Verbose\n)\n\n# 全局变量\n$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path\n$LogDir = Join-Path $ScriptDir \"logs\"\n$LogFile = Join-Path $LogDir \"sync_$(Get-Date -Format 'yyyyMMdd').log\"\n\n# 创建日志目录\nif (!(Test-Path $LogDir)) {\n    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null\n}\n\n# 日志函数\nfunction Write-Log {\n    param([string]$Message, [string]$Level = \"INFO\")\n    $timestamp = Get-Date -Format \"yyyy-MM-dd HH:mm:ss\"\n    $logMessage = \"[$timestamp] [$Level] $Message\"\n    Write-Host $logMessage\n    Add-Content -Path $LogFile -Value $logMessage\n}\n\n# 显示帮助信息\nfunction Show-Help {\n    @\"\nWindows端实时文件同步脚本\n\n用法: .\\windows_realtime_sync.ps1 [参数]\n\n参数:\n    -ConfigFile <path>  指定配置文件路径 (默认: sync_config.json)\n    -Test              仅测试连接，不启动同步\n    -InitialSync       仅执行初始同步\n    -Help              显示此帮助信息\n    -Verbose           详细输出模式\n\n配置文件示例 (sync_config.json):\n{\n    \"localDir\": \"C:\\\\Users\\\\<USER>\\\\Documents\\\\project\",\n    \"remoteHost\": \"*************\",\n    \"remoteUser\": \"macuser\",\n    \"remoteDir\": \"/Users/<USER>/Documents/project\",\n    \"syncDelay\": 2,\n    \"maxRetries\": 3,\n    \"showProgress\": false,\n    \"excludePatterns\": [\n        \"*.tmp\",\n        \"Thumbs.db\",\n        \".DS_Store\",\n        \"node_modules\\\\\",\n        \".git\\\\\"\n    ]\n}\n\n前置要求:\n1. 安装WSL2或cwRsync\n2. 配置SSH密钥认证\n3. 确保网络连通性\n\n\"@\n}\n\n# 加载配置文件\nfunction Load-Config {\n    param([string]$ConfigPath)\n    \n    $fullPath = Join-Path $ScriptDir $ConfigPath\n    if (!(Test-Path $fullPath)) {\n        Write-Log \"错误: 配置文件 $fullPath 不存在\" \"ERROR\"\n        return $null\n    }\n    \n    try {\n        $config = Get-Content $fullPath | ConvertFrom-Json\n        Write-Log \"配置文件加载成功\"\n        return $config\n    }\n    catch {\n        Write-Log \"错误: 配置文件格式无效 - $($_.Exception.Message)\" \"ERROR\"\n        return $null\n    }\n}\n\n# 验证配置\nfunction Test-Config {\n    param($Config)\n    \n    $required = @(\"localDir\", \"remoteHost\", \"remoteUser\", \"remoteDir\")\n    foreach ($field in $required) {\n        if (!$Config.$field) {\n            Write-Log \"错误: 配置项 $field 缺失\" \"ERROR\"\n            return $false\n        }\n    }\n    \n    if (!(Test-Path $Config.localDir)) {\n        Write-Log \"错误: 本地目录 $($Config.localDir) 不存在\" \"ERROR\"\n        return $false\n    }\n    \n    return $true\n}\n\n# 检查依赖工具\nfunction Test-Dependencies {\n    $tools = @(\"ssh\", \"rsync\")\n    $missing = @()\n    \n    foreach ($tool in $tools) {\n        try {\n            $null = Get-Command $tool -ErrorAction Stop\n            Write-Log \"$tool 已安装\"\n        }\n        catch {\n            $missing += $tool\n        }\n    }\n    \n    if ($missing.Count -gt 0) {\n        Write-Log \"错误: 缺少以下工具: $($missing -join ', ')\" \"ERROR\"\n        Write-Log \"请安装WSL2或cwRsync\" \"ERROR\"\n        return $false\n    }\n    \n    return $true\n}\n\n# 测试远程连接\nfunction Test-RemoteConnection {\n    param($Config)\n    \n    Write-Log \"测试远程连接...\"\n    $testCmd = \"ssh -o ConnectTimeout=10 $($Config.remoteUser)@$($Config.remoteHost) `\"test -d '$($Config.remoteDir)'`\"\"\n    \n    try {\n        $result = Invoke-Expression $testCmd\n        if ($LASTEXITCODE -eq 0) {\n            Write-Log \"远程连接测试成功\"\n            return $true\n        }\n        else {\n            Write-Log \"错误: 无法连接到远程主机或目录不存在\" \"ERROR\"\n            return $false\n        }\n    }\n    catch {\n        Write-Log \"错误: 连接测试失败 - $($_.Exception.Message)\" \"ERROR\"\n        return $false\n    }\n}\n\n# 执行同步\nfunction Sync-Files {\n    param($Config)\n    \n    $syncStart = Get-Date\n    Write-Log \"开始同步: $($Config.localDir) -> $($Config.remoteUser)@$($Config.remoteHost):$($Config.remoteDir)\"\n    \n    # 构建rsync命令\n    $rsyncCmd = \"rsync -avz --delete --compress-level=6\"\n    \n    # 添加排除规则\n    $excludeFile = Join-Path $ScriptDir \"exclude.txt\"\n    if (Test-Path $excludeFile) {\n        $rsyncCmd += \" --exclude-from=`\"$excludeFile`\"\"\n    }\n    \n    # 添加进度显示\n    if ($Config.showProgress) {\n        $rsyncCmd += \" --progress\"\n    }\n    \n    # 转换Windows路径为WSL路径（如果使用WSL）\n    $localPath = $Config.localDir\n    if ($localPath -match \"^[A-Z]:\") {\n        $drive = $localPath.Substring(0,1).ToLower()\n        $path = $localPath.Substring(2).Replace('\\', '/')\n        $localPath = \"/mnt/$drive$path\"\n    }\n    \n    # 完整命令\n    $fullCmd = \"$rsyncCmd `\"$localPath/`\" `\"$($Config.remoteUser)@$($Config.remoteHost):$($Config.remoteDir)/`\"\"\n    \n    try {\n        if ($Verbose) {\n            Write-Log \"执行命令: $fullCmd\"\n        }\n        \n        Invoke-Expression $fullCmd\n        \n        if ($LASTEXITCODE -eq 0) {\n            $syncEnd = Get-Date\n            $duration = ($syncEnd - $syncStart).TotalSeconds\n            Write-Log \"同步完成，耗时: $([math]::Round($duration, 2))秒\"\n            return $true\n        }\n        else {\n            Write-Log \"同步失败，退出码: $LASTEXITCODE\" \"ERROR\"\n            return $false\n        }\n    }\n    catch {\n        Write-Log \"同步异常: $($_.Exception.Message)\" \"ERROR\"\n        return $false\n    }\n}\n\n# 处理文件变化\nfunction Handle-FileChange {\n    param($Config, $ChangedPath)\n    \n    Write-Log \"检测到文件变化: $ChangedPath\"\n    \n    # 防抖动\n    Start-Sleep -Seconds ($Config.syncDelay -or 2)\n    \n    # 重试机制\n    $maxRetries = $Config.maxRetries -or 3\n    $retryCount = 0\n    \n    while ($retryCount -lt $maxRetries) {\n        if (Sync-Files -Config $Config) {\n            break\n        }\n        else {\n            $retryCount++\n            Write-Log \"同步失败，重试 $retryCount/$maxRetries\" \"WARN\"\n            Start-Sleep -Seconds 5\n        }\n    }\n    \n    if ($retryCount -eq $maxRetries) {\n        Write-Log \"同步失败，已达到最大重试次数\" \"ERROR\"\n    }\n}\n\n# 启动文件监控\nfunction Start-FileWatcher {\n    param($Config)\n    \n    Write-Log \"启动文件监控: $($Config.localDir)\"\n    \n    # 创建FileSystemWatcher\n    $watcher = New-Object System.IO.FileSystemWatcher\n    $watcher.Path = $Config.localDir\n    $watcher.IncludeSubdirectories = $true\n    $watcher.EnableRaisingEvents = $true\n    \n    # 注册事件处理器\n    $action = {\n        $path = $Event.SourceEventArgs.FullPath\n        $changeType = $Event.SourceEventArgs.ChangeType\n        \n        # 过滤临时文件和系统文件\n        $excludePatterns = @(\"*.tmp\", \"*.temp\", \"~*\", \".DS_Store\", \"Thumbs.db\")\n        $shouldExclude = $false\n        \n        foreach ($pattern in $excludePatterns) {\n            if ($path -like $pattern) {\n                $shouldExclude = $true\n                break\n            }\n        }\n        \n        if (!$shouldExclude) {\n            $Global:PendingChanges = $true\n        }\n    }\n    \n    # 注册所有相关事件\n    $events = @(\"Created\", \"Changed\", \"Deleted\", \"Renamed\")\n    foreach ($event in $events) {\n        Register-ObjectEvent -InputObject $watcher -EventName $event -Action $action | Out-Null\n    }\n    \n    Write-Log \"文件监控已启动\"\n    \n    # 监控循环\n    $Global:PendingChanges = $false\n    $lastSyncTime = Get-Date\n    \n    try {\n        while ($true) {\n            Start-Sleep -Seconds 1\n            \n            if ($Global:PendingChanges) {\n                $timeSinceLastSync = (Get-Date) - $lastSyncTime\n                if ($timeSinceLastSync.TotalSeconds -ge ($Config.syncDelay -or 2)) {\n                    Handle-FileChange -Config $Config -ChangedPath \"Multiple files\"\n                    $Global:PendingChanges = $false\n                    $lastSyncTime = Get-Date\n                }\n            }\n        }\n    }\n    finally {\n        # 清理\n        $watcher.EnableRaisingEvents = $false\n        $watcher.Dispose()\n        Get-EventSubscriber | Unregister-Event\n        Write-Log \"文件监控已停止\"\n    }\n}\n\n# 主函数\nfunction Main {\n    if ($Help) {\n        Show-Help\n        return\n    }\n    \n    Write-Log \"启动Windows端实时同步服务\"\n    \n    # 加载配置\n    $config = Load-Config -ConfigPath $ConfigFile\n    if (!$config) {\n        return\n    }\n    \n    # 验证配置\n    if (!(Test-Config -Config $config)) {\n        return\n    }\n    \n    Write-Log \"监控目录: $($config.localDir)\"\n    Write-Log \"远程目标: $($config.remoteUser)@$($config.remoteHost):$($config.remoteDir)\"\n    \n    # 检查依赖\n    if (!(Test-Dependencies)) {\n        return\n    }\n    \n    # 测试连接\n    if ($Test) {\n        Test-RemoteConnection -Config $config\n        return\n    }\n    \n    if (!(Test-RemoteConnection -Config $config)) {\n        return\n    }\n    \n    # 初始同步\n    if ($InitialSync) {\n        Sync-Files -Config $config\n        return\n    }\n    \n    Write-Log \"执行初始同步...\"\n    if (!(Sync-Files -Config $config)) {\n        Write-Log \"初始同步失败，退出\" \"ERROR\"\n        return\n    }\n    \n    # 启动监控\n    try {\n        Start-FileWatcher -Config $config\n    }\n    catch {\n        Write-Log \"监控异常: $($_.Exception.Message)\" \"ERROR\"\n    }\n}\n\n# 运行主函数\nMain\n", "modifiedCode": "# Windows端实时同步脚本\n# 使用FileSystemWatcher监控文件变化，通过rsync实现实时同步\n\nparam(\n    [string]$ConfigFile = \"sync_config.json\",\n    [switch]$Test,\n    [switch]$InitialSync,\n    [switch]$Help,\n    [switch]$Verbose\n)\n\n# 全局变量\n$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path\n$LogDir = Join-Path $ScriptDir \"logs\"\n$LogFile = Join-Path $LogDir \"sync_$(Get-Date -Format 'yyyyMMdd').log\"\n\n# 创建日志目录\nif (!(Test-Path $LogDir)) {\n    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null\n}\n\n# 日志函数\nfunction Write-Log {\n    param([string]$Message, [string]$Level = \"INFO\")\n    $timestamp = Get-Date -Format \"yyyy-MM-dd HH:mm:ss\"\n    $logMessage = \"[$timestamp] [$Level] $Message\"\n    Write-Host $logMessage\n    Add-Content -Path $LogFile -Value $logMessage\n}\n\n# 显示帮助信息\nfunction Show-Help {\n    @\"\nWindows端实时文件同步脚本\n\n用法: .\\windows_realtime_sync.ps1 [参数]\n\n参数:\n    -ConfigFile <path>  指定配置文件路径 (默认: sync_config.json)\n    -Test              仅测试连接，不启动同步\n    -InitialSync       仅执行初始同步\n    -Help              显示此帮助信息\n    -Verbose           详细输出模式\n\n配置文件示例 (sync_config.json):\n{\n    \"localDir\": \"C:\\\\Users\\\\<USER>\\\\Documents\\\\project\",\n    \"remoteHost\": \"*************\",\n    \"remoteUser\": \"macuser\",\n    \"remoteDir\": \"/Users/<USER>/Documents/project\",\n    \"syncDelay\": 2,\n    \"maxRetries\": 3,\n    \"showProgress\": false,\n    \"excludePatterns\": [\n        \"*.tmp\",\n        \"Thumbs.db\",\n        \".DS_Store\",\n        \"node_modules\\\\\",\n        \".git\\\\\"\n    ]\n}\n\n前置要求:\n1. 安装WSL2或cwRsync\n2. 配置SSH密钥认证\n3. 确保网络连通性\n\n\"@\n}\n\n# 加载配置文件\nfunction Load-Config {\n    param([string]$ConfigPath)\n    \n    $fullPath = Join-Path $ScriptDir $ConfigPath\n    if (!(Test-Path $fullPath)) {\n        Write-Log \"错误: 配置文件 $fullPath 不存在\" \"ERROR\"\n        return $null\n    }\n    \n    try {\n        $config = Get-Content $fullPath | ConvertFrom-Json\n        Write-Log \"配置文件加载成功\"\n        return $config\n    }\n    catch {\n        Write-Log \"错误: 配置文件格式无效 - $($_.Exception.Message)\" \"ERROR\"\n        return $null\n    }\n}\n\n# 验证配置\nfunction Test-Config {\n    param($Config)\n    \n    $required = @(\"localDir\", \"remoteHost\", \"remoteUser\", \"remoteDir\")\n    foreach ($field in $required) {\n        if (!$Config.$field) {\n            Write-Log \"错误: 配置项 $field 缺失\" \"ERROR\"\n            return $false\n        }\n    }\n    \n    if (!(Test-Path $Config.localDir)) {\n        Write-Log \"错误: 本地目录 $($Config.localDir) 不存在\" \"ERROR\"\n        return $false\n    }\n    \n    return $true\n}\n\n# 检查依赖工具\nfunction Test-Dependencies {\n    $tools = @(\"ssh\", \"rsync\")\n    $missing = @()\n    \n    foreach ($tool in $tools) {\n        try {\n            $null = Get-Command $tool -ErrorAction Stop\n            Write-Log \"$tool 已安装\"\n        }\n        catch {\n            $missing += $tool\n        }\n    }\n    \n    if ($missing.Count -gt 0) {\n        Write-Log \"错误: 缺少以下工具: $($missing -join ', ')\" \"ERROR\"\n        Write-Log \"请安装WSL2或cwRsync\" \"ERROR\"\n        return $false\n    }\n    \n    return $true\n}\n\n# 测试远程连接\nfunction Test-RemoteConnection {\n    param($Config)\n    \n    Write-Log \"测试远程连接...\"\n    $testCmd = \"ssh -o ConnectTimeout=10 $($Config.remoteUser)@$($Config.remoteHost) `\"test -d '$($Config.remoteDir)'`\"\"\n    \n    try {\n        $result = Invoke-Expression $testCmd\n        if ($LASTEXITCODE -eq 0) {\n            Write-Log \"远程连接测试成功\"\n            return $true\n        }\n        else {\n            Write-Log \"错误: 无法连接到远程主机或目录不存在\" \"ERROR\"\n            return $false\n        }\n    }\n    catch {\n        Write-Log \"错误: 连接测试失败 - $($_.Exception.Message)\" \"ERROR\"\n        return $false\n    }\n}\n\n# 执行同步\nfunction Sync-Files {\n    param($Config)\n    \n    $syncStart = Get-Date\n    Write-Log \"开始同步: $($Config.localDir) -> $($Config.remoteUser)@$($Config.remoteHost):$($Config.remoteDir)\"\n    \n    # 构建rsync命令\n    $rsyncCmd = \"rsync -avz --delete --compress-level=6\"\n    \n    # 添加排除规则\n    $excludeFile = Join-Path $ScriptDir \"exclude.txt\"\n    if (Test-Path $excludeFile) {\n        $rsyncCmd += \" --exclude-from=`\"$excludeFile`\"\"\n    }\n    \n    # 添加进度显示\n    if ($Config.showProgress) {\n        $rsyncCmd += \" --progress\"\n    }\n    \n    # 转换Windows路径为WSL路径（如果使用WSL）\n    $localPath = $Config.localDir\n    if ($localPath -match \"^[A-Z]:\") {\n        $drive = $localPath.Substring(0,1).ToLower()\n        $path = $localPath.Substring(2).Replace('\\', '/')\n        $localPath = \"/mnt/$drive$path\"\n    }\n    \n    # 完整命令\n    $fullCmd = \"$rsyncCmd `\"$localPath/`\" `\"$($Config.remoteUser)@$($Config.remoteHost):$($Config.remoteDir)/`\"\"\n    \n    try {\n        if ($Verbose) {\n            Write-Log \"执行命令: $fullCmd\"\n        }\n        \n        Invoke-Expression $fullCmd\n        \n        if ($LASTEXITCODE -eq 0) {\n            $syncEnd = Get-Date\n            $duration = ($syncEnd - $syncStart).TotalSeconds\n            Write-Log \"同步完成，耗时: $([math]::Round($duration, 2))秒\"\n            return $true\n        }\n        else {\n            Write-Log \"同步失败，退出码: $LASTEXITCODE\" \"ERROR\"\n            return $false\n        }\n    }\n    catch {\n        Write-Log \"同步异常: $($_.Exception.Message)\" \"ERROR\"\n        return $false\n    }\n}\n\n# 处理文件变化\nfunction Handle-FileChange {\n    param($Config, $ChangedPath)\n    \n    Write-Log \"检测到文件变化: $ChangedPath\"\n    \n    # 防抖动\n    Start-Sleep -Seconds ($Config.syncDelay -or 2)\n    \n    # 重试机制\n    $maxRetries = $Config.maxRetries -or 3\n    $retryCount = 0\n    \n    while ($retryCount -lt $maxRetries) {\n        if (Sync-Files -Config $Config) {\n            break\n        }\n        else {\n            $retryCount++\n            Write-Log \"同步失败，重试 $retryCount/$maxRetries\" \"WARN\"\n            Start-Sleep -Seconds 5\n        }\n    }\n    \n    if ($retryCount -eq $maxRetries) {\n        Write-Log \"同步失败，已达到最大重试次数\" \"ERROR\"\n    }\n}\n\n# 启动文件监控\nfunction Start-FileWatcher {\n    param($Config)\n    \n    Write-Log \"启动文件监控: $($Config.localDir)\"\n    \n    # 创建FileSystemWatcher\n    $watcher = New-Object System.IO.FileSystemWatcher\n    $watcher.Path = $Config.localDir\n    $watcher.IncludeSubdirectories = $true\n    $watcher.EnableRaisingEvents = $true\n    \n    # 注册事件处理器\n    $action = {\n        $path = $Event.SourceEventArgs.FullPath\n        $changeType = $Event.SourceEventArgs.ChangeType\n        \n        # 过滤临时文件和系统文件\n        $excludePatterns = @(\"*.tmp\", \"*.temp\", \"~*\", \".DS_Store\", \"Thumbs.db\")\n        $shouldExclude = $false\n        \n        foreach ($pattern in $excludePatterns) {\n            if ($path -like $pattern) {\n                $shouldExclude = $true\n                break\n            }\n        }\n        \n        if (!$shouldExclude) {\n            $Global:PendingChanges = $true\n        }\n    }\n    \n    # 注册所有相关事件\n    $events = @(\"Created\", \"Changed\", \"Deleted\", \"Renamed\")\n    foreach ($event in $events) {\n        Register-ObjectEvent -InputObject $watcher -EventName $event -Action $action | Out-Null\n    }\n    \n    Write-Log \"文件监控已启动\"\n    \n    # 监控循环\n    $Global:PendingChanges = $false\n    $lastSyncTime = Get-Date\n    \n    try {\n        while ($true) {\n            Start-Sleep -Seconds 1\n            \n            if ($Global:PendingChanges) {\n                $timeSinceLastSync = (Get-Date) - $lastSyncTime\n                if ($timeSinceLastSync.TotalSeconds -ge ($Config.syncDelay -or 2)) {\n                    Handle-FileChange -Config $Config -ChangedPath \"Multiple files\"\n                    $Global:PendingChanges = $false\n                    $lastSyncTime = Get-Date\n                }\n            }\n        }\n    }\n    finally {\n        # 清理\n        $watcher.EnableRaisingEvents = $false\n        $watcher.Dispose()\n        Get-EventSubscriber | Unregister-Event\n        Write-Log \"文件监控已停止\"\n    }\n}\n\n# 主函数\nfunction Main {\n    if ($Help) {\n        Show-Help\n        return\n    }\n    \n    Write-Log \"启动Windows端实时同步服务\"\n    \n    # 加载配置\n    $config = Load-Config -ConfigPath $ConfigFile\n    if (!$config) {\n        return\n    }\n    \n    # 验证配置\n    if (!(Test-Config -Config $config)) {\n        return\n    }\n    \n    Write-Log \"监控目录: $($config.localDir)\"\n    Write-Log \"远程目标: $($config.remoteUser)@$($config.remoteHost):$($config.remoteDir)\"\n    \n    # 检查依赖\n    if (!(Test-Dependencies)) {\n        return\n    }\n    \n    # 测试连接\n    if ($Test) {\n        Test-RemoteConnection -Config $config\n        return\n    }\n    \n    if (!(Test-RemoteConnection -Config $config)) {\n        return\n    }\n    \n    # 初始同步\n    if ($InitialSync) {\n        Sync-Files -Config $config\n        return\n    }\n    \n    Write-Log \"执行初始同步...\"\n    if (!(Sync-Files -Config $config)) {\n        Write-Log \"初始同步失败，退出\" \"ERROR\"\n        return\n    }\n    \n    # 启动监控\n    try {\n        Start-FileWatcher -Config $config\n    }\n    catch {\n        Write-Log \"监控异常: $($_.Exception.Message)\" \"ERROR\"\n    }\n}\n\n# 运行主函数\nMain\n"}