# User Preferences
- User prefers remote AI editor development workflow with local synchronization for running and testing Unity projects.
- User is interested in Unity development using cloud servers and cloud development with local sync workflow for running/testing.
- User prefers real-time/immediate synchronization between editor changes and local environment for development workflows.
- User is interested in using Cursor editor on headless Linux servers for remote development workflow.