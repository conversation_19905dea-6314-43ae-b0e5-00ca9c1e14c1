# Mac与Windows实时文件同步方案

## 方案概述
设计一个双向实时文件同步系统，确保Mac（A端）和Windows（B端）之间的文件保持一致，任一端修改文件后立即同步到另一端。

## 推荐方案

### 方案一：基于云存储的实时同步（推荐）

#### 1. Google Drive File Stream / OneDrive
**优点：**
- 官方支持，稳定可靠
- 真正的实时同步（几秒内）
- 自动冲突解决
- 版本历史功能
- 跨平台支持完善

**实施步骤：**
1. 在两台设备上安装Google Drive桌面版或OneDrive
2. 将需要同步的文件夹移动到云盘目录
3. 启用"实时同步"模式
4. 配置同步规则和排除文件

#### 2. Dropbox
**优点：**
- 同步速度最快（通常1-3秒）
- 智能同步技术
- 优秀的冲突处理
- 支持局域网同步加速

### 方案二：专业同步工具

#### 1. Syncthing（开源免费）
**优点：**
- 完全去中心化，无需云服务
- 端到端加密
- 支持局域网直连
- 开源免费

**配置步骤：**
```bash
# Mac安装
brew install syncthing

# Windows下载安装包
# https://syncthing.net/downloads/
```

#### 2. Resilio Sync（商业版）
**优点：**
- P2P技术，速度极快
- 支持局域网和互联网同步
- 强大的权限控制

### 方案三：自建同步服务

#### 使用rsync + inotify实现实时同步

**Mac端脚本：**
```bash
#!/bin/bash
# mac_sync.sh

WATCH_DIR="/path/to/sync/folder"
REMOTE_HOST="windows_ip"
REMOTE_USER="username"
REMOTE_DIR="/path/to/windows/folder"

# 安装fswatch
brew install fswatch

# 监控文件变化并同步
fswatch -o "$WATCH_DIR" | while read f; do
    rsync -avz --delete "$WATCH_DIR/" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/"
    echo "$(date): 同步完成"
done
```

**Windows端脚本（PowerShell）：**
```powershell
# windows_sync.ps1

$watchDir = "C:\path\to\sync\folder"
$remoteHost = "mac_ip"
$remoteUser = "username"
$remoteDir = "/path/to/mac/folder"

# 使用FileSystemWatcher监控变化
$watcher = New-Object System.IO.FileSystemWatcher
$watcher.Path = $watchDir
$watcher.IncludeSubdirectories = $true
$watcher.EnableRaisingEvents = $true

# 注册事件处理
Register-ObjectEvent -InputObject $watcher -EventName "Changed" -Action {
    Start-Sleep -Seconds 1  # 防止重复触发
    & rsync -avz --delete "$watchDir/" "$remoteUser@$remoteHost:$remoteDir/"
    Write-Host "$(Get-Date): 同步完成"
}
```

## 详细实施方案

### 第一阶段：环境准备

#### Mac端配置
1. 安装必要工具
```bash
# 安装Homebrew（如果没有）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装同步工具
brew install rsync fswatch
```

2. 配置SSH密钥认证
```bash
# 生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 复制公钥到Windows
ssh-copy-id username@windows_ip
```

#### Windows端配置
1. 安装WSL2或使用Windows版rsync
```powershell
# 启用WSL2
wsl --install

# 或安装cwRsync
# 下载：https://www.itefix.net/cwrsync
```

2. 配置SSH服务
```bash
# 在WSL中安装openssh-server
sudo apt update
sudo apt install openssh-server

# 启动SSH服务
sudo service ssh start
```

### 第二阶段：同步脚本部署

#### 创建监控脚本
```bash
# 创建同步目录
mkdir -p ~/sync_project
cd ~/sync_project
```

#### 配置文件示例
```yaml
# sync_config.yaml
sync:
  local_path: "/Users/<USER>/Documents/project"
  remote_host: "*************"
  remote_user: "windowsuser"
  remote_path: "/mnt/c/Users/<USER>/Documents/project"
  exclude_patterns:
    - "*.tmp"
    - ".DS_Store"
    - "Thumbs.db"
    - "node_modules/"
    - ".git/"
  sync_delay: 2  # 秒
  max_retries: 3
```

### 第三阶段：高级功能

#### 冲突检测与解决
```python
# conflict_resolver.py
import os
import hashlib
from datetime import datetime

def detect_conflict(local_file, remote_file):
    """检测文件冲突"""
    local_hash = get_file_hash(local_file)
    remote_hash = get_file_hash(remote_file)
    
    if local_hash != remote_hash:
        return True
    return False

def resolve_conflict(local_file, remote_file, strategy="timestamp"):
    """解决冲突"""
    if strategy == "timestamp":
        local_time = os.path.getmtime(local_file)
        remote_time = os.path.getmtime(remote_file)
        
        if local_time > remote_time:
            return "use_local"
        else:
            return "use_remote"
    
    elif strategy == "backup_both":
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{local_file}.conflict_{timestamp}"
        os.rename(local_file, backup_name)
        return "backup_created"
```

#### 性能优化
```bash
# 使用增量同步
rsync -avz --delete --checksum --compress-level=9 \
      --exclude-from=exclude.txt \
      "$SOURCE/" "$DESTINATION/"

# 启用并行传输
rsync -avz --delete --parallel=4 "$SOURCE/" "$DESTINATION/"
```

## 监控与日志

### 日志记录
```bash
# 创建日志目录
mkdir -p ~/sync_logs

# 日志轮转配置
cat > ~/sync_logs/logrotate.conf << EOF
~/sync_logs/sync.log {
    daily
    rotate 7
    compress
    missingok
    notifempty
}
EOF
```

### 状态监控
```python
# sync_monitor.py
import psutil
import time
import json

def monitor_sync_status():
    """监控同步状态"""
    status = {
        "timestamp": time.time(),
        "cpu_usage": psutil.cpu_percent(),
        "memory_usage": psutil.virtual_memory().percent,
        "disk_io": psutil.disk_io_counters(),
        "network_io": psutil.net_io_counters()
    }
    
    with open("sync_status.json", "w") as f:
        json.dump(status, f, indent=2)
```

## 故障排除

### 常见问题
1. **网络连接问题**
   - 检查防火墙设置
   - 验证SSH连接
   - 测试网络延迟

2. **权限问题**
   - 确保用户有读写权限
   - 检查SSH密钥配置
   - 验证目录权限

3. **同步延迟**
   - 调整监控间隔
   - 优化网络配置
   - 使用压缩传输

### 性能调优
```bash
# 网络优化
echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf

# SSH优化
echo 'Compression yes' >> ~/.ssh/config
echo 'CompressionLevel 6' >> ~/.ssh/config
```

## 安全考虑

1. **加密传输**：使用SSH或TLS加密
2. **访问控制**：限制IP访问范围
3. **备份策略**：定期创建备份点
4. **监控告警**：异常情况及时通知

## 总结

推荐使用**Google Drive File Stream**或**Syncthing**作为主要方案：
- Google Drive：适合对稳定性要求高的场景
- Syncthing：适合对隐私和控制要求高的场景
- 自建方案：适合有特殊需求的技术团队

选择方案时需要考虑：网络环境、文件大小、同步频率、安全要求等因素。
