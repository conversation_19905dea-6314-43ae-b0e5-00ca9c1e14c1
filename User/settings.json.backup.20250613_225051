{"editor.accessibilitySupport": "off", "git.confirmSync": false, "omnisharp.dotnetPath": "/usr/local/share/dotnet/dotnet", "omnisharp.useModernNet": false, "omnisharp.waitForDebugger": true, "redhat.telemetry.enabled": true, "security.workspace.trust.untrustedFiles": "open", "terminal.integrated.env.osx": {"FIG_NEW_SESSION": "1"}, "liveServer.settings.AdvanceCustomBrowserCmdLine": "", "liveServer.settings.ChromeDebuggingAttachment": false, "liveServer.settings.port": 5503, "liveServer.settings.donotShowInfoMsg": true, "tabnine.experimentalAutoImports": true, "java.editor.reloadChangedSources": "auto", "java.imports.gradle.wrapper.checksums": [{"sha256": "94c175588616ecca500da69953fef41a8ea512c0e955258a6910cf3267deda7f", "allowed": false}, {"sha256": "e2b82129ab64751fd40437007bd2f7f2afb3c6e41a9198e628650b22d5824a14", "allowed": false}], "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "volar.format.initialIndent": {"html": true}, "vue.autoInsert.dotValue": true, "workbench.editorAssociations": {"*.vsix": "default"}, "gitlens.views.commitDetails.files.layout": "list", "livePreview.portNumber": 3099, "cSpell.userWords": ["<PERSON><PERSON><PERSON>", "pinia", "unref", "Vetur"], "window.zoomLevel": 1, "liveServer.settings.ignoreFiles": [".vscode/**", "**/*.scss", "**/*.sass", "**/*.ts"], "workbench.startupEditor": "none", "files.watcherExclude": {"/sys/*/**": true}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "vscode-office.editorTheme": "<PERSON><PERSON><PERSON>", "vscode-office.openOutline": true, "java.completion.favoriteStaticMembers": ["org.junit.Assert.*", "org.junit.Assume.*", "org.junit.jupiter.api.Assertions.*", "org.junit.jupiter.api.Assumptions.*", "org.junit.jupiter.api.DynamicContainer.*", "org.junit.jupiter.api.DynamicTest.*", "org.mockito.Mockito.*", "org.mockito.ArgumentMatchers.*", "org.mockito.Answers.*"], "[java]": {"editor.suggest.snippetsPreventQuickSuggestions": false}, "git.openRepositoryInParentFolders": "always", "editor.tabCompletion": "on", "http.proxyAuthorization": null, "editor.wordWrap": "on", "files.autoSave": "onFocusChange", "editor.largeFileOptimizations": false, "geminicodeassist.project": "quickstart-1559631106265", "cline.preferredLanguage": "Simplified Chinese - 简体中文", "cline.vsCodeLmModelSelector": {}, "chat.agent.enabled": true, "github.copilot.chat.languageContext.fix.typescript.enabled": true, "github.copilot.chat.languageContext.inline.typescript.enabled": true, "github.copilot.chat.languageContext.typescript.enabled": true, "database-client.autoSync": true, "roo-cline.allowedCommands": ["npm test", "npm install", "tsc", "git log", "git diff", "git show", "git clone", "pnpm"], "github.copilot.nextEditSuggestions.enabled": true, "workbench.editor.empty.hint": "hidden", "augment.chat.userGuidelines": "用中文回答", "github.copilot.enable": {"*": false}, "files.exclude": {"**/*.asset": true, "**/*.meta": true, "**/*.prefab": true, "**/*.unity": true}, "workbench.colorTheme": "__vs-dark", "cursor.composer.collapsePaneInputBoxPills": true, "cursor.composer.shouldChimeAfterChatFinishes": true}