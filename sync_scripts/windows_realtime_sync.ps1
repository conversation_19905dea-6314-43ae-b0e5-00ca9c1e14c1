# Windows端实时同步脚本
# 使用FileSystemWatcher监控文件变化，通过rsync实现实时同步

param(
    [string]$ConfigFile = "sync_config.json",
    [switch]$Test,
    [switch]$InitialSync,
    [switch]$Help,
    [switch]$Verbose
)

# 全局变量
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$LogDir = Join-Path $ScriptDir "logs"
$LogFile = Join-Path $LogDir "sync_$(Get-Date -Format 'yyyyMMdd').log"

# 创建日志目录
if (!(Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
}

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage
}

# 显示帮助信息
function Show-Help {
    @"
Windows端实时文件同步脚本

用法: .\windows_realtime_sync.ps1 [参数]

参数:
    -ConfigFile <path>  指定配置文件路径 (默认: sync_config.json)
    -Test              仅测试连接，不启动同步
    -InitialSync       仅执行初始同步
    -Help              显示此帮助信息
    -Verbose           详细输出模式

配置文件示例 (sync_config.json):
{
    "localDir": "C:\\Users\\<USER>\\Documents\\project",
    "remoteHost": "*************",
    "remoteUser": "macuser",
    "remoteDir": "/Users/<USER>/Documents/project",
    "syncDelay": 2,
    "maxRetries": 3,
    "showProgress": false,
    "excludePatterns": [
        "*.tmp",
        "Thumbs.db",
        ".DS_Store",
        "node_modules\\",
        ".git\\"
    ]
}

前置要求:
1. 安装WSL2或cwRsync
2. 配置SSH密钥认证
3. 确保网络连通性

"@
}

# 加载配置文件
function Load-Config {
    param([string]$ConfigPath)
    
    $fullPath = Join-Path $ScriptDir $ConfigPath
    if (!(Test-Path $fullPath)) {
        Write-Log "错误: 配置文件 $fullPath 不存在" "ERROR"
        return $null
    }
    
    try {
        $config = Get-Content $fullPath | ConvertFrom-Json
        Write-Log "配置文件加载成功"
        return $config
    }
    catch {
        Write-Log "错误: 配置文件格式无效 - $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# 验证配置
function Test-Config {
    param($Config)
    
    $required = @("localDir", "remoteHost", "remoteUser", "remoteDir")
    foreach ($field in $required) {
        if (!$Config.$field) {
            Write-Log "错误: 配置项 $field 缺失" "ERROR"
            return $false
        }
    }
    
    if (!(Test-Path $Config.localDir)) {
        Write-Log "错误: 本地目录 $($Config.localDir) 不存在" "ERROR"
        return $false
    }
    
    return $true
}

# 检查依赖工具
function Test-Dependencies {
    $tools = @("ssh", "rsync")
    $missing = @()
    
    foreach ($tool in $tools) {
        try {
            $null = Get-Command $tool -ErrorAction Stop
            Write-Log "$tool 已安装"
        }
        catch {
            $missing += $tool
        }
    }
    
    if ($missing.Count -gt 0) {
        Write-Log "错误: 缺少以下工具: $($missing -join ', ')" "ERROR"
        Write-Log "请安装WSL2或cwRsync" "ERROR"
        return $false
    }
    
    return $true
}

# 测试远程连接
function Test-RemoteConnection {
    param($Config)
    
    Write-Log "测试远程连接..."
    $testCmd = "ssh -o ConnectTimeout=10 $($Config.remoteUser)@$($Config.remoteHost) `"test -d '$($Config.remoteDir)'`""
    
    try {
        $result = Invoke-Expression $testCmd
        if ($LASTEXITCODE -eq 0) {
            Write-Log "远程连接测试成功"
            return $true
        }
        else {
            Write-Log "错误: 无法连接到远程主机或目录不存在" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "错误: 连接测试失败 - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 执行同步
function Sync-Files {
    param($Config)
    
    $syncStart = Get-Date
    Write-Log "开始同步: $($Config.localDir) -> $($Config.remoteUser)@$($Config.remoteHost):$($Config.remoteDir)"
    
    # 构建rsync命令
    $rsyncCmd = "rsync -avz --delete --compress-level=6"
    
    # 添加排除规则
    $excludeFile = Join-Path $ScriptDir "exclude.txt"
    if (Test-Path $excludeFile) {
        $rsyncCmd += " --exclude-from=`"$excludeFile`""
    }
    
    # 添加进度显示
    if ($Config.showProgress) {
        $rsyncCmd += " --progress"
    }
    
    # 转换Windows路径为WSL路径（如果使用WSL）
    $localPath = $Config.localDir
    if ($localPath -match "^[A-Z]:") {
        $drive = $localPath.Substring(0,1).ToLower()
        $path = $localPath.Substring(2).Replace('\', '/')
        $localPath = "/mnt/$drive$path"
    }
    
    # 完整命令
    $fullCmd = "$rsyncCmd `"$localPath/`" `"$($Config.remoteUser)@$($Config.remoteHost):$($Config.remoteDir)/`""
    
    try {
        if ($Verbose) {
            Write-Log "执行命令: $fullCmd"
        }
        
        Invoke-Expression $fullCmd
        
        if ($LASTEXITCODE -eq 0) {
            $syncEnd = Get-Date
            $duration = ($syncEnd - $syncStart).TotalSeconds
            Write-Log "同步完成，耗时: $([math]::Round($duration, 2))秒"
            return $true
        }
        else {
            Write-Log "同步失败，退出码: $LASTEXITCODE" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "同步异常: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 处理文件变化
function Handle-FileChange {
    param($Config, $ChangedPath)
    
    Write-Log "检测到文件变化: $ChangedPath"
    
    # 防抖动
    Start-Sleep -Seconds ($Config.syncDelay -or 2)
    
    # 重试机制
    $maxRetries = $Config.maxRetries -or 3
    $retryCount = 0
    
    while ($retryCount -lt $maxRetries) {
        if (Sync-Files -Config $Config) {
            break
        }
        else {
            $retryCount++
            Write-Log "同步失败，重试 $retryCount/$maxRetries" "WARN"
            Start-Sleep -Seconds 5
        }
    }
    
    if ($retryCount -eq $maxRetries) {
        Write-Log "同步失败，已达到最大重试次数" "ERROR"
    }
}

# 启动文件监控
function Start-FileWatcher {
    param($Config)
    
    Write-Log "启动文件监控: $($Config.localDir)"
    
    # 创建FileSystemWatcher
    $watcher = New-Object System.IO.FileSystemWatcher
    $watcher.Path = $Config.localDir
    $watcher.IncludeSubdirectories = $true
    $watcher.EnableRaisingEvents = $true
    
    # 注册事件处理器
    $action = {
        $path = $Event.SourceEventArgs.FullPath
        $changeType = $Event.SourceEventArgs.ChangeType
        
        # 过滤临时文件和系统文件
        $excludePatterns = @("*.tmp", "*.temp", "~*", ".DS_Store", "Thumbs.db")
        $shouldExclude = $false
        
        foreach ($pattern in $excludePatterns) {
            if ($path -like $pattern) {
                $shouldExclude = $true
                break
            }
        }
        
        if (!$shouldExclude) {
            $Global:PendingChanges = $true
        }
    }
    
    # 注册所有相关事件
    $events = @("Created", "Changed", "Deleted", "Renamed")
    foreach ($event in $events) {
        Register-ObjectEvent -InputObject $watcher -EventName $event -Action $action | Out-Null
    }
    
    Write-Log "文件监控已启动"
    
    # 监控循环
    $Global:PendingChanges = $false
    $lastSyncTime = Get-Date
    
    try {
        while ($true) {
            Start-Sleep -Seconds 1
            
            if ($Global:PendingChanges) {
                $timeSinceLastSync = (Get-Date) - $lastSyncTime
                if ($timeSinceLastSync.TotalSeconds -ge ($Config.syncDelay -or 2)) {
                    Handle-FileChange -Config $Config -ChangedPath "Multiple files"
                    $Global:PendingChanges = $false
                    $lastSyncTime = Get-Date
                }
            }
        }
    }
    finally {
        # 清理
        $watcher.EnableRaisingEvents = $false
        $watcher.Dispose()
        Get-EventSubscriber | Unregister-Event
        Write-Log "文件监控已停止"
    }
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Log "启动Windows端实时同步服务"
    
    # 加载配置
    $config = Load-Config -ConfigPath $ConfigFile
    if (!$config) {
        return
    }
    
    # 验证配置
    if (!(Test-Config -Config $config)) {
        return
    }
    
    Write-Log "监控目录: $($config.localDir)"
    Write-Log "远程目标: $($config.remoteUser)@$($config.remoteHost):$($config.remoteDir)"
    
    # 检查依赖
    if (!(Test-Dependencies)) {
        return
    }
    
    # 测试连接
    if ($Test) {
        Test-RemoteConnection -Config $config
        return
    }
    
    if (!(Test-RemoteConnection -Config $config)) {
        return
    }
    
    # 初始同步
    if ($InitialSync) {
        Sync-Files -Config $config
        return
    }
    
    Write-Log "执行初始同步..."
    if (!(Sync-Files -Config $config)) {
        Write-Log "初始同步失败，退出" "ERROR"
        return
    }
    
    # 启动监控
    try {
        Start-FileWatcher -Config $config
    }
    catch {
        Write-Log "监控异常: $($_.Exception.Message)" "ERROR"
    }
}

# 运行主函数
Main
