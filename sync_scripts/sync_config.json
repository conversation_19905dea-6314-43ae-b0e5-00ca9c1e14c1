{"localDir": "C:\\Users\\<USER>\\Documents\\project", "remoteHost": "*************", "remoteUser": "mac<PERSON>", "remoteDir": "/Users/<USER>/Documents/project", "syncDelay": 2, "maxRetries": 3, "showProgress": false, "sshPort": 22, "connectionTimeout": 10, "compressLevel": 6, "parallelTransfers": 1, "bandwidthLimit": "", "logLevel": "INFO", "logRetentionDays": 7, "healthCheckInterval": 300, "autoRestart": true, "excludePatterns": ["*.tmp", "*.temp", "~*", ".DS_Store", "Thumbs.db", "desktop.ini", "node_modules\\", ".git\\", ".svn\\", ".vscode\\", "*.log", "*.cache"], "includePatterns": ["*.txt", "*.md", "*.js", "*.ts", "*.py", "*.java", "*.cpp", "*.h", "*.css", "*.html", "*.json", "*.xml", "*.yaml", "*.yml"], "notifications": {"enabled": true, "onSuccess": false, "onError": true, "onConflict": true}, "backup": {"enabled": true, "maxBackups": 5, "backupDir": "C:\\Users\\<USER>\\Documents\\sync_backups"}, "security": {"encryptTransfer": true, "verifyChecksums": true, "allowedHosts": ["*************", "macbook.local"]}}