#!/bin/bash

# 同步速度测试脚本
# 用于测试不同配置下的同步性能

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DIR="$SCRIPT_DIR/speed_test"
RESULTS_FILE="$SCRIPT_DIR/speed_test_results.txt"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# 创建测试环境
setup_test_env() {
    print_header "设置测试环境"
    
    # 创建测试目录
    mkdir -p "$TEST_DIR"
    cd "$TEST_DIR"
    
    # 创建不同大小的测试文件
    echo "创建测试文件..."
    
    # 小文件 (1KB)
    for i in {1..10}; do
        echo "这是测试文件 $i $(date)" > "small_file_$i.txt"
    done
    
    # 中等文件 (100KB)
    for i in {1..5}; do
        head -c 100000 /dev/urandom > "medium_file_$i.bin"
    done
    
    # 大文件 (1MB)
    for i in {1..2}; do
        head -c 1000000 /dev/urandom > "large_file_$i.bin"
    done
    
    print_success "测试文件创建完成"
    ls -lh
}

# 测试网络延迟
test_network_latency() {
    print_header "网络延迟测试"
    
    if [[ -f "$SCRIPT_DIR/sync_config.conf" ]]; then
        source "$SCRIPT_DIR/sync_config.conf"
        
        echo "测试到 $REMOTE_HOST 的网络延迟..."
        
        # ping测试
        ping_result=$(ping -c 5 "$REMOTE_HOST" | tail -1 | awk -F'/' '{print $5}')
        echo "平均延迟: ${ping_result}ms"
        
        # SSH连接测试
        ssh_start=$(date +%s.%N)
        ssh "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH连接测试'" > /dev/null 2>&1
        ssh_end=$(date +%s.%N)
        ssh_latency=$(echo "$ssh_end - $ssh_start" | bc)
        echo "SSH连接延迟: ${ssh_latency}秒"
        
        # 带宽测试（简单）
        echo "测试传输带宽..."
        dd if=/dev/zero bs=1M count=10 2>/dev/null | ssh "$REMOTE_USER@$REMOTE_HOST" "cat > /dev/null"
        
    else
        print_error "配置文件不存在"
    fi
}

# 测试同步速度
test_sync_speed() {
    local config_file="$1"
    local test_name="$2"
    
    print_header "同步速度测试: $test_name"
    
    if [[ ! -f "$config_file" ]]; then
        print_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 备份原配置
    cp "$SCRIPT_DIR/sync_config.conf" "$SCRIPT_DIR/sync_config.conf.backup" 2>/dev/null
    
    # 使用测试配置
    cp "$config_file" "$SCRIPT_DIR/sync_config.conf"
    
    # 记录开始时间
    start_time=$(date +%s.%N)
    
    # 执行同步
    echo "开始同步测试..."
    bash "$SCRIPT_DIR/mac_realtime_sync.sh" --init > /dev/null 2>&1
    
    if [[ $? -eq 0 ]]; then
        # 记录结束时间
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc)
        
        # 计算文件大小
        total_size=$(du -sb "$TEST_DIR" | cut -f1)
        total_size_mb=$(echo "scale=2; $total_size / 1024 / 1024" | bc)
        
        # 计算速度
        speed_mbps=$(echo "scale=2; $total_size_mb / $duration" | bc)
        
        print_success "同步完成"
        echo "总大小: ${total_size_mb}MB"
        echo "耗时: ${duration}秒"
        echo "速度: ${speed_mbps}MB/s"
        
        # 记录结果
        echo "[$test_name] 大小:${total_size_mb}MB 耗时:${duration}s 速度:${speed_mbps}MB/s" >> "$RESULTS_FILE"
        
    else
        print_error "同步失败"
        echo "[$test_name] 同步失败" >> "$RESULTS_FILE"
    fi
    
    # 恢复原配置
    mv "$SCRIPT_DIR/sync_config.conf.backup" "$SCRIPT_DIR/sync_config.conf" 2>/dev/null
}

# 测试文件变化检测速度
test_file_change_detection() {
    print_header "文件变化检测速度测试"
    
    # 创建测试文件
    test_file="$TEST_DIR/change_test.txt"
    echo "初始内容" > "$test_file"
    
    # 启动文件监控
    fswatch -o "$TEST_DIR" &
    fswatch_pid=$!
    
    sleep 1
    
    # 记录开始时间
    start_time=$(date +%s.%N)
    
    # 修改文件
    echo "修改内容 $(date)" >> "$test_file"
    
    # 等待检测到变化
    sleep 0.5
    
    # 停止监控
    kill $fswatch_pid 2>/dev/null
    
    end_time=$(date +%s.%N)
    detection_time=$(echo "$end_time - $start_time" | bc)
    
    echo "文件变化检测时间: ${detection_time}秒"
}

# 比较不同配置的性能
compare_configurations() {
    print_header "配置性能对比"
    
    # 清空结果文件
    > "$RESULTS_FILE"
    
    # 测试标准配置
    if [[ -f "$SCRIPT_DIR/sync_config.conf" ]]; then
        test_sync_speed "$SCRIPT_DIR/sync_config.conf" "标准配置"
    fi
    
    # 测试速度优化配置
    if [[ -f "$SCRIPT_DIR/speed_optimized_config.conf" ]]; then
        test_sync_speed "$SCRIPT_DIR/speed_optimized_config.conf" "速度优化配置"
    fi
    
    # 显示对比结果
    print_header "测试结果对比"
    cat "$RESULTS_FILE"
}

# 生成性能报告
generate_report() {
    print_header "生成性能报告"
    
    local report_file="$SCRIPT_DIR/performance_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# 文件同步性能测试报告

**测试时间:** $(date)
**测试环境:** $(uname -a)

## 网络环境

$(test_network_latency 2>&1)

## 同步性能测试

$(cat "$RESULTS_FILE" 2>/dev/null)

## 文件变化检测

$(test_file_change_detection 2>&1)

## 优化建议

### 局域网环境优化
- 使用 --whole-file 参数
- 禁用压缩 (--no-compress)
- 增加并行传输数量
- 使用快速加密算法

### 互联网环境优化
- 启用压缩 (--compress-level=6)
- 使用增量同步
- 限制带宽使用
- 启用断点续传

### 系统优化
- 调整TCP窗口大小
- 优化SSH配置
- 使用SSD存储
- 增加内存缓冲区

EOF

    print_success "性能报告已生成: $report_file"
}

# 清理测试环境
cleanup() {
    print_header "清理测试环境"
    
    if [[ -d "$TEST_DIR" ]]; then
        rm -rf "$TEST_DIR"
        print_success "测试目录已清理"
    fi
    
    if [[ -f "$RESULTS_FILE" ]]; then
        rm -f "$RESULTS_FILE"
        print_success "测试结果已清理"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
文件同步速度测试工具

用法: $0 [选项]

选项:
    -s, --setup        设置测试环境
    -n, --network      测试网络延迟
    -c, --compare      比较不同配置性能
    -d, --detection    测试文件变化检测速度
    -r, --report       生成性能报告
    -C, --cleanup      清理测试环境
    -h, --help         显示帮助信息

示例:
    $0 --setup         # 设置测试环境
    $0 --compare       # 运行性能对比测试
    $0 --report        # 生成完整报告
    $0 --cleanup       # 清理测试文件

EOF
}

# 主函数
main() {
    case "${1:-}" in
        -s|--setup)
            setup_test_env
            ;;
        -n|--network)
            test_network_latency
            ;;
        -c|--compare)
            setup_test_env
            compare_configurations
            ;;
        -d|--detection)
            setup_test_env
            test_file_change_detection
            ;;
        -r|--report)
            setup_test_env
            compare_configurations
            generate_report
            ;;
        -C|--cleanup)
            cleanup
            ;;
        -h|--help)
            show_help
            ;;
        *)
            echo "运行完整性能测试..."
            setup_test_env
            test_network_latency
            compare_configurations
            test_file_change_detection
            generate_report
            ;;
    esac
}

main "$@"
