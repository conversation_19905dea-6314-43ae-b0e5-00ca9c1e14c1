#!/bin/bash

# Mac端实时同步脚本
# 使用fswatch监控文件变化，通过rsync实现实时同步

# 配置参数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/sync_config.conf"
LOG_FILE="$SCRIPT_DIR/logs/sync_$(date +%Y%m%d).log"

# 创建日志目录
mkdir -p "$SCRIPT_DIR/logs"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 加载配置文件
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
else
    log "错误: 配置文件 $CONFIG_FILE 不存在"
    exit 1
fi

# 验证必要参数
if [[ -z "$LOCAL_DIR" || -z "$REMOTE_HOST" || -z "$REMOTE_USER" || -z "$REMOTE_DIR" ]]; then
    log "错误: 配置参数不完整"
    exit 1
fi

# 检查本地目录
if [[ ! -d "$LOCAL_DIR" ]]; then
    log "错误: 本地目录 $LOCAL_DIR 不存在"
    exit 1
fi

# 检查依赖工具
check_dependencies() {
    local deps=("fswatch" "rsync" "ssh")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log "错误: 缺少依赖工具 $dep"
            echo "请运行: brew install $dep"
            exit 1
        fi
    done
}

# 测试远程连接
test_connection() {
    log "测试远程连接..."
    if ssh -o ConnectTimeout=10 "$REMOTE_USER@$REMOTE_HOST" "test -d '$REMOTE_DIR'"; then
        log "远程连接测试成功"
        return 0
    else
        log "错误: 无法连接到远程主机或目录不存在"
        return 1
    fi
}

# 执行同步
sync_files() {
    local sync_start=$(date +%s)
    log "开始同步: $LOCAL_DIR -> $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"
    
    # 构建rsync命令
    local rsync_cmd="rsync -avz --delete --compress-level=6"
    
    # 添加排除规则
    if [[ -f "$SCRIPT_DIR/exclude.txt" ]]; then
        rsync_cmd="$rsync_cmd --exclude-from=$SCRIPT_DIR/exclude.txt"
    fi
    
    # 添加进度显示（可选）
    if [[ "$SHOW_PROGRESS" == "true" ]]; then
        rsync_cmd="$rsync_cmd --progress"
    fi
    
    # 执行同步
    if $rsync_cmd "$LOCAL_DIR/" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/"; then
        local sync_end=$(date +%s)
        local duration=$((sync_end - sync_start))
        log "同步完成，耗时: ${duration}秒"
        return 0
    else
        log "同步失败"
        return 1
    fi
}

# 初始同步
initial_sync() {
    log "执行初始同步..."
    if sync_files; then
        log "初始同步完成"
    else
        log "初始同步失败，退出"
        exit 1
    fi
}

# 文件变化处理
handle_change() {
    local changed_file="$1"
    log "检测到文件变化: $changed_file"
    
    # 防抖动：等待一段时间确保文件写入完成
    sleep "${SYNC_DELAY:-2}"
    
    # 重试机制
    local max_retries="${MAX_RETRIES:-3}"
    local retry_count=0
    
    while [[ $retry_count -lt $max_retries ]]; do
        if sync_files; then
            break
        else
            retry_count=$((retry_count + 1))
            log "同步失败，重试 $retry_count/$max_retries"
            sleep 5
        fi
    done
    
    if [[ $retry_count -eq $max_retries ]]; then
        log "同步失败，已达到最大重试次数"
    fi
}

# 信号处理
cleanup() {
    log "收到退出信号，正在清理..."
    if [[ -n "$FSWATCH_PID" ]]; then
        kill "$FSWATCH_PID" 2>/dev/null
    fi
    log "同步服务已停止"
    exit 0
}

# 主函数
main() {
    log "启动Mac端实时同步服务"
    log "监控目录: $LOCAL_DIR"
    log "远程目标: $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"
    
    # 检查依赖
    check_dependencies
    
    # 测试连接
    if ! test_connection; then
        exit 1
    fi
    
    # 初始同步
    initial_sync
    
    # 设置信号处理
    trap cleanup SIGINT SIGTERM
    
    # 启动文件监控
    log "启动文件监控..."
    fswatch -o "$LOCAL_DIR" | while read f; do
        handle_change "$f"
    done &
    
    FSWATCH_PID=$!
    log "文件监控已启动 (PID: $FSWATCH_PID)"
    
    # 保持脚本运行
    wait $FSWATCH_PID
}

# 显示帮助信息
show_help() {
    cat << EOF
Mac端实时文件同步脚本

用法: $0 [选项]

选项:
    -h, --help      显示此帮助信息
    -c, --config    指定配置文件路径
    -t, --test      仅测试连接，不启动同步
    -i, --init      仅执行初始同步
    -v, --verbose   详细输出模式

配置文件示例:
    LOCAL_DIR="/Users/<USER>/Documents/project"
    REMOTE_HOST="*************"
    REMOTE_USER="windowsuser"
    REMOTE_DIR="/mnt/c/Users/<USER>/Documents/project"
    SYNC_DELAY=2
    MAX_RETRIES=3
    SHOW_PROGRESS=false

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -t|--test)
            check_dependencies
            test_connection
            exit $?
            ;;
        -i|--init)
            check_dependencies
            test_connection && initial_sync
            exit $?
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主函数
main
