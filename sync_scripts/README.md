# Mac与Windows实时文件同步工具

这是一套完整的Mac与Windows双向实时文件同步解决方案，支持秒级同步，自动冲突处理，断线重连等功能。

## 功能特性

- ✅ **实时同步**: 文件变化后2秒内完成同步
- ✅ **双向支持**: Mac ↔ Windows 双向同步
- ✅ **自动重试**: 网络异常时自动重试
- ✅ **冲突处理**: 智能处理文件冲突
- ✅ **排除规则**: 支持自定义排除文件/目录
- ✅ **日志记录**: 详细的同步日志
- ✅ **断线重连**: 网络恢复后自动重连
- ✅ **系统服务**: 支持开机自启动

## 系统要求

### Mac端
- macOS 10.12 或更高版本
- Homebrew 包管理器
- 网络连接

### Windows端
- Windows 10 或更高版本
- WSL2 或 cwRsync
- PowerShell 5.0 或更高版本
- SSH 服务

## 快速开始

### 1. Mac端安装

```bash
# 克隆或下载脚本文件
cd sync_scripts

# 运行安装脚本
chmod +x setup.sh
./setup.sh
```

安装脚本会自动：
- 安装 Homebrew（如果未安装）
- 安装 fswatch 和 rsync
- 生成 SSH 密钥对
- 创建配置文件
- 设置启动脚本

### 2. Windows端配置

#### 方法一：使用WSL2（推荐）

```powershell
# 安装WSL2
wsl --install

# 在WSL中安装SSH服务
sudo apt update
sudo apt install openssh-server rsync

# 启动SSH服务
sudo service ssh start

# 设置开机自启动
sudo systemctl enable ssh
```

#### 方法二：使用cwRsync

1. 下载并安装 [cwRsync](https://www.itefix.net/cwrsync)
2. 配置环境变量
3. 安装SSH服务

### 3. SSH密钥配置

在Mac端运行安装脚本后，会显示SSH公钥，需要将其添加到Windows端：

```bash
# Windows端（在WSL中执行）
mkdir -p ~/.ssh
echo "你的SSH公钥内容" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
chmod 700 ~/.ssh
```

### 4. 配置同步参数

编辑配置文件：

**Mac端** (`sync_config.conf`):
```bash
LOCAL_DIR="/Users/<USER>/Documents/project"
REMOTE_HOST="*************"
REMOTE_USER="windowsuser"
REMOTE_DIR="/mnt/c/Users/<USER>/Documents/project"
```

**Windows端** (`sync_config.json`):
```json
{
  "localDir": "C:\\Users\\<USER>\\Documents\\project",
  "remoteHost": "*************",
  "remoteUser": "macuser",
  "remoteDir": "/Users/<USER>/Documents/project"
}
```

### 5. 测试连接

```bash
# Mac端测试
./setup.sh --test

# Windows端测试
.\windows_realtime_sync.ps1 -Test
```

### 6. 启动同步

```bash
# Mac端启动
./start_sync.sh

# Windows端启动
.\windows_realtime_sync.ps1
```

## 详细配置

### 排除文件规则

编辑 `exclude.txt` 文件来设置不需要同步的文件：

```
# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp

# 开发文件
node_modules/
.git/
.vscode/
```

### 高级配置选项

#### Mac端配置 (`sync_config.conf`)

```bash
SYNC_DELAY=2          # 同步延迟（秒）
MAX_RETRIES=3         # 最大重试次数
COMPRESS_LEVEL=6      # 压缩级别（1-9）
SHOW_PROGRESS=false   # 显示进度
LOG_LEVEL="INFO"      # 日志级别
```

#### Windows端配置 (`sync_config.json`)

```json
{
  "syncDelay": 2,
  "maxRetries": 3,
  "compressLevel": 6,
  "showProgress": false,
  "logLevel": "INFO",
  "notifications": {
    "enabled": true,
    "onError": true
  },
  "backup": {
    "enabled": true,
    "maxBackups": 5
  }
}
```

## 使用说明

### 启动同步服务

```bash
# Mac端
./mac_realtime_sync.sh

# 或使用启动器
./start_sync.sh

# Windows端
.\windows_realtime_sync.ps1

# 后台运行
.\windows_realtime_sync.ps1 &
```

### 命令行选项

#### Mac端选项

```bash
./mac_realtime_sync.sh [选项]

选项:
  -h, --help      显示帮助信息
  -c, --config    指定配置文件
  -t, --test      仅测试连接
  -i, --init      仅执行初始同步
  -v, --verbose   详细输出
```

#### Windows端选项

```powershell
.\windows_realtime_sync.ps1 [参数]

参数:
  -ConfigFile     指定配置文件
  -Test          仅测试连接
  -InitialSync   仅执行初始同步
  -Help          显示帮助信息
  -Verbose       详细输出
```

### 系统服务配置

#### Mac端（LaunchAgent）

```bash
# 加载服务
launchctl load ~/Library/LaunchAgents/com.filesync.realtime.plist

# 卸载服务
launchctl unload ~/Library/LaunchAgents/com.filesync.realtime.plist

# 查看服务状态
launchctl list | grep filesync
```

#### Windows端（计划任务）

```powershell
# 创建计划任务
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\path\to\windows_realtime_sync.ps1"
$trigger = New-ScheduledTaskTrigger -AtStartup
Register-ScheduledTask -TaskName "FileSync" -Action $action -Trigger $trigger
```

## 监控和日志

### 日志文件位置

- **Mac端**: `sync_scripts/logs/sync_YYYYMMDD.log`
- **Windows端**: `sync_scripts/logs/sync_YYYYMMDD.log`

### 日志级别

- `DEBUG`: 详细调试信息
- `INFO`: 一般信息
- `WARN`: 警告信息
- `ERROR`: 错误信息

### 监控同步状态

```bash
# 实时查看日志
tail -f logs/sync_$(date +%Y%m%d).log

# 查看同步统计
grep "同步完成" logs/sync_$(date +%Y%m%d).log | wc -l
```

## 故障排除

### 常见问题

1. **SSH连接失败**
   ```bash
   # 检查SSH服务状态
   ssh -v username@hostname
   
   # 检查密钥权限
   chmod 600 ~/.ssh/id_rsa
   chmod 644 ~/.ssh/id_rsa.pub
   ```

2. **同步延迟过高**
   ```bash
   # 检查网络延迟
   ping remote_host
   
   # 调整同步参数
   SYNC_DELAY=1
   COMPRESS_LEVEL=3
   ```

3. **文件冲突**
   ```bash
   # 查看冲突文件
   find . -name "*.conflict_*"
   
   # 手动解决冲突
   # 选择保留的版本，删除冲突文件
   ```

### 性能优化

1. **网络优化**
   ```bash
   # 启用SSH压缩
   echo "Compression yes" >> ~/.ssh/config
   
   # 调整TCP窗口大小
   echo "net.core.rmem_max = 16777216" >> /etc/sysctl.conf
   ```

2. **同步优化**
   ```bash
   # 减少同步延迟
   SYNC_DELAY=1
   
   # 启用并行传输
   rsync --parallel=4
   ```

## 安全建议

1. **使用密钥认证**: 禁用密码登录
2. **限制IP访问**: 配置防火墙规则
3. **定期备份**: 启用自动备份功能
4. **监控日志**: 定期检查异常活动

## 许可证

MIT License

## 支持

如有问题，请查看：
1. 日志文件中的错误信息
2. 网络连接状态
3. SSH配置是否正确
4. 文件权限设置

---

**注意**: 首次使用前请仔细阅读配置说明，确保网络和权限设置正确。
