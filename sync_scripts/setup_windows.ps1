# Windows端同步环境安装脚本

param(
    [switch]$Test,
    [switch]$Help
)

$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Green" = "Green"
        "Yellow" = "Yellow" 
        "Red" = "Red"
        "Blue" = "Blue"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Blue"
}

# 显示帮助信息
function Show-Help {
    @"
Windows端文件同步环境安装脚本

用法: .\setup_windows.ps1 [参数]

参数:
    -Test    测试环境和连接
    -Help    显示此帮助信息

功能:
    1. 检查系统要求
    2. 安装WSL2和必要工具
    3. 配置SSH服务
    4. 创建配置文件
    5. 测试连接

前置要求:
    - Windows 10 版本 2004 或更高
    - 管理员权限
    - 网络连接

"@
}

# 检查管理员权限
function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 检查Windows版本
function Test-WindowsVersion {
    $version = [System.Environment]::OSVersion.Version
    if ($version.Major -lt 10) {
        Write-Error "需要Windows 10或更高版本"
        return $false
    }
    
    $build = (Get-ItemProperty "HKLM:SOFTWARE\Microsoft\Windows NT\CurrentVersion").CurrentBuild
    if ([int]$build -lt 19041) {
        Write-Warning "建议升级到Windows 10版本2004或更高以获得最佳WSL2支持"
    }
    
    Write-Success "Windows版本检查通过"
    return $true
}

# 检查WSL状态
function Test-WSLStatus {
    try {
        $wslStatus = wsl --status 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "WSL已安装"
            return $true
        }
    }
    catch {
        Write-Info "WSL未安装"
        return $false
    }
    return $false
}

# 安装WSL2
function Install-WSL {
    Write-Info "正在安装WSL2..."
    
    try {
        # 启用WSL功能
        dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
        dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
        
        Write-Success "WSL功能已启用"
        
        # 安装WSL2
        wsl --install --no-distribution
        
        Write-Success "WSL2安装完成"
        Write-Warning "需要重启系统以完成安装，重启后请重新运行此脚本"
        
        $restart = Read-Host "是否现在重启？(y/N)"
        if ($restart -eq 'y' -or $restart -eq 'Y') {
            Restart-Computer -Force
        }
        
        return $false
    }
    catch {
        Write-Error "WSL安装失败: $($_.Exception.Message)"
        return $false
    }
}

# 安装Ubuntu发行版
function Install-Ubuntu {
    Write-Info "检查Ubuntu发行版..."
    
    $distributions = wsl --list --quiet
    if ($distributions -contains "Ubuntu") {
        Write-Success "Ubuntu已安装"
        return $true
    }
    
    Write-Info "正在安装Ubuntu..."
    try {
        wsl --install -d Ubuntu
        Write-Success "Ubuntu安装完成"
        Write-Info "请按照提示设置Ubuntu用户名和密码"
        return $true
    }
    catch {
        Write-Error "Ubuntu安装失败: $($_.Exception.Message)"
        return $false
    }
}

# 配置WSL环境
function Setup-WSLEnvironment {
    Write-Info "配置WSL环境..."
    
    $setupScript = @"
#!/bin/bash
set -e

echo "更新包列表..."
sudo apt update

echo "安装必要工具..."
sudo apt install -y openssh-server rsync

echo "配置SSH服务..."
sudo sed -i 's/#Port 22/Port 22/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config

echo "启动SSH服务..."
sudo service ssh start

echo "设置SSH服务自启动..."
echo 'sudo service ssh start' >> ~/.bashrc

echo "创建SSH目录..."
mkdir -p ~/.ssh
chmod 700 ~/.ssh

echo "WSL环境配置完成"
"@
    
    $tempScript = [System.IO.Path]::GetTempFileName() + ".sh"
    $setupScript | Out-File -FilePath $tempScript -Encoding UTF8
    
    try {
        wsl bash $tempScript
        Remove-Item $tempScript
        Write-Success "WSL环境配置完成"
        return $true
    }
    catch {
        Write-Error "WSL环境配置失败: $($_.Exception.Message)"
        Remove-Item $tempScript -ErrorAction SilentlyContinue
        return $false
    }
}

# 创建配置文件
function New-ConfigFile {
    $configFile = Join-Path $ScriptDir "sync_config.json"
    
    if (Test-Path $configFile) {
        $overwrite = Read-Host "配置文件已存在，是否覆盖？(y/N)"
        if ($overwrite -ne 'y' -and $overwrite -ne 'Y') {
            Write-Info "跳过配置文件创建"
            return
        }
    }
    
    Write-Info "创建配置文件..."
    
    # 获取用户输入
    $localDir = Read-Host "本地同步目录路径 (例: C:\Users\<USER>\Documents\project)"
    $remoteHost = Read-Host "Mac主机IP地址"
    $remoteUser = Read-Host "Mac用户名"
    $remoteDir = Read-Host "Mac端目录路径 (例: /Users/<USER>/Documents/project)"
    
    # 验证本地目录
    if (!(Test-Path $localDir)) {
        $create = Read-Host "本地目录不存在，是否创建？(y/N)"
        if ($create -eq 'y' -or $create -eq 'Y') {
            New-Item -ItemType Directory -Path $localDir -Force | Out-Null
            Write-Success "本地目录创建完成"
        }
    }
    
    # 生成配置文件
    $config = @{
        localDir = $localDir
        remoteHost = $remoteHost
        remoteUser = $remoteUser
        remoteDir = $remoteDir
        syncDelay = 2
        maxRetries = 3
        showProgress = $false
        sshPort = 22
        connectionTimeout = 10
        compressLevel = 6
        logLevel = "INFO"
        logRetentionDays = 7
        excludePatterns = @(
            "*.tmp", "*.temp", "~*", ".DS_Store", "Thumbs.db",
            "desktop.ini", "node_modules\", ".git\", ".vscode\"
        )
        notifications = @{
            enabled = $true
            onError = $true
        }
        backup = @{
            enabled = $true
            maxBackups = 5
        }
    }
    
    $config | ConvertTo-Json -Depth 3 | Out-File -FilePath $configFile -Encoding UTF8
    Write-Success "配置文件创建完成: $configFile"
}

# 设置SSH密钥
function Setup-SSHKey {
    Write-Info "设置SSH密钥..."
    
    $sshDir = "$env:USERPROFILE\.ssh"
    if (!(Test-Path $sshDir)) {
        New-Item -ItemType Directory -Path $sshDir -Force | Out-Null
    }
    
    $keyFile = Join-Path $sshDir "id_rsa"
    if (Test-Path $keyFile) {
        Write-Success "SSH密钥已存在"
    } else {
        $email = Read-Host "请输入您的邮箱地址"
        
        # 在WSL中生成密钥
        wsl ssh-keygen -t rsa -b 4096 -C "$email" -f "/mnt/c/Users/<USER>/.ssh/id_rsa" -N '""'
        Write-Success "SSH密钥生成完成"
    }
    
    # 显示公钥
    Write-Info "您的SSH公钥："
    Write-Host "----------------------------------------" -ForegroundColor Yellow
    Get-Content "$keyFile.pub"
    Write-Host "----------------------------------------" -ForegroundColor Yellow
    Write-Warning "请将上述公钥添加到Mac端的~/.ssh/authorized_keys文件中"
}

# 测试连接
function Test-Connection {
    Write-Info "测试连接..."
    
    $configFile = Join-Path $ScriptDir "sync_config.json"
    if (!(Test-Path $configFile)) {
        Write-Error "配置文件不存在，请先运行安装"
        return $false
    }
    
    $config = Get-Content $configFile | ConvertFrom-Json
    
    # 测试SSH连接
    try {
        $testCmd = "ssh -o ConnectTimeout=10 -o BatchMode=yes $($config.remoteUser)@$($config.remoteHost) 'echo SSH连接成功'"
        $result = wsl bash -c $testCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "SSH连接测试成功"
        } else {
            Write-Error "SSH连接失败"
            return $false
        }
    }
    catch {
        Write-Error "SSH连接测试异常: $($_.Exception.Message)"
        return $false
    }
    
    # 测试远程目录
    try {
        $testDirCmd = "ssh $($config.remoteUser)@$($config.remoteHost) 'test -d `"$($config.remoteDir)`"'"
        wsl bash -c $testDirCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "远程目录访问成功"
        } else {
            Write-Warning "远程目录不存在，尝试创建..."
            $createDirCmd = "ssh $($config.remoteUser)@$($config.remoteHost) 'mkdir -p `"$($config.remoteDir)`"'"
            wsl bash -c $createDirCmd
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "远程目录创建成功"
            } else {
                Write-Error "远程目录创建失败"
                return $false
            }
        }
    }
    catch {
        Write-Error "远程目录测试异常: $($_.Exception.Message)"
        return $false
    }
    
    return $true
}

# 创建启动脚本
function New-LaunchScript {
    $launchScript = Join-Path $ScriptDir "start_sync.ps1"
    
    $scriptContent = @"
# Windows端同步服务启动脚本

`$ScriptDir = Split-Path -Parent `$MyInvocation.MyCommand.Path
`$SyncScript = Join-Path `$ScriptDir "windows_realtime_sync.ps1"

if (!(Test-Path `$SyncScript)) {
    Write-Error "同步脚本不存在: `$SyncScript"
    exit 1
}

Write-Host "启动实时同步服务..." -ForegroundColor Green
& `$SyncScript @args
"@
    
    $scriptContent | Out-File -FilePath $launchScript -Encoding UTF8
    Write-Success "启动脚本创建完成: $launchScript"
}

# 显示完成信息
function Show-Completion {
    Write-Success "Windows端安装完成！"
    Write-Host ""
    Write-Info "下一步操作："
    Write-Host "1. 将SSH公钥添加到Mac端的~/.ssh/authorized_keys文件"
    Write-Host "2. 运行测试: .\setup_windows.ps1 -Test"
    Write-Host "3. 启动同步: .\start_sync.ps1"
    Write-Host ""
    Write-Info "配置文件位置: $ScriptDir\sync_config.json"
    Write-Info "日志文件位置: $ScriptDir\logs\"
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    if ($Test) {
        Test-Connection
        return
    }
    
    Write-Info "开始安装Windows端文件同步环境..."
    
    # 检查管理员权限
    if (!(Test-AdminRights)) {
        Write-Error "需要管理员权限，请以管理员身份运行PowerShell"
        return
    }
    
    # 检查Windows版本
    if (!(Test-WindowsVersion)) {
        return
    }
    
    # 检查并安装WSL
    if (!(Test-WSLStatus)) {
        if (!(Install-WSL)) {
            return
        }
    }
    
    # 安装Ubuntu
    if (!(Install-Ubuntu)) {
        return
    }
    
    # 配置WSL环境
    if (!(Setup-WSLEnvironment)) {
        return
    }
    
    # 创建配置文件
    New-ConfigFile
    
    # 设置SSH密钥
    Setup-SSHKey
    
    # 创建启动脚本
    New-LaunchScript
    
    # 显示完成信息
    Show-Completion
}

# 运行主函数
Main
