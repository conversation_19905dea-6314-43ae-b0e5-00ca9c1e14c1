#!/bin/bash

# Mac端同步环境安装脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message "$GREEN" "✓ $1"
}

print_warning() {
    print_message "$YELLOW" "⚠ $1"
}

print_error() {
    print_message "$RED" "✗ $1"
}

print_info() {
    echo "ℹ $1"
}

# 检查是否为Mac系统
check_macos() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "此脚本仅适用于macOS系统"
        exit 1
    fi
    print_success "检测到macOS系统"
}

# 检查并安装Homebrew
install_homebrew() {
    if command -v brew &> /dev/null; then
        print_success "Homebrew已安装"
    else
        print_info "正在安装Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        print_success "Homebrew安装完成"
    fi
}

# 安装必要工具
install_tools() {
    local tools=("fswatch" "rsync")
    
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            print_success "$tool 已安装"
        else
            print_info "正在安装 $tool..."
            brew install "$tool"
            print_success "$tool 安装完成"
        fi
    done
}

# 生成SSH密钥
setup_ssh() {
    local ssh_dir="$HOME/.ssh"
    local key_file="$ssh_dir/id_rsa"
    
    if [[ ! -d "$ssh_dir" ]]; then
        mkdir -p "$ssh_dir"
        chmod 700 "$ssh_dir"
    fi
    
    if [[ -f "$key_file" ]]; then
        print_success "SSH密钥已存在"
    else
        print_info "生成SSH密钥..."
        read -p "请输入您的邮箱地址: " email
        ssh-keygen -t rsa -b 4096 -C "$email" -f "$key_file" -N ""
        print_success "SSH密钥生成完成"
    fi
    
    # 显示公钥
    print_info "您的SSH公钥："
    echo "----------------------------------------"
    cat "$key_file.pub"
    echo "----------------------------------------"
    print_warning "请将上述公钥添加到Windows端的authorized_keys文件中"
}

# 创建配置文件
create_config() {
    local config_file="$SCRIPT_DIR/sync_config.conf"
    
    if [[ -f "$config_file" ]]; then
        print_warning "配置文件已存在，是否覆盖？(y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            print_info "跳过配置文件创建"
            return
        fi
    fi
    
    print_info "创建配置文件..."
    
    # 获取用户输入
    read -p "本地同步目录路径: " local_dir
    read -p "Windows主机IP地址: " remote_host
    read -p "Windows用户名: " remote_user
    read -p "Windows端目录路径: " remote_dir
    
    # 验证本地目录
    if [[ ! -d "$local_dir" ]]; then
        print_warning "本地目录不存在，是否创建？(y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            mkdir -p "$local_dir"
            print_success "本地目录创建完成"
        fi
    fi
    
    # 生成配置文件
    cat > "$config_file" << EOF
# Mac端同步配置文件
LOCAL_DIR="$local_dir"
REMOTE_HOST="$remote_host"
REMOTE_USER="$remote_user"
REMOTE_DIR="$remote_dir"
SYNC_DELAY=2
MAX_RETRIES=3
SHOW_PROGRESS=false
SSH_PORT=22
CONNECTION_TIMEOUT=10
COMPRESS_LEVEL=6
LOG_LEVEL="INFO"
LOG_RETENTION_DAYS=7
EOF
    
    print_success "配置文件创建完成: $config_file"
}

# 测试连接
test_connection() {
    print_info "测试SSH连接..."
    
    # 加载配置
    source "$SCRIPT_DIR/sync_config.conf"
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH连接成功'" 2>/dev/null; then
        print_success "SSH连接测试成功"
    else
        print_error "SSH连接失败"
        print_info "请确保："
        echo "  1. Windows端已安装SSH服务"
        echo "  2. SSH公钥已添加到Windows端"
        echo "  3. 网络连接正常"
        echo "  4. 防火墙允许SSH连接"
        return 1
    fi
    
    # 测试目录
    if ssh "$REMOTE_USER@$REMOTE_HOST" "test -d '$REMOTE_DIR'" 2>/dev/null; then
        print_success "远程目录访问成功"
    else
        print_warning "远程目录不存在，尝试创建..."
        if ssh "$REMOTE_USER@$REMOTE_HOST" "mkdir -p '$REMOTE_DIR'" 2>/dev/null; then
            print_success "远程目录创建成功"
        else
            print_error "远程目录创建失败"
            return 1
        fi
    fi
}

# 创建启动脚本
create_launcher() {
    local launcher_file="$SCRIPT_DIR/start_sync.sh"
    
    cat > "$launcher_file" << 'EOF'
#!/bin/bash

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SYNC_SCRIPT="$SCRIPT_DIR/mac_realtime_sync.sh"

# 检查同步脚本是否存在
if [[ ! -f "$SYNC_SCRIPT" ]]; then
    echo "错误: 同步脚本不存在: $SYNC_SCRIPT"
    exit 1
fi

# 启动同步服务
echo "启动实时同步服务..."
bash "$SYNC_SCRIPT" "$@"
EOF
    
    chmod +x "$launcher_file"
    chmod +x "$SCRIPT_DIR/mac_realtime_sync.sh"
    
    print_success "启动脚本创建完成: $launcher_file"
}

# 创建系统服务（可选）
create_service() {
    print_info "是否创建系统服务以便开机自启动？(y/N)"
    read -r response
    
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        return
    fi
    
    local service_file="$HOME/Library/LaunchAgents/com.filesync.realtime.plist"
    local script_path="$SCRIPT_DIR/mac_realtime_sync.sh"
    
    cat > "$service_file" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.filesync.realtime</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>$script_path</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>$SCRIPT_DIR/logs/service.log</string>
    <key>StandardErrorPath</key>
    <string>$SCRIPT_DIR/logs/service_error.log</string>
    <key>WorkingDirectory</key>
    <string>$SCRIPT_DIR</string>
</dict>
</plist>
EOF
    
    print_success "系统服务文件创建完成"
    print_info "使用以下命令管理服务："
    echo "  启动: launchctl load $service_file"
    echo "  停止: launchctl unload $service_file"
}

# 显示完成信息
show_completion() {
    print_success "安装完成！"
    echo
    print_info "下一步操作："
    echo "1. 确保Windows端已配置SSH服务"
    echo "2. 将SSH公钥添加到Windows端"
    echo "3. 运行测试: ./setup.sh --test"
    echo "4. 启动同步: ./start_sync.sh"
    echo
    print_info "配置文件位置: $SCRIPT_DIR/sync_config.conf"
    print_info "日志文件位置: $SCRIPT_DIR/logs/"
}

# 主函数
main() {
    case "${1:-}" in
        --test)
            test_connection
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --test    测试连接"
            echo "  --help    显示帮助"
            ;;
        *)
            print_info "开始安装Mac端文件同步环境..."
            check_macos
            install_homebrew
            install_tools
            setup_ssh
            create_config
            create_launcher
            create_service
            show_completion
            ;;
    esac
}

main "$@"
