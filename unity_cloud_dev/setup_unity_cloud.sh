#!/bin/bash

# Unity云端开发环境安装脚本
# 适用于Ubuntu 20.04/22.04云服务器

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查系统要求
check_system() {
    print_header "检查系统环境"
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        print_error "无法检测操作系统"
        exit 1
    fi
    
    source /etc/os-release
    if [[ "$ID" != "ubuntu" ]]; then
        print_warning "此脚本主要针对Ubuntu系统，其他系统可能需要调整"
    fi
    
    print_success "操作系统: $PRETTY_NAME"
    
    # 检查内存
    total_mem=$(free -m | awk 'NR==2{printf "%.1f", $2/1024}')
    if (( $(echo "$total_mem < 4.0" | bc -l) )); then
        print_warning "内存不足4GB，建议升级配置"
    else
        print_success "内存: ${total_mem}GB"
    fi
    
    # 检查磁盘空间
    disk_space=$(df -h / | awk 'NR==2 {print $4}')
    print_success "可用磁盘空间: $disk_space"
    
    # 检查网络
    if ping -c 1 google.com &> /dev/null; then
        print_success "网络连接正常"
    else
        print_error "网络连接异常"
        exit 1
    fi
}

# 更新系统
update_system() {
    print_header "更新系统包"
    
    sudo apt update
    sudo apt upgrade -y
    
    # 安装基础工具
    sudo apt install -y \
        curl \
        wget \
        git \
        git-lfs \
        unzip \
        build-essential \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release \
        bc
    
    print_success "系统更新完成"
}

# 安装Docker (用于容器化开发环境)
install_docker() {
    print_header "安装Docker"
    
    if command -v docker &> /dev/null; then
        print_success "Docker已安装"
        return
    fi
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 安装Docker
    sudo apt update
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # 添加用户到docker组
    sudo usermod -aG docker $USER
    
    print_success "Docker安装完成"
    print_warning "请重新登录以使docker组权限生效"
}

# 安装Node.js (用于一些构建工具)
install_nodejs() {
    print_header "安装Node.js"
    
    if command -v node &> /dev/null; then
        print_success "Node.js已安装: $(node --version)"
        return
    fi
    
    # 安装NodeSource仓库
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt install -y nodejs
    
    print_success "Node.js安装完成: $(node --version)"
}

# 安装VS Code Server
install_vscode_server() {
    print_header "安装VS Code Server"
    
    # 下载并安装code-server
    curl -fsSL https://code-server.dev/install.sh | sh
    
    # 创建配置目录
    mkdir -p ~/.config/code-server
    
    # 生成配置文件
    cat > ~/.config/code-server/config.yaml << EOF
bind-addr: 0.0.0.0:8080
auth: password
password: $(openssl rand -base64 32)
cert: false
EOF
    
    print_success "VS Code Server安装完成"
    print_info "配置文件位置: ~/.config/code-server/config.yaml"
    print_warning "请记录密码并配置防火墙规则"
}

# 安装Unity Hub (如果需要在云端运行Unity Editor)
install_unity_hub() {
    print_header "安装Unity Hub (可选)"
    
    read -p "是否安装Unity Hub到云端？(需要GUI支持) [y/N]: " install_unity
    
    if [[ "$install_unity" =~ ^[Yy]$ ]]; then
        # 安装图形界面支持
        sudo apt install -y ubuntu-desktop-minimal
        
        # 下载Unity Hub
        wget -O unity-hub.AppImage "https://public-cdn.cloud.unity3d.com/hub/prod/UnityHub.AppImage"
        chmod +x unity-hub.AppImage
        sudo mv unity-hub.AppImage /usr/local/bin/unity-hub
        
        print_success "Unity Hub安装完成"
        print_warning "需要重启系统以启用图形界面"
    else
        print_info "跳过Unity Hub安装"
    fi
}

# 配置Git和Git LFS
setup_git() {
    print_header "配置Git环境"
    
    # 初始化Git LFS
    git lfs install
    
    # 设置Git配置 (如果还没有设置)
    if [[ -z "$(git config --global user.name)" ]]; then
        read -p "请输入Git用户名: " git_username
        git config --global user.name "$git_username"
    fi
    
    if [[ -z "$(git config --global user.email)" ]]; then
        read -p "请输入Git邮箱: " git_email
        git config --global user.email "$git_email"
    fi
    
    # 配置Git LFS跟踪Unity文件
    cat > .gitattributes << EOF
# Unity文件类型
*.fbx filter=lfs diff=lfs merge=lfs -text
*.FBX filter=lfs diff=lfs merge=lfs -text
*.psd filter=lfs diff=lfs merge=lfs -text
*.PSD filter=lfs diff=lfs merge=lfs -text
*.tga filter=lfs diff=lfs merge=lfs -text
*.TGA filter=lfs diff=lfs merge=lfs -text
*.tif filter=lfs diff=lfs merge=lfs -text
*.TIF filter=lfs diff=lfs merge=lfs -text
*.tiff filter=lfs diff=lfs merge=lfs -text
*.TIFF filter=lfs diff=lfs merge=lfs -text
*.exr filter=lfs diff=lfs merge=lfs -text
*.EXR filter=lfs diff=lfs merge=lfs -text

# 音频文件
*.mp3 filter=lfs diff=lfs merge=lfs -text
*.MP3 filter=lfs diff=lfs merge=lfs -text
*.ogg filter=lfs diff=lfs merge=lfs -text
*.OGG filter=lfs diff=lfs merge=lfs -text
*.wav filter=lfs diff=lfs merge=lfs -text
*.WAV filter=lfs diff=lfs merge=lfs -text

# 视频文件
*.mp4 filter=lfs diff=lfs merge=lfs -text
*.MP4 filter=lfs diff=lfs merge=lfs -text
*.mov filter=lfs diff=lfs merge=lfs -text
*.MOV filter=lfs diff=lfs merge=lfs -text

# 其他大文件
*.zip filter=lfs diff=lfs merge=lfs -text
*.ZIP filter=lfs diff=lfs merge=lfs -text
*.rar filter=lfs diff=lfs merge=lfs -text
*.RAR filter=lfs diff=lfs merge=lfs -text
*.7z filter=lfs diff=lfs merge=lfs -text
*.7Z filter=lfs diff=lfs merge=lfs -text
EOF
    
    print_success "Git环境配置完成"
}

# 安装文件同步工具
install_sync_tools() {
    print_header "安装文件同步工具"
    
    # 安装rsync
    sudo apt install -y rsync
    
    # 安装Syncthing
    curl -s https://syncthing.net/release-key.txt | sudo apt-key add -
    echo "deb https://apt.syncthing.net/ syncthing stable" | sudo tee /etc/apt/sources.list.d/syncthing.list
    sudo apt update
    sudo apt install -y syncthing
    
    print_success "同步工具安装完成"
}

# 创建Unity项目模板
create_unity_template() {
    print_header "创建Unity项目模板"
    
    mkdir -p ~/unity-projects/template
    cd ~/unity-projects/template
    
    # 创建基本目录结构
    mkdir -p Assets/{Scripts,Prefabs,Materials,Textures,Audio,Scenes}
    mkdir -p ProjectSettings
    
    # 创建.gitignore文件
    cat > .gitignore << EOF
# Unity生成的文件
[Ll]ibrary/
[Tt]emp/
[Oo]bj/
[Bb]uild/
[Bb]uilds/
[Ll]ogs/
[Uu]ser[Ss]ettings/

# MemoryCaptures可以非常大
[Mm]emoryCaptures/

# 资源存储
[Aa]ssets/AssetStoreTools*

# Autogenerated Jetbrains Rider plugin
[Aa]ssets/Plugins/Editor/JetBrains*

# Visual Studio缓存文件
.vs/

# Gradle文件
.gradle/

# Autogenerated VS/MD/Consulo solution and project files
ExportedObj/
.consulo/
*.csproj
*.unityproj
*.sln
*.suo
*.tmp
*.user
*.userprefs
*.pidb
*.booproj
*.svd
*.pdb
*.mdb
*.opendb
*.VC.db

# Unity3D生成的元文件
*.pidb.meta
*.pdb.meta
*.mdb.meta

# Unity3D生成的文件
Temp/
Obj/
UnityGenerated/
Library/

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
EOF
    
    # 初始化Git仓库
    git init
    git add .
    git commit -m "Initial Unity project template"
    
    print_success "Unity项目模板创建完成"
}

# 配置防火墙
setup_firewall() {
    print_header "配置防火墙"
    
    # 启用UFW
    sudo ufw --force enable
    
    # 允许SSH
    sudo ufw allow ssh
    
    # 允许VS Code Server
    sudo ufw allow 8080
    
    # 允许Syncthing
    sudo ufw allow 8384
    sudo ufw allow 22000
    
    print_success "防火墙配置完成"
    sudo ufw status
}

# 创建启动脚本
create_startup_scripts() {
    print_header "创建启动脚本"
    
    # VS Code Server启动脚本
    cat > ~/start-vscode.sh << 'EOF'
#!/bin/bash
echo "启动VS Code Server..."
code-server --bind-addr 0.0.0.0:8080 ~/unity-projects
EOF
    chmod +x ~/start-vscode.sh
    
    # Syncthing启动脚本
    cat > ~/start-syncthing.sh << 'EOF'
#!/bin/bash
echo "启动Syncthing..."
syncthing -no-browser -gui-address=0.0.0.0:8384
EOF
    chmod +x ~/start-syncthing.sh
    
    # 综合启动脚本
    cat > ~/start-dev-env.sh << 'EOF'
#!/bin/bash
echo "启动Unity云端开发环境..."

# 启动VS Code Server
nohup code-server --bind-addr 0.0.0.0:8080 ~/unity-projects > ~/vscode.log 2>&1 &

# 启动Syncthing
nohup syncthing -no-browser -gui-address=0.0.0.0:8384 > ~/syncthing.log 2>&1 &

echo "开发环境已启动"
echo "VS Code Server: http://$(curl -s ifconfig.me):8080"
echo "Syncthing: http://$(curl -s ifconfig.me):8384"
echo ""
echo "日志文件:"
echo "  VS Code: ~/vscode.log"
echo "  Syncthing: ~/syncthing.log"
EOF
    chmod +x ~/start-dev-env.sh
    
    print_success "启动脚本创建完成"
}

# 显示完成信息
show_completion() {
    print_header "安装完成"
    
    echo -e "${GREEN}Unity云端开发环境安装完成！${NC}"
    echo ""
    echo "下一步操作："
    echo "1. 重新登录以使docker组权限生效"
    echo "2. 运行 ~/start-dev-env.sh 启动开发环境"
    echo "3. 访问 VS Code Server: http://$(curl -s ifconfig.me):8080"
    echo "4. 访问 Syncthing: http://$(curl -s ifconfig.me):8384"
    echo ""
    echo "重要文件位置："
    echo "  VS Code配置: ~/.config/code-server/config.yaml"
    echo "  Unity项目: ~/unity-projects/"
    echo "  启动脚本: ~/start-dev-env.sh"
    echo ""
    echo "安全提醒："
    echo "  - 请修改VS Code Server密码"
    echo "  - 配置Syncthing设备认证"
    echo "  - 定期备份项目文件"
    echo ""
    print_warning "请记录VS Code Server密码: $(grep password ~/.config/code-server/config.yaml | cut -d' ' -f2)"
}

# 主函数
main() {
    print_header "Unity云端开发环境安装"
    
    check_system
    update_system
    install_docker
    install_nodejs
    install_vscode_server
    install_unity_hub
    setup_git
    install_sync_tools
    create_unity_template
    setup_firewall
    create_startup_scripts
    show_completion
}

# 运行主函数
main "$@"
