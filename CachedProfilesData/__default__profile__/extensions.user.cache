{"input": {"location": {"$mid": 1, "external": "vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json", "path": "/Users/<USER>/.windsurf/extensions/extensions.json", "scheme": "vscode-userdata"}, "mtime": 1749826854238, "profile": true, "profileScanOptions": {"bailOutWhenFileNotFound": true}, "type": 1, "validate": true, "productVersion": "1.99.3", "productDate": "2025-06-12T07:31:38.222Z", "productCommit": "c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47", "devMode": false, "language": "en", "translations": {}}, "result": [{"type": 1, "identifier": {"id": "visualstudiotoolsforunity.vstuc", "uuid": "17d96133-c9cc-4a9b-8788-7de26979bcdd"}, "manifest": {"name": "vstuc", "publisher": "visualstudiotoolsforunity", "displayName": "Unity", "author": "Microsoft Corporation", "description": "Integrates Visual Studio Code with Unity", "version": "1.1.0", "aiKey": "********************************-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "license": "SEE LICENSE IN LICENSE.md", "icon": "resources/unity-logo.png", "preview": false, "qna": "https://github.com/microsoft/vscode-dotnettools/issues", "bugs": {"url": "https://github.com/microsoft/vscode-dotnettools/issues"}, "engines": {"vscode": "^1.76.0"}, "categories": ["Programming Languages", "Debuggers", "Linters", "Other"], "keywords": ["gaming", "unity", "unity3d", "debugger", "C#", "dotnet", "scripting"], "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": false}}, "activationEvents": ["onDebugResolve:vstuc", "onDebugDynamicConfigurations:vstuc", "workspaceContains:**/Packages/manifest.json"], "main": "./out/extension.js", "contributes": {"csharpExtensionLoadPaths": ["./CodeAnalysis/UnityCodeAnalysis.dll"], "commands": [{"command": "vstuc.attachUnityDebugger", "title": "Attach Unity Debugger"}, {"command": "vstuc.showUnityAPIReference", "title": "Unity API Reference"}], "breakpoints": [{"language": "csharp"}], "debuggers": [{"type": "vstuc", "languages": ["csharp "], "label": "Unity", "configurationAttributes": {"attach": {"required": [], "properties": {"projectPath": {"type": "string", "description": "Project Path", "default": "${workspaceFolder}"}, "logFile": {"type": "string", "description": "Path to the log file for the debugger"}, "endPoint": {"type": "string", "description": "ip:port"}}}}, "initialConfigurations": [{"name": "Unity Debugger", "type": "vstuc", "request": "attach", "projectPath": "${workspaceFolder}"}]}], "jsonValidation": [{"fileMatch": "*.asmdef", "url": "https://json.schemastore.org/asmdef"}], "languages": [{"id": "json", "extensions": [".asmdef"]}, {"id": "UnityShader", "configuration": "./grammars/UnityShader.language-configuration.json", "extensions": [".shader", ".cginc", ".compute", ".hlsl", ".raytrace"]}, {"id": "UnityUSS", "configuration": "./grammars/UnityUSS.language-configuration.json", "extensions": [".uss"]}, {"id": "UnityUXML", "configuration": "./grammars/UnityUXML.language-configuration.json", "extensions": [".uxml"]}], "grammars": [{"language": "UnityShader", "scopeName": "source.shader.unity", "path": "./grammars/UnityShader.json"}, {"language": "UnityUSS", "scopeName": "source.uss.unity", "path": "./grammars/UnityUSS.json"}, {"language": "UnityUXML", "scopeName": "source.uxml.unity", "path": "./grammars/UnityUXML.json"}], "configuration": {"title": "Unity", "properties": {"vstuc.enableMessageCompletion": {"type": "boolean", "default": true, "markdownDescription": "Provide IntelliSense for Unity Messages (restart required)"}, "vstuc.unityMessageScope": {"type": "string", "default": "<PERSON><PERSON><PERSON>", "enum": ["Public", "Private", "<PERSON><PERSON><PERSON>"], "markdownDescription": "Default scope when generating Unity messages (restart required)"}, "vstuc.refreshOnSave": {"type": "boolean", "default": false, "markdownDescription": "Refresh Unity's Asset Database when saving a C# script"}, "vstuc.displayBackgroundProcesses": {"type": "boolean", "default": false, "markdownDescription": "Display background processes created by the main Unity process"}}}}, "scripts": {"compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "webpack --mode production --devtool hidden-source-map", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts --fix", "localize": "npm run ci-prepareLoc && npm run ci-deployLoc", "ci-localize": "gulp localizationBundle", "ci-prepareLoc": "gulp prepare", "ci-deployLoc": "copyfiles -u 2 .localization/out/**/* ./out"}, "extensionDependencies": ["ms-dotnettools.vscode-dotnet-runtime", "ms-dotnettools.csdevkit"], "dependencies": {"vscode-nls": "5.2.0", "@vscode/extension-telemetry": "0.9.6"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.1", "@types/node": "16.x", "@types/vscode": "^1.76.0", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.56.0", "@vscode/test-electron": "^2.4.0", "@vscode/vsce": "^3.1.0", "copyfiles": "^2.4.1", "eslint": "^8.38.0", "eslint-config-standard-with-typescript": "^34.0.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-editorconfig": "^4.0.2", "glob": "^8.1.0", "gulp": "^5.0.0", "gulp-filter": "^9.0.1", "gulp-sourcemaps": "^3.0.0", "gulp-typescript": "^6.0.0-alpha.1", "gulp-concat": "^2.6.1", "mocha": "^10.2.0", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "vscode-nls-dev": "^4.0.4", "webpack": "^5.76.3", "webpack-cli": "^5.0.1"}, "overrides": {"@gulp-sourcemaps/identity-map": {"postcss": "^8.4.38"}, "glob-stream": "^8.0.2"}}, "location": {"$mid": 1, "path": "/Users/<USER>/.windsurf/extensions/visualstudiotoolsforunity.vstuc-1.1.0", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "VisualStudioToolsForUnity", "metadata": {"installedTimestamp": 1749166134985, "source": "gallery", "id": "17d96133-c9cc-4a9b-8788-7de26979bcdd", "publisherId": "0e770294-cf89-4f12-87a4-dee82a1b72b0", "publisherDisplayName": "VisualStudioToolsForUnity", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 22876663}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "ms-dotnettools.csdevkit", "uuid": "7ad42776-8f7e-4214-bf67-4f0b518ef629"}, "manifest": {"name": "csdevkit", "displayName": "C# Dev Kit", "description": "Official C# extension from Microsoft", "publisher": "ms-dot<PERSON><PERSON>s", "version": "1.16.6", "aiKey": "********************************-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "preview": false, "license": "SEE LICENSE IN LICENSE.md", "qna": "https://github.com/microsoft/vscode-dotnettools/issues", "icon": "c-sharp-icon.png", "bugs": {"url": "https://github.com/microsoft/vscode-dotnettools/issues"}, "engines": {"vscode": "^1.93.0"}, "categories": ["Programming Languages", "Debuggers", "Testing", "Linters"], "keywords": ["c#", "csharp", "devkit", "dotnet", "asp.net"], "activationEvents": ["onDebugDynamicConfigurations:dotnet", "onCommand:csdevkit.searchProjectFromCommandPalette", "onCommand:csdevkit.searchOpenFromCommandPalette", "onCommand:csdevkit.searchFromCommandPalette", "onWebviewPanel:csdevkit.installDotnetSdk", "workspaceContains:**/*.csproj", "onLanguage:csharp"], "main": "./dist/extension.js", "contributes": {"keybindings": [{"command": "csdevkit.rename", "key": "f2", "when": "focusedView == solutionExplorer && (selectedSolutionExplorerNodeType == Folder || selectedSolutionExplorerNodeType == File || selectedSolutionExplorerNodeType == Project)"}, {"command": "csdevkit.renameSolutionFolder", "key": "f2", "when": "focusedView == solutionExplorer && selectedSolutionExplorerNodeType == SolutionFolder"}, {"command": "csdevkit.deleteItem", "key": "delete", "when": "focusedView == solutionExplorer && (selectedSolutionExplorerNodeType == Folder || selectedSolutionExplorerNodeType == File)"}, {"command": "csdevkit.removeSolutionFolder", "key": "delete", "when": "focusedView == solutionExplorer && selectedSolutionExplorerNodeType == SolutionFolder"}, {"command": "csdevkit.removeProject", "key": "delete", "when": "focusedView == solutionExplorer && selectedSolutionExplorerNodeType == Project"}, {"command": "csdevkit.removeItem", "key": "delete", "when": "focusedView == solutionExplorer && selectedSolutionExplorerNodeType == LinkedFile"}, {"command": "csdevkit.debug.hotReload", "key": "Ctrl+Shift+Enter", "when": "hotReloadEnabled"}], "configuration": [{"title": "C# Dev Kit", "properties": {"dotnet.autoDetect": {"scope": "application", "type": "string", "enum": ["off", "on"], "default": "on", "description": "Controls auto detection of dotnet tasks."}, "dotnet.preferVisualStudioCodeFileSystemWatcher": {"scope": "window", "type": "boolean", "default": false, "description": "Controls whether the extension uses the default Visual Studio Code File System Watcher. This is recommended if you have a files.watcherExclude setting."}, "dotnet.automaticallySyncWithActiveItem": {"scope": "window", "type": "boolean", "default": false, "description": "Enables/disables the feature for the extension to automatically track the currently active item in the editor and highlight it within the Solution Explorer."}, "dotnet.automaticallyCreateSolutionInWorkspace": {"scope": "window", "type": "boolean", "default": true, "description": "Automatically create a solution file in the workspace if one does not exist. This may not work correctly for all cases."}, "dotnet.previewSolution-freeWorkspaceMode": {"scope": "window", "type": "boolean", "default": false, "description": "[Preview] Prevent the automatic creation of solution files in workspaces that do not already contain one. This feature takes priority over the 'Automatically Create Solution In Workspace' setting, even if it is enabled."}, "dotnet.testWindow.useTestingPlatformProtocol": {"scope": "window", "type": "boolean", "default": false, "description": "Experimental: Controls whether the extension will start MSTest runner test projects directly as an executable, rather than start them under vstest.console."}, "dotnet.testWindow.disableAutoDiscovery": {"scope": "window", "type": "boolean", "default": false, "description": "Disable automatic test discovery when test binaries change on disk. This can avoid file locking issues when building large solutions."}, "dotnet.testWindow.disableBuildOnRefresh": {"scope": "window", "type": "boolean", "default": false, "description": "Disable automatic build when triggering a test discovery refresh. If this option is set, you must manually build your tests before refresh to make sure they are up to date."}, "dotnet.testWindow.disableBuildOnRun": {"scope": "window", "type": "boolean", "default": false, "description": "Disable automatic build before running tests. If this option is set, you must manually build your tests before running to make sure they are up to date."}, "dotnet.workingWithTestSignedSdk": {"scope": "machine", "type": "boolean", "default": false, "description": "Working with ad-hoc signed SDK on a Mac computer. This option forces the C# Dev Kit extension to work in a slower compatible mode, and should only be turned on environment when it is essential."}, "dotnet.useLegacyDotnetResolution": {"scope": "window", "type": "boolean", "default": true, "description": "Use the legacy dotnet resolution. This is a temporary option to workaround potential issues with the new logic."}, "csharp.experimental.debug.hotReload": {"scope": "machine", "type": "boolean", "default": false, "description": "[Experimental] Enables C# Hot Reload while debugging."}, "csharp.debug.hotReloadOnSave": {"scope": "machine", "type": "boolean", "default": true, "description": "When true, saving a file will automatically apply changes when Hot Reload is available."}, "csharp.debug.hotReloadVerbosity": {"scope": "machine", "type": "string", "enum": ["minimal", "detailed", "diagnostic"], "enumDescriptions": ["Show only minimal information.", "Show detailed information about how changes are found and applied to the target process.", "Show diagnostic information on each step of a hot reload operation. Useful when diagnosing unexpected behaviors."], "default": "minimal", "description": "The verbosity of logging in the Output Panel for '.NET Hot Reload'. The order of levels from least verbose to most verbose is: 'minimal' < 'detailed' < 'diagnostic' (default: 'minimal')"}, "csharp.preview.improvedLaunchExperience": {"scope": "window", "type": "boolean", "default": false, "description": "[Preview] Enables an improved launch experience with UI for choosing startup projects and launch configurations. VS Code must be restarted for changes to take effect."}, "nuget.includePrereleasePackageVersions": {"scope": "machine", "type": "boolean", "default": false, "description": "Include prerelease package versions for all NuGet package operations."}, "csharp.debug.debugConsoleVerbosity": {"scope": "machine", "type": "string", "enum": ["minimal", "verbose"], "enumDescriptions": ["Show minimal details in the debug console. Additional information is available in the 'C# Debugger' output window channel.", "Show all information in the debug console."], "default": "minimal", "description": "Sets the log level for messages sent by the debugger for the Debug Console. [Learn more...](https://aka.ms/vscode/dotnet/debugConsoleVerbosity)"}, "csharp.debug.testExplorerVerbosity": {"scope": "machine", "type": "string", "enum": ["minimal", "diagnostic"], "enumDescriptions": ["Show minimal details in 'C# devkit - Test Explorer' output window.", "Show diagnostic information in 'C# devkit - Test Explorer' output window"], "default": "minimal", "description": "Sets the verbosity of logging in the 'C# DevKit - Test Explorer' output window"}}}], "commands": [{"command": "csdevkit.openSolution", "title": "Open Solution", "category": ".NET"}, {"command": "csdevkit.closeSolution", "title": "Close Solution", "category": ".NET", "icon": {"light": "imageCatalog/png/Close.16.16.png", "dark": "imageCatalog/png/Close.dark.16.16.png"}}, {"command": "csdevkit.addNewProject", "title": "New Project...", "category": ".NET", "icon": "$(add)"}, {"command": "csdevkit.addNewProjectFromNewFileMenu", "title": ".NET: New Project...", "icon": "$(add)"}, {"command": "csdevkit.addNewFile", "title": "New File...", "category": ".NET", "icon": "$(new-file)"}, {"command": "csdevkit.addNewFileFromNewFileMenu", "title": ".NET: New File...", "icon": "$(new-file)"}, {"command": "csdevkit.addExistingFile", "title": "Add Existing File...", "category": ".NET"}, {"command": "csdevkit.addProjectReference", "title": "Add Project Reference", "category": ".NET"}, {"command": "csdevkit.showEntitlementsStatus", "title": "Show entitlement status"}, {"command": "csdevkit.openInTerminal", "title": "Open In Integrated Terminal"}, {"command": "csdevkit.addExistingProject", "title": "Add Existing Project...", "category": ".NET"}, {"command": "csdevkit.buildSolution", "title": "Build", "category": ".NET"}, {"command": "csdevkit.rebuildSolution", "title": "Rebuild", "category": ".NET"}, {"command": "csdevkit.cleanSolution", "title": "Clean", "category": ".NET"}, {"command": "csdevkit.removeProject", "title": "Remove", "category": ".NET"}, {"command": "csdevkit.signIntoVsAccount", "title": "Sign into Visual Studio account", "category": ".NET"}, {"command": "csdevkit.revealFileInOS.windows", "title": "Reveal in File Explorer", "category": ".NET"}, {"command": "csdevkit.revealFileInOS.mac", "title": "Reveal in Finder", "category": ".NET"}, {"command": "csdevkit.revealFileInOS.linux", "title": "Open Containing Folder", "category": ".NET"}, {"command": "csdevkit.deleteItem", "title": "Delete", "category": ".NET"}, {"command": "csdevkit.rename", "title": "Rename...", "category": ".NET"}, {"command": "csdevkit.copyPath", "title": "Copy Path", "category": ".NET"}, {"command": "csdevkit.copyRelativePath", "title": "Copy Relative Path", "category": ".NET"}, {"command": "csdevkit.newSolutionFolder", "title": "New Solution Folder...", "category": ".NET", "icon": "$(new-folder)", "when": "!generatedSlnOutsideWorkspace"}, {"command": "csdevkit.renameSolutionFolder", "title": "Rename Solution Folder...", "category": ".NET"}, {"command": "csdevkit.removeSolutionFolder", "title": "Remove", "category": ".NET"}, {"command": "csdevkit.newFolder", "title": "New Folder...", "category": ".NET", "icon": "$(new-folder)"}, {"command": "csdevkit.removeItem", "title": "Remove", "category": ".NET"}, {"command": "csdevkit.openProjectsLogsView", "title": "Open project system logs", "category": ".NET"}, {"command": "csdevkit.debug.hotReload", "title": "Hot Reload", "category": "Debug", "icon": {"light": "imageCatalog/svg/HotReload.16.16.svg", "dark": "imageCatalog/svg/HotReload.dark.16.16.svg"}, "enablement": "hotReloadEnabled"}, {"command": "csdevkit.debug.showHotReloadPanel", "title": "Show Hot Reload output", "category": "Debug", "enablement": "hotReloadEnabled"}, {"command": "csdevkit.debug.projectDebugLaunch", "title": "Start New Instance", "category": "Debug"}, {"command": "csdevkit.debug.noDebugProjectLaunch", "title": "Start without Debugging", "category": "Debug"}, {"command": "csdevkit.debug.stepIntoProjectLaunch", "title": "Step into New Instance", "category": "Debug"}, {"command": "csdevkit.debug.fileLaunch", "title": "Debug project associated with this file", "category": "Debug", "icon": "$(debug-alt)"}, {"command": "csdevkit.debug.noDebugFileLaunch", "title": "Run project associated with this file", "category": "Debug", "icon": "$(run)"}, {"command": "csdevkit.debug.selectStartupProject", "title": "Select C# Startup Project", "category": ".NET"}, {"command": "csdevkit.debug.selectLaunchTargetFramework", "title": "Select Launch Target Framework", "category": ".NET"}, {"command": "csdevkit.debug.selectLaunchConfiguration", "title": "Select Launch Configuration", "category": ".NET"}, {"command": "csdevkit.installDotnetSdk", "category": ".NET", "title": "Install New .NET SDK"}, {"command": "csdevkit.installDotnetSdk.fromWalkthrough", "category": ".NET", "title": "Install New .NET SDK"}, {"command": "csdevkit.syncWithActiveItem", "title": "Sync with Active Document", "category": ".NET"}, {"command": "csdevkit.addNuGetPackage", "title": "Add NuGet Package...", "category": "NuGet"}, {"command": "csdevkit.updateNuGetPackage", "title": "Update NuGet Package...", "category": "NuGet"}, {"command": "csdevkit.removeNuGetPackage", "title": "Remove NuGet Package...", "category": "NuGet"}, {"command": "csdevkit.selectActiveConfiguration", "title": "Select a Configuration...", "category": ".NET"}, {"command": "csdevkit.openCoverageReport", "title": "Open Coverage Report...", "category": ".NET"}, {"command": "csdevkit.addAspireOrchestration", "title": "Add .NET Aspire Orchestration (Preview)", "category": ".NET"}, {"command": "csdevkit.saveSolutionFile", "title": "Create a Solution File", "category": ".NET"}], "walkthroughs": [{"id": "csdevkit.gettingStarted", "title": "Get Started with C# Dev Kit", "description": "Your first steps to set up your .NET environment with powerful, easy-to-use tooling.", "steps": [{"id": "csdevkit.signInStep", "title": "Connect account", "description": "You can sign in with your Microsoft account. If you have a Visual Studio subscription, you can get information about your subscription after you've signed in.\n[Connect](command:csdevkit.signIntoVsAccount)", "media": {"image": "media/sign-in-win.svg", "altText": "Connect account"}}, {"id": "csdevkit.setupEnvironmentStep", "title": "Set up your environment", "description": "The .NET SDK is required to build with .NET in VS Code.\n[Install .NET SDK](command:csdevkit.installDotnetSdk.fromWalkthrough)", "media": {"markdown": "media/install-sdk.md"}}, {"id": "csdevkit.openWorkspaceStep", "title": "Open your folder", "description": "Open your solution folder. Supported project types include:\n   - ASP.NET Core web projects\n   - .NET Console app projects\n   - .NET class library projects.\n[Open Folder](command:workbench.action.files.openFolder)", "media": {"image": "media/open-folder-win.svg", "altText": "Open Folder"}}, {"id": "csdevkit.createNewProjectStep", "title": "Create your .NET project", "description": "Choose your project template from the command palette to scaffold your project.\nOnce your project is created, you can view your files in the Solution Explorer.\n[Choose project template](command:csdevkit.searchProjectFromCommandPalette)", "media": {"image": "media/create-project-win.svg", "altText": "Create Project"}}, {"id": "csdevkit.launchDebugAndTestStep", "title": "Launch, debug, and test", "description": "Run and debug your application using the debugger. [Learn More...](https://code.visualstudio.com/docs/csharp/debugging).\n\nThe Test Explorer allows you to view, run, and manage all test cases in your project. [Learn More...](https://code.visualstudio.com/docs/languages/csharp).\n\n[Show Run and Debug View](command:workbench.view.debug)", "media": {"image": "media/launch-debug-test-win.svg", "altText": "Run and Debug View"}}, {"id": "csdevkit.learnMoreStep", "title": "Learn More", "description": "Explore all the features the C# extension has to offer by looking for “.NET:” in the Command Palette.\n[Open Command Palette](command:csdevkit.searchFromCommandPalette)", "media": {"markdown": "media/learn-more-win.md"}}]}], "menus": {"explorer/context": [{"command": "csdevkit.openSolution", "when": "resourceScheme == file && resourceExtname == .sln", "group": "navigation"}], "file/newFile": [{"command": "csdevkit.addNewProjectFromNewFileMenu", "group": ".NET"}, {"command": "csdevkit.addNewFileFromNewFileMenu", "group": ".NET", "when": "solutionOpened"}], "view/item/context": [{"command": "csdevkit.addNewProject", "when": "view == solutionExplorer && viewItem == Solution && isValidSolution", "group": "inline"}, {"command": "csdevkit.addNewProject", "when": "view == solutionExplorer && viewItem == SolutionFolder", "group": "inline"}, {"command": "csdevkit.addNewFile", "when": "view == solutionExplorer && (viewItem == Folder || viewItem == Project || viewItem == SolutionFolder)", "group": "inline"}, {"command": "csdevkit.newFolder", "when": "view == solutionExplorer && (viewItem == Folder || viewItem == Project)", "group": "inline"}, {"command": "csdevkit.newSolutionFolder", "when": "view == solutionExplorer && viewItem == Solution && isValidSolution && !generatedSlnOutsideWorkspace", "group": "inline"}, {"command": "csdevkit.addNewProject", "when": "view == solutionExplorer && viewItem == Solution && isValidSolution", "group": "1_group@1"}, {"command": "csdevkit.addExistingProject", "when": "view == solutionExplorer && viewItem == Solution && isValidSolution && !generatedSlnOutsideWorkspace", "group": "1_group@2"}, {"command": "csdevkit.newSolutionFolder", "when": "view == solutionExplorer && viewItem == Solution && isValidSolution && !generatedSlnOutsideWorkspace", "group": "1_group@3"}, {"command": "csdevkit.saveSolutionFile", "when": "view == solutionExplorer && viewItem == Solution && generatedSlnOutsideWorkspace", "group": "1_group@4"}, {"command": "csdevkit.openInTerminal", "when": "view == solutionExplorer && viewItem == Solution && isValidSolution", "group": "1_group@5"}, {"command": "csdevkit.addAspireOrchestration", "when": "view == solutionExplorer && viewItem == Solution && isValidSolution", "group": "1_group@6"}, {"command": "csdevkit.buildSolution", "when": "view == solutionExplorer && viewItem == Solution && solutionBuildContextVisible && isValidSolution && !generatedSlnOutsideWorkspace", "group": "2_group@1"}, {"command": "csdevkit.rebuildSolution", "when": "view == solutionExplorer && viewItem == Solution && solutionBuildContextVisible && isValidSolution && !generatedSlnOutsideWorkspace", "group": "2_group@2"}, {"command": "csdevkit.cleanSolution", "when": "view == solutionExplorer && viewItem == Solution && solutionBuildContextVisible && isValidSolution && !generatedSlnOutsideWorkspace", "group": "2_group@3"}, {"command": "csdevkit.revealFileInOS.windows", "when": "isWindows && view == solutionExplorer && viewItem == Solution", "group": "4_group@2"}, {"command": "csdevkit.revealFileInOS.mac", "when": "isMac && view == solutionExplorer && viewItem == Solution", "group": "4_group@2"}, {"command": "csdevkit.revealFileInOS.linux", "when": "isLinux && view == solutionExplorer && viewItem == Solution", "group": "4_group@2"}, {"command": "csdevkit.closeSolution", "when": "view == solutionExplorer && viewItem == Solution", "group": "4_group@3"}, {"command": "csdevkit.addNewProject", "when": "view == solutionExplorer && viewItem == SolutionFolder", "group": "1_group@1"}, {"command": "csdevkit.addExistingProject", "when": "view == solutionExplorer && viewItem == SolutionFolder && !generatedSlnOutsideWorkspace", "group": "1_group@2"}, {"command": "csdevkit.newSolutionFolder", "when": "view == solutionExplorer && viewItem == SolutionFolder && !generatedSlnOutsideWorkspace", "group": "1_group@3"}, {"command": "csdevkit.addNewFile", "when": "view == solutionExplorer && viewItem == SolutionFolder", "group": "1_group@4"}, {"command": "csdevkit.addExistingFile", "when": "view == solutionExplorer && viewItem == SolutionFolder", "group": "1_group@5"}, {"command": "csdevkit.renameSolutionFolder", "when": "view == solutionExplorer && viewItem == SolutionFolder && !generatedSlnOutsideWorkspace", "group": "3_group@1"}, {"command": "csdevkit.removeSolutionFolder", "when": "view == solutionExplorer && viewItem == SolutionFolder && !generatedSlnOutsideWorkspace", "group": "3_group@2"}, {"command": "csdevkit.addNewFile", "when": "view == solutionExplorer && viewItem == Project", "group": "1_group@1"}, {"command": "csdevkit.newFolder", "when": "view == solutionExplorer && viewItem == Project", "group": "1_group@2"}, {"command": "csdevkit.openInTerminal", "when": "view == solutionExplorer && viewItem == Project", "group": "1_group@3"}, {"command": "csdevkit.addProjectReference", "when": "view == solutionExplorer && viewItem == Project", "group": "2_group@1"}, {"command": "csdevkit.addNuGetPackage", "when": "view == solutionExplorer && viewItem == Project", "group": "2_group@2"}, {"command": "csdevkit.buildSolution", "when": "view == solutionExplorer && viewItem == Project && solutionBuildContextVisible", "group": "3_group@1"}, {"command": "csdevkit.rebuildSolution", "when": "view == solutionExplorer && viewItem == Project && solutionBuildContextVisible", "group": "3_group@2"}, {"command": "csdevkit.cleanSolution", "when": "view == solutionExplorer && viewItem == Project && solutionBuildContextVisible", "group": "3_group@3"}, {"command": "csdevkit.revealFileInOS.windows", "when": "isWindows && view == solutionExplorer && viewItem == Project", "group": "4_group@1"}, {"command": "csdevkit.revealFileInOS.mac", "when": "isMac && view == solutionExplorer && viewItem == Project", "group": "4_group@1"}, {"command": "csdevkit.revealFileInOS.linux", "when": "isLinux && view == solutionExplorer && viewItem == Project", "group": "4_group@1"}, {"command": "csdevkit.removeProject", "when": "view == solutionExplorer && viewItem == Project", "group": "4_group@2"}, {"command": "csdevkit.rename", "when": "view == solutionExplorer && viewItem == Project", "group": "4_group@3"}, {"submenu": "csdevkit.debug.projectLaunch", "when": "view == solutionExplorer && viewItem == Project", "group": "5_group@1"}, {"command": "csdevkit.addNewFile", "when": "view == solutionExplorer && viewItem == Folder", "group": "1_group@1"}, {"command": "csdevkit.newFolder", "when": "view == solutionExplorer && viewItem == Folder", "group": "1_group@2"}, {"command": "csdevkit.openInTerminal", "when": "view == solutionExplorer && viewItem == Folder", "group": "1_group@3"}, {"command": "csdevkit.revealFileInOS.windows", "when": "isWindows && view == solutionExplorer && viewItem == Folder", "group": "2_group@1"}, {"command": "csdevkit.revealFileInOS.mac", "when": "isMac && view == solutionExplorer && viewItem == Folder", "group": "2_group@1"}, {"command": "csdevkit.revealFileInOS.linux", "when": "isLinux && view == solutionExplorer && viewItem == Folder", "group": "2_group@1"}, {"command": "csdevkit.rename", "when": "view == solutionExplorer && viewItem == Folder", "group": "4_group@1"}, {"command": "csdevkit.deleteItem", "when": "view == solutionExplorer && viewItem == Folder", "group": "4_group@2"}, {"command": "csdevkit.openInTerminal", "when": "view == solutionExplorer && viewItem == File", "group": "1_group@3"}, {"command": "csdevkit.revealFileInOS.windows", "when": "isWindows && view == solutionExplorer && viewItem == File", "group": "2_group@1"}, {"command": "csdevkit.revealFileInOS.mac", "when": "isMac && view == solutionExplorer && viewItem == File", "group": "2_group@1"}, {"command": "csdevkit.revealFileInOS.linux", "when": "isLinux && view == solutionExplorer && viewItem == File", "group": "2_group@1"}, {"command": "csdevkit.copyPath", "when": "view == solutionExplorer && viewItem == File", "group": "3_group@1"}, {"command": "csdevkit.copyRelativePath", "when": "view == solutionExplorer && viewItem == File", "group": "3_group@2"}, {"command": "csdevkit.rename", "when": "view == solutionExplorer && viewItem == File", "group": "4_group@1"}, {"command": "csdevkit.deleteItem", "when": "view == solutionExplorer && viewItem == File", "group": "4_group@2"}, {"command": "csdevkit.openInTerminal", "when": "view == solutionExplorer && viewItem == LinkedFile", "group": "1_group@1"}, {"command": "csdevkit.revealFileInOS.windows", "when": "isWindows && view == solutionExplorer && viewItem == LinkedFile", "group": "1_group@2"}, {"command": "csdevkit.revealFileInOS.mac", "when": "isMac && view == solutionExplorer && viewItem == LinkedFile", "group": "1_group@2"}, {"command": "csdevkit.revealFileInOS.linux", "when": "isLinux && view == solutionExplorer && viewItem == LinkedFile", "group": "1_group@2"}, {"command": "csdevkit.copyPath", "when": "view == solutionExplorer && viewItem == LinkedFile", "group": "2_group@1"}, {"command": "csdevkit.copyRelativePath", "when": "view == solutionExplorer && viewItem == LinkedFile", "group": "2_group@2"}, {"command": "csdevkit.removeItem", "when": "view == solutionExplorer && viewItem == LinkedFile", "group": "3_group@1"}, {"command": "csdevkit.updateNuGetPackage", "when": "view == solutionExplorer && viewItem == PackageDependency", "group": "1_group@1"}, {"command": "csdevkit.removeNuGetPackage", "when": "view == solutionExplorer && viewItem == PackageDependency", "group": "1_group@2"}], "editor/title/run": [{"command": "csdevkit.debug.noDebugFileLaunch", "when": "editorLangId == 'csharp'", "group": "navigation@0"}, {"command": "csdevkit.debug.fileLaunch", "when": "editorLangId == 'csharp'", "group": "navigation@1"}], "commandPalette": [{"command": "csdevkit.openInTerminal", "when": "false"}, {"command": "csdevkit.syncWithActiveItem", "when": "solutionOpened"}, {"command": "csdevkit.buildSolution", "when": "solutionBuildContextVisible"}, {"command": "csdevkit.rebuildSolution", "when": "solutionBuildContextVisible"}, {"command": "csdevkit.cleanSolution", "when": "solutionBuildContextVisible"}, {"command": "csdevkit.deleteItem", "when": "false"}, {"command": "csdevkit.addNewFile", "when": "solutionOpened"}, {"command": "csdevkit.addExistingFile", "when": "false"}, {"command": "csdevkit.addProjectReference", "when": "false"}, {"command": "csdevkit.rename", "when": "false"}, {"command": "csdevkit.copyPath", "when": "false"}, {"command": "csdevkit.copyRelativePath", "when": "false"}, {"command": "csdevkit.revealFileInOS.windows", "when": "false"}, {"command": "csdevkit.revealFileInOS.mac", "when": "false"}, {"command": "csdevkit.revealFileInOS.linux", "when": "false"}, {"command": "csdevkit.newSolutionFolder", "when": "false"}, {"command": "csdevkit.renameSolutionFolder", "when": "false"}, {"command": "csdevkit.removeSolutionFolder", "when": "false"}, {"command": "csdevkit.newFolder", "when": "false"}, {"command": "csdevkit.removeProject", "when": "false"}, {"command": "csdevkit.removeItem", "when": "false"}, {"command": "csdevkit.closeSolution", "when": "solutionOpened"}, {"command": "csdevkit.addExistingProject", "when": "workspaceFolderCount >= 1 && solutionOpened && !generatedSlnOutsideWorkspace"}, {"command": "csdevkit.openSolution", "when": "workspaceFolderCount >= 1"}, {"command": "csdevkit.debug.hotReload", "when": "false"}, {"command": "csdevkit.debug.selectLaunchConfiguration", "when": "workspaceFolderCount >= 1 && solutionOpened"}, {"command": "csdevkit.debug.selectStartupProject", "when": "workspaceFolderCount >= 1 && solutionOpened"}, {"command": "csdevkit.debug.selectLaunchTargetFramework", "when": "workspaceFolderCount >= 1 && solutionOpened"}, {"command": "csdevkit.debug.showHotReloadPanel", "when": "false"}, {"command": "csdevkit.debug.projectDebugLaunch", "when": "false"}, {"command": "csdevkit.debug.noDebugProjectLaunch", "when": "false"}, {"command": "csdevkit.debug.stepIntoProjectLaunch", "when": "false"}, {"command": "csdevkit.debug.fileLaunch", "when": "false"}, {"command": "csdevkit.debug.noDebugFileLaunch", "when": "false"}, {"command": "csdevkit.installDotnetSdk.fromWalkthrough", "when": "false"}, {"command": "csdevkit.addNuGetPackage", "when": "workspaceFolderCount >= 1 && solutionOpened"}, {"command": "csdevkit.updateNuGetPackage", "when": "workspaceFolderCount >= 1 && solutionOpened"}, {"command": "csdevkit.removeNuGetPackage", "when": "workspaceFolderCount >= 1 && solutionOpened"}, {"command": "csdevkit.selectActiveConfiguration", "when": "workspaceFolderCount >= 1 && solutionOpened"}, {"command": "csdevkit.addNewProjectFromNewFileMenu", "when": "false"}, {"command": "csdevkit.addNewFileFromNewFileMenu", "when": "false"}, {"command": "csdevkit.saveSolutionFile", "when": "generatedSlnOutsideWorkspace"}], "debug/toolBar": [{"command": "csdevkit.debug.hotReload", "when": "hotReloadEnabled", "group": "navigation@65"}], "csdevkit.debug.projectLaunch": [{"command": "csdevkit.debug.projectDebugLaunch", "title": "Start New Instance", "category": "Debug"}, {"command": "csdevkit.debug.noDebugProjectLaunch", "title": "Start without Debugging", "category": "Debug"}, {"command": "csdevkit.debug.stepIntoProjectLaunch", "title": "Step into New Instance", "category": "Debug"}]}, "submenus": [{"id": "csdevkit.debug.projectLaunch", "label": "Debug"}], "views": {"explorer": [{"id": "solutionExplorer", "name": "Solution Explorer", "when": "solutionExplorerVisible", "icon": "./imageCatalog/svg/Solution.16.16.svg"}]}, "viewsWelcome": [{"view": "solutionExplorer", "contents": "[Open Solution](command:csdevkit.openSolution)", "when": "!solutionOpened"}, {"view": "explorer", "contents": "You can [open a folder containing a .NET project or solution](command:vscode.openFolder), or create a new .NET project.\n[Create .NET Project](command:csdevkit.addNewProject)", "when": "workspaceFolderCount == 0"}, {"view": "testing", "contents": "Build your C# test projects to view the tests here."}], "taskDefinitions": [{"type": "dotnet", "required": ["task"], "properties": {"task": {"type": "string", "description": "The dotnet task to customize."}, "file": {"type": "string", "description": "File associated with the task. Can be omitted."}}}], "jsonValidation": [{"fileMatch": ["appsettings.json", "appsettings.*.json"], "url": "https://json.schemastore.org/appsettings.json"}, {"fileMatch": "launchSettings.json", "url": "https://json.schemastore.org/launchsettings.json"}], "icons": {"statusbar-no-entitlement": {"description": "No Entitlement", "default": {"fontPath": "media/csharp-dev-kit-icon-font.woff", "fontCharacter": "A"}}, "statusbar-attention-needed": {"description": "Attention Needed", "default": {"fontPath": "media/csharp-dev-kit-icon-font.woff", "fontCharacter": "B"}}, "statusbar-entitlement": {"description": "Entitlement Available", "default": {"fontPath": "media/csharp-dev-kit-icon-font.woff", "fontCharacter": "C"}}}}, "brokeredServices": [{"moniker": {"name": "helloVSCodeExtensionHost", "version": "1.0"}}, {"moniker": {"name": "Microsoft.VisualStudio.ComponentExportsService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.BrokeredServiceExportsRegistrar", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.ErrorReportingService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.ActiveDocumentsInformationService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.BuildService", "version": "0.1"}, "serviceHub": {}}, {"moniker": {"name": "Microsoft.VisualStudio.ProjectSystem.Query.Remoting.QueryExecutionService", "version": "0.3"}, "serviceHub": {}}, {"moniker": {"name": "Microsoft.VisualStudio.ProjectSystem.ProjectInitializationStatusService", "version": "0.1"}, "serviceHub": {}}, {"moniker": {"name": "Microsoft.VisualStudio.OutOfProcServiceRegistration", "version": "0.1"}, "serviceHub": {}}, {"moniker": {"name": "Microsoft.VisualStudio.TokenAcquisitionService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.NuGetSolutionRestoreService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.LogOutputChannelService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.Telemetry.TelemetrySessionUpdateService", "version": "0.1"}, "serviceHub": {}}, {"moniker": {"name": "Microsoft.VisualStudio.Debugger.HotReloadLogger", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.Debugger.HotReloadClientListener", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.Debugger.HotReloadOptionService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.Debugger.HotReloadClientService", "version": "0.1"}, "serviceHub": {}}, {"moniker": {"name": "Microsoft.VisualStudio.Debugger.DotnetDebugConfigurationService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.VsCodeFileWatcherService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.DotnetDebugLaunchService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.NuGetCommandsService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.OptionService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.OutputChannelService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.StartupProjectService", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.Debugger.VSCodeAzureFunctionExtensionService", "version": "0.1"}}], "scripts": {"vscode:prepublish": "yarn run package && dotnet build tools/msbuild/signJs -v:normal", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc", "watch-tests": "tsc -w", "lint": "eslint src --ext ts", "unit-test": "jest", "aspire-integration-test": "yarn run compile-tests && yarn run compile && yarn run lint && yarn run localize && node ./out/test/integration/runAspireTestFunctionalSuite.js", "integration-test": "yarn run compile-tests && yarn run compile && yarn run lint && yarn run localize && node ./out/test/integration/runTestFunctionalSuite.js", "integration-test-perf": "yarn run compile-tests && yarn run compile && yarn run lint && yarn run localize && node ./out/test/integration/runTestPerformanceSuite.js", "test": "yarn unit-test && yarn integration-test", "localize": "yarn ci-prepareLoc && yarn ci-deployLoc", "ci-localize": "gulp localizationBundle", "ci-prepareLoc": "gulp prepare", "ci-deployLoc": "copyfiles -u 2 .localization/out/**/* ./dist"}, "extensionDependencies": ["ms-dotnettools.csharp", "ms-dotnettools.vscode-dotnet-runtime"], "dependencies": {"@iconify-icons/codicon": "1.2.8", "@iconify/react": "^1.1.4", "@microsoft/coverage-client": "17.14.0-preview.24611.3", "@microsoft/servicehub-framework": "4.7.30-beta", "@microsoft/visualstudio-testwindow-vscode-client": "17.13.0-beta.24610.3", "@opentelemetry/api": "1.4.1", "@reduxjs/toolkit": "^1.8.6", "@vscode/codicons": "^0.0.35", "@vscode/deviceid": "^0.1.1", "@vscode/extension-telemetry": "0.9.6", "@vscode/webview-ui-toolkit": "1.4.0", "immutable": "^4.3.1", "lazy-promise": "^4.0.0", "react": "^18.2.0", "react-bootstrap": "^1.6.6", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "semver": "^7.5.4", "tslib": "^2.6.2", "uuid": "^9.0.0", "vscode-extension-telemetry-wrapper": "^0.13.2", "vscode-nls": "5.2.0", "vscode-tas-client": "^0.1.84"}, "devDependencies": {"@types/bytes": "^3.1.1", "@types/microsoft__typescript-etw": "^0.1.1", "@types/mocha": "^10.0.0", "@types/node": "16.x", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@types/screenshot-desktop": "^1.12.0", "@types/semver": "^7.5.1", "@types/sinon": "^10.0.13", "@types/uuid": "^9.0.3", "@types/vscode": "^1.93.0", "@types/which": "^3.0.0", "@types/winreg": "^1.2.32", "@typescript-eslint/eslint-plugin": "^6.6.0", "@typescript-eslint/parser": "^6.3.0", "@vscode/test-electron": "^2.3.8", "@vscode/vsce": "^2.16.0", "applicationinsights": "^2.5.1", "assert": "^2.0.0", "autoprefixer": "^10.4.12", "bytes": "^3.1.2", "copy-webpack-plugin": "^11.0.0", "copyfiles": "^2.4.1", "css-loader": "^6.7.1", "eslint": "^8.47.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-import": "2.28.0", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "5.0.0", "fantasticon": "1.2.3", "file-loader": "^6.2.0", "glob": "^10.3.3", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-filter": "^7.0.0", "gulp-sourcemaps": "^3.0.0", "gulp-typescript": "^6.0.0-alpha.1", "jest": "^29.2.2", "jest-junit": "^16.0.0", "mocha": "^10.0.0", "mocha-junit-reporter": "^2.2.0", "node-loader": "^2.0.0", "postcss": "^8.4.31", "postcss-loader": "^6.2.1", "prettier": "^3.0.01", "process": "0.11.10", "sass": "^1.55.0", "sass-loader": "^12.6.0", "screenshot-desktop": "^1.15.0", "sinon": "^15.0.1", "style-loader": "^3.3.1", "ts-jest": "^29.1.0", "ts-loader": "^9.4.1", "ts-node": "^10.9.1", "typescript": "^5.0.2", "vscode-nls-dev": "^4.0.1", "webpack": "^5.74.0", "webpack-cli": "^5.1.4", "winreg": "^1.2.4"}, "optionalDependencies": {"@microsoft/typescript-etw": "^0.1.0"}, "resolutions": {"@opentelemetry/instrumentation": "^0.41.2"}}, "location": {"$mid": 1, "path": "/Users/<USER>/.windsurf/extensions/ms-dotnettools.csdevkit-1.16.6-darwin-arm64", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "darwin-arm64", "publisherDisplayName": "ms-dot<PERSON><PERSON>s", "metadata": {"installedTimestamp": 1749166134985, "source": "gallery", "id": "7ad42776-8f7e-4214-bf67-4f0b518ef629", "publisherId": "d05e23de-3974-4ff0-8d47-23ee77830092", "publisherDisplayName": "ms-dot<PERSON><PERSON>s", "targetPlatform": "darwin-arm64", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 175913882}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "ms-dotnettools.csharp", "uuid": "d0bfc4ab-1d3a-4487-8782-7cf6027b4fff"}, "manifest": {"name": "csharp", "publisher": "ms-dot<PERSON><PERSON>s", "version": "2.63.32", "description": "Base language support for C#", "displayName": "C#", "author": "Microsoft Corporation", "license": "SEE LICENSE IN RuntimeLicenses/license.txt", "qna": "https://github.com/dotnet/vscode-csharp/issues", "icon": "images/csharpIcon.png", "preview": false, "bugs": {"url": "https://github.com/dotnet/vscode-csharp"}, "repository": {"type": "git", "url": "https://github.com/dotnet/vscode-csharp"}, "categories": ["Debuggers", "Programming Languages", "Linters", "Snippets"], "keywords": ["multi-root ready", ".NET", "ASP.NET", ".NET Core", "dotnet", "coreclr"], "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": false}}, "defaults": {"roslyn": "4.14.0-1.25074.7", "omniSharp": "1.39.12", "razor": "9.0.0-preview.25064.4", "razorOmnisharp": "7.0.0-preview.23363.1", "xamlTools": "17.14.35723.260"}, "main": "./dist/extension", "l10n": "./l10n", "brokeredServices": [{"moniker": {"name": "Microsoft.CodeAnalysis.LanguageClient.SolutionSnapshotProvider", "version": "0.1"}}, {"moniker": {"name": "Microsoft.VisualStudio.CSharpExtension.BuildResultService", "version": "0.1"}}], "scripts": {"vscode:prepublish": "tsc -p ./ && webpack --mode production && gulp signJs", "l10nDevGenerateLocalizationBundle": "npx @vscode/l10n-dev export --outDir ./l10n ./src", "compile": "tsc -p ./ && npx eslint ./ && npm run l10nDevGenerateLocalizationBundle && npm run compile:razorTextMate", "compile:razorTextMate": "npx js-yaml src/razor/syntaxes/aspnetcorerazor.tmLanguage.yml > src/razor/syntaxes/aspnetcorerazor.tmLanguage.json", "compileDev": "tsc -p ./ && npx eslint ./ && webpack --mode development && npm run l10nDevGenerateLocalizationBundle", "compileTest": "tsc -p ./ && webpack --mode development", "watch": "tsc -watch -p ./", "test": "tsc -p ./ && gulp test", "test:unit": "tsc -p ./ && gulp test:unit", "test:withoutDevKit": "tsc -p ./ && gulp test:withoutDevKit", "test:integration:devkit": "tsc -p ./ && gulp test:integration:devkit", "test:razor": "tsc -p ./ && npm run compile:razorTextMate && gulp test:razor", "test:razorintegration": "tsc -p ./ && gulp test:razorintegration", "test:artifacts": "tsc -p ./ && gulp test:artifacts", "omnisharptest": "tsc -p ./ && gulp omnisharptest", "omnisharptest:unit": "tsc -p ./ && gulp omnisharptest:unit", "omnisharptest:integration": "tsc -p ./ && gulp omnisharptest:integration", "omnisharptest:integration:singleCsproj": "tsc -p ./ && gulp omnisharptest:integration:singleCsproj", "omnisharptest:integration:slnWithCsproj": "tsc -p ./ && gulp omnisharptest:integration:slnWithCsproj", "omnisharptest:integration:slnFilterWithCsproj": "tsc -p ./ && gulp omnisharptest:integration:slnFilterWithCsproj", "unpackage:vsix": "gulp vsix:release:unpackage", "updatePackageDependencies": "gulp updatePackageDependencies", "l10nDevGenerateXlf": "npx @vscode/l10n-dev generate-xlf ./package.nls.json ./l10n/bundle.l10n.json --outFile ./loc/vscode-csharp.xlf", "l10nDevImportXlf": "npx @vscode/l10n-dev import-xlf ./loc/vscode-csharp.*.xlf --outDir ./l10n && move l10n\\package.nls.*.json ."}, "extensionDependencies": ["ms-dotnettools.vscode-dotnet-runtime"], "dependencies": {"@microsoft/servicehub-framework": "4.2.99-beta", "@octokit/rest": "^20.0.1", "@types/cross-spawn": "6.0.2", "@vscode/debugprotocol": "1.56.0", "@vscode/extension-telemetry": "^0.9.0", "@vscode/js-debug-browsers": "^1.1.0", "async-file": "2.0.2", "cross-spawn": "6.0.6", "execa": "4.0.0", "fs-extra": "9.1.0", "http-proxy-agent": "7.0.0", "https-proxy-agent": "7.0.2", "jsonc-parser": "3.0.0", "microsoft.aspnetcore.razor.vscode": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/0af42abab690d5de903a4a814d6aedc1/microsoft.aspnetcore.razor.vscode-7.0.0-preview.23363.1.tgz", "nerdbank-gitversioning": "^3.6.79-alpha", "node-machine-id": "1.1.12", "ps-list": "7.2.0", "request-light": "0.7.0", "rxjs": "6.6.7", "semver": "7.5.4", "stream": "0.0.2", "strip-bom": "5.0.0", "strip-bom-buf": "2.0.0", "tmp": "0.0.33", "uuid": "^9.0.0", "vscode-html-languageservice": "^5.0.1", "vscode-jsonrpc": "8.2.0-next.0", "vscode-languageclient": "8.2.0-next.1", "vscode-languageserver-protocol": "3.17.4-next.1", "vscode-languageserver-textdocument": "^1.0.5", "vscode-nls": "5.0.1", "yauzl": "2.10.0"}, "devDependencies": {"@jest/globals": "^29.6.2", "@types/archiver": "5.1.0", "@types/del": "3.0.1", "@types/fs-extra": "5.0.4", "@types/gulp": "4.0.5", "@types/minimist": "1.2.1", "@types/node": "20.14.8", "@types/semver": "7.3.13", "@types/tmp": "0.0.33", "@types/unzipper": "^0.9.1", "@types/uuid": "^9.0.1", "@types/vscode": "1.93.0", "@types/yauzl": "2.10.0", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "@vscode/l10n-dev": "^0.0.35", "@vscode/test-electron": "2.3.8", "@vscode/vsce": "3.0.0", "archiver": "5.3.0", "del": "3.0.0", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-header": "^3.1.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-unicorn": "^47.0.0", "find-versions": "4.0.0", "get-port": "5.1.1", "glob-promise": "4.1.0", "gulp": "5.0.0", "jest": "^29.6.2", "jest-cli": "^29.6.4", "jest-junit": "^16.0.0", "js-yaml": ">=3.13.1", "minimatch": "3.0.5", "mock-http-server": "1.4.2", "octokit": "^3.1.0", "prettier": "2.8.8", "rimraf": "2.6.3", "source-map-support": "^0.5.21", "ts-jest": "^29.1.1", "ts-loader": "9.0.0", "ts-node": "9.1.1", "typescript": "5.6.2", "unzipper": "0.10.11", "vscode-oniguruma": "^1.6.1", "vscode-textmate": "^6.0.0", "vscode-uri": "^3.0.7", "webpack": "5.94.0", "webpack-cli": "4.6.0"}, "runtimeDependencies": [{"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 4.7.2 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-win-x64-1.39.12.zip", "installPath": ".omnisharp/1.39.12", "platforms": ["win32"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.12/OmniSharp.exe", "platformId": "win-x64", "isFramework": true, "integrity": "A52562B44C9BAA2811F0A617F182A5886BB79BB1532850827D89F173D8C962B6"}, {"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 6 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-win-x64-net6.0-1.39.12.zip", "installPath": ".omnisharp/1.39.12-net6.0", "platforms": ["win32"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.12-net6.0/OmniSharp.dll", "platformId": "win-x64", "isFramework": false, "integrity": "21BB3F7D990B6D464A748E9C11731582CAEEAAB87D7F749EDEACFE136A09C13E"}, {"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 4.7.2 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-win-arm64-1.39.12.zip", "installPath": ".omnisharp/1.39.12", "platforms": ["win32"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.12/OmniSharp.exe", "platformId": "win-arm64", "isFramework": true, "integrity": "E61CFBD416C9099D5710748281A0E07763ABE6D6EC930F31335B008DB6907780"}, {"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 6 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-win-arm64-net6.0-1.39.12.zip", "installPath": ".omnisharp/1.39.12-net6.0", "platforms": ["win32"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.12-net6.0/OmniSharp.dll", "platformId": "win-arm64", "isFramework": false, "integrity": "ECA34E2AEEB1775AAB664C5C06232EFBB57BB2CA6020A63FB92D24A0EE3A6906"}, {"id": "OmniSharp", "description": "OmniSharp for OSX (Mono / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-osx-1.39.12.zip", "installPath": ".omnisharp/1.39.12", "platforms": ["darwin"], "architectures": ["x86_64", "arm64"], "binaries": ["./mono.osx", "./run"], "installTestPath": "./.omnisharp/1.39.12/run", "platformId": "osx", "isFramework": true, "integrity": "645BD54508011CEF386FC765FA9E2DD6F6615120A6F611EF78DB2C41A3634C71"}, {"id": "OmniSharp", "description": "OmniSharp for OSX (.NET 6 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-osx-x64-net6.0-1.39.12.zip", "installPath": ".omnisharp/1.39.12-net6.0", "platforms": ["darwin"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.12-net6.0/OmniSharp.dll", "platformId": "osx-x64", "isFramework": false, "integrity": "04A286EA36CE60AD1EBC83E16B114FD6663BDE840C43FD695067268A39B81CB1"}, {"id": "OmniSharp", "description": "OmniSharp for OSX (.NET 6 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-osx-arm64-net6.0-1.39.12.zip", "installPath": ".omnisharp/1.39.12-net6.0", "platforms": ["darwin"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.12-net6.0/OmniSharp.dll", "platformId": "osx-arm64", "isFramework": false, "integrity": "44A197EBB3FC1AE55209FD3A125F2EDF6327F91A0E1EFC76025B5F7787CC4F94"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (Mono / x86)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-linux-x86-1.39.12.zip", "installPath": ".omnisharp/1.39.12", "platforms": ["linux"], "architectures": ["x86", "i686"], "binaries": ["./mono.linux-x86", "./run"], "installTestPath": "./.omnisharp/1.39.12/run", "platformId": "linux-x86", "isFramework": true, "integrity": "7C5691CCA9F617FBCCAA687F3624913428F31FF3213F689E25D62E17CBC0C2DE"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (Mono / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-linux-x64-1.39.12.zip", "installPath": ".omnisharp/1.39.12", "platforms": ["linux"], "architectures": ["x86_64"], "binaries": ["./mono.linux-x86_64", "./run"], "installTestPath": "./.omnisharp/1.39.12/run", "platformId": "linux-x64", "isFramework": true, "integrity": "82997A2EC98CDC213712EE0F96FB5A0A6C0858889D26C6BD7DB2FA1269FD6CBA"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (.NET 6 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-linux-x64-net6.0-1.39.12.zip", "installPath": ".omnisharp/1.39.12-net6.0", "platforms": ["linux"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.12-net6.0/OmniSharp.dll", "platformId": "linux-x64", "isFramework": false, "integrity": "7D65424EEEC4D0C06BAEE22D37465AD9718B626D4E9E9CA0E5FB95862630C049"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (Mono / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-linux-arm64-1.39.12.zip", "installPath": ".omnisharp/1.39.12", "platforms": ["linux"], "architectures": ["arm64"], "binaries": ["./mono.linux-arm64", "./run"], "installTestPath": "./.omnisharp/1.39.12/run", "platformId": "linux-arm64", "isFramework": true, "integrity": "39478AE2072874258CC493055F7332FD9BEC7B3ED1FFF57280EB2D07CAFAE4BD"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (.NET 6 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-linux-arm64-net6.0-1.39.12.zip", "installPath": ".omnisharp/1.39.12-net6.0", "platforms": ["linux"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.12-net6.0/OmniSharp.dll", "platformId": "linux-arm64", "isFramework": false, "integrity": "1A009CF2D62D1A6FE1646FFF03FED52633B019B49A71FB3D8CE74406224EA0C5"}, {"id": "OmniSharp", "description": "OmniSharp for Linux musl (.NET 6 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-linux-musl-x64-net6.0-1.39.12.zip", "installPath": ".omnisharp/1.39.12-net6.0", "platforms": ["linux-musl"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.12-net6.0/OmniSharp.dll", "platformId": "linux-musl-x64", "isFramework": false, "integrity": "FB97F4B0AB76823272B55CA3134FD5877C5651366CB88C678ED0445E86A8CD35"}, {"id": "OmniSharp", "description": "OmniSharp for Linux musl (.NET 6 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.12/omnisharp-linux-musl-arm64-net6.0-1.39.12.zip", "installPath": ".omnisharp/1.39.12-net6.0", "platforms": ["linux-musl"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.12-net6.0/OmniSharp.dll", "platformId": "linux-musl-arm64", "isFramework": false, "integrity": "9944EBD6EE06BD595BCADD3057CD9BEF4105C3A3952DAE03E54F3114E2E6661F"}, {"id": "Debugger", "description": ".NET Core Debugger (Windows / x64)", "url": "https://vsdebugger-cyg0dxb6czfafzaz.b01.azurefd.net/coreclr-debug-2-60-0/coreclr-debug-win7-x64.zip", "installPath": ".debugger/x86_64", "platforms": ["win32"], "architectures": ["x86_64", "arm64"], "installTestPath": "./.debugger/x86_64/vsdbg-ui.exe", "integrity": "BFA8E3298DAFE59213BA2FD9938031B7A41A354415274E8238BB49D9E9381E66"}, {"id": "Debugger", "description": ".NET Core Debugger (Windows / ARM64)", "url": "https://vsdebugger-cyg0dxb6czfafzaz.b01.azurefd.net/coreclr-debug-2-60-0/coreclr-debug-win10-arm64.zip", "installPath": ".debugger/arm64", "platforms": ["win32"], "architectures": ["arm64"], "installTestPath": "./.debugger/arm64/vsdbg-ui.exe", "integrity": "299DA0ABD00CD61AA1A6293A3C103CCF3070844E4B667C2811A3AAC16D7E6E44"}, {"id": "Debugger", "description": ".NET Core Debugger (macOS / x64)", "url": "https://vsdebugger-cyg0dxb6czfafzaz.b01.azurefd.net/coreclr-debug-2-60-0/coreclr-debug-osx-x64.zip", "installPath": ".debugger/x86_64", "platforms": ["darwin"], "architectures": ["x86_64", "arm64"], "binaries": ["./vsdbg-ui", "./vsdbg"], "installTestPath": "./.debugger/x86_64/vsdbg-ui", "integrity": "5DF02935DFFD453E8C947AFC7567F4199D4BD83F582EBB20ECD23C054F8A43E2"}, {"id": "Debugger", "description": ".NET Core Debugger (macOS / arm64)", "url": "https://vsdebugger-cyg0dxb6czfafzaz.b01.azurefd.net/coreclr-debug-2-60-0/coreclr-debug-osx-arm64.zip", "installPath": ".debugger/arm64", "platforms": ["darwin"], "architectures": ["arm64"], "binaries": ["./vsdbg-ui", "./vsdbg"], "installTestPath": "./.debugger/arm64/vsdbg-ui", "integrity": "1CBC75E00B7CE32DBD2617AC59EE10A4FE1DBB5AEA6E6A94224F50DEC3C96D3E"}, {"id": "Debugger", "description": ".NET Core Debugger (linux / ARM)", "url": "https://vsdebugger-cyg0dxb6czfafzaz.b01.azurefd.net/coreclr-debug-2-60-0/coreclr-debug-linux-arm.zip", "installPath": ".debugger", "platforms": ["linux"], "architectures": ["arm"], "binaries": ["./vsdbg-ui", "./vsdbg"], "installTestPath": "./.debugger/vsdbg-ui", "integrity": "AE7B3967783B87448BAC4C78074B1D6F6AD4B70981FBB202CC9150A46405D287"}, {"id": "Debugger", "description": ".NET Core Debugger (linux / ARM64)", "url": "https://vsdebugger-cyg0dxb6czfafzaz.b01.azurefd.net/coreclr-debug-2-60-0/coreclr-debug-linux-arm64.zip", "installPath": ".debugger", "platforms": ["linux"], "architectures": ["arm64"], "binaries": ["./vsdbg-ui", "./vsdbg"], "installTestPath": "./.debugger/vsdbg-ui", "integrity": "468C4577B8E131E47CE83C7A290DA41580C6D327B43308CA8B206D068057CCD0"}, {"id": "Debugger", "description": ".NET Core Debugger (linux musl / x64)", "url": "https://vsdebugger-cyg0dxb6czfafzaz.b01.azurefd.net/coreclr-debug-2-60-0/coreclr-debug-linux-musl-x64.zip", "installPath": ".debugger", "platforms": ["linux-musl"], "architectures": ["x86_64"], "binaries": ["./vsdbg-ui", "./vsdbg"], "installTestPath": "./.debugger/vsdbg-ui", "integrity": "B097E8D5C87B445399829DCDC32C387121E81F6F79001715645B620FC8EFD087"}, {"id": "Debugger", "description": ".NET Core Debugger (linux musl / ARM64)", "url": "https://vsdebugger-cyg0dxb6czfafzaz.b01.azurefd.net/coreclr-debug-2-60-0/coreclr-debug-linux-musl-arm64.zip", "installPath": ".debugger", "platforms": ["linux-musl"], "architectures": ["arm64"], "binaries": ["./vsdbg-ui", "./vsdbg"], "installTestPath": "./.debugger/vsdbg-ui", "integrity": "02CBCBD53377ECDFD68671D216A7CD2631CF22C60CFA66F5361A919F915D0B19"}, {"id": "Debugger", "description": ".NET Core Debugger (linux / x64)", "url": "https://vsdebugger-cyg0dxb6czfafzaz.b01.azurefd.net/coreclr-debug-2-60-0/coreclr-debug-linux-x64.zip", "installPath": ".debugger", "platforms": ["linux"], "architectures": ["x86_64"], "binaries": ["./vsdbg-ui", "./vsdbg"], "installTestPath": "./.debugger/vsdbg-ui", "integrity": "1710E20F9A7BD4F10A126B86D8F3F9F6C2859B7A7EA2E6BACCE7029ACDA57DFB"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Windows / x64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/8d42e62ea4051381c219b3e31bc4eced/razorlanguageserver-win-x64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["win32"], "architectures": ["x86_64"]}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Windows / ARM64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/4ef26e45cf32fe8d51c0e7dd21f1fef6/razorlanguageserver-win-arm64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["win32"], "architectures": ["arm64"]}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Linux / x64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/6d4e23a3c7cf0465743950a39515a716/razorlanguageserver-linux-x64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["linux"], "architectures": ["x86_64"], "binaries": ["./rzls"]}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Linux ARM64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/85deebd44647ebf65724cc291d722283/razorlanguageserver-linux-arm64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["linux"], "architectures": ["arm64"], "binaries": ["./rzls"]}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Linux musl / x64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/4f0caa94ae182785655efb15eafcef23/razorlanguageserver-linux-musl-x64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["linux-musl"], "architectures": ["x86_64"], "binaries": ["./rzls"]}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Linux musl ARM64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/0a24828206a6f3b4bc743d058ef88ce7/razorlanguageserver-linux-musl-arm64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["linux-musl"], "architectures": ["arm64"], "binaries": ["./rzls"]}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (macOS / x64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/2afcafaf41082989efcc10405abb9314/razorlanguageserver-osx-x64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["darwin"], "architectures": ["x86_64"], "binaries": ["./rzls"]}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (macOS ARM64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/8bf2ed2f00d481a5987e3eb5165afddd/razorlanguageserver-osx-arm64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["darwin"], "architectures": ["arm64"], "binaries": ["./rzls"]}], "engines": {"vscode": "^1.93.0"}, "activationEvents": ["onDebugInitialConfigurations", "onDebugResolve:blazorwasm", "onDebugResolve:coreclr", "onDebugResolve:clr", "onDebugResolve:monovsdbg", "onDebugResolve:dotnet", "onLanguage:csharp", "onCommand:o.showOutput", "onCommand:omnisharp.registerLanguageMiddleware", "workspaceContains:**/*.{csproj,csx,cake}"], "contributes": {"themes": [{"label": "Visual Studio 2019 Dark", "uiTheme": "vs-dark", "path": "./themes/vs2019_dark.json"}, {"label": "Visual Studio 2019 Light", "uiTheme": "vs", "path": "./themes/vs2019_light.json"}], "configuration": [{"title": "Project", "order": 0, "properties": {"dotnet.defaultSolution": {"type": "string", "description": "The path of the default solution to be opened in the workspace, or set to 'disable' to skip it. (Previously `omnisharp.defaultLaunchSolution`)", "order": 0}}}, {"title": "Text Editor", "order": 1, "properties": {"dotnet.autoInsert.enableAutoInsert": {"type": "boolean", "default": true, "description": "Enable automatic insertion of documentation comments."}, "dotnet.formatting.organizeImportsOnFormat": {"type": "boolean", "default": false, "description": "Specifies whether 'using' directives should be grouped and sorted during document formatting. (Previously `omnisharp.organizeImportsOnFormat`)"}, "dotnet.typeMembers.memberInsertionLocation": {"type": "string", "enum": ["withOtherMembersOfTheSameKind", "atTheEnd"], "default": "withOtherMembersOfTheSameKind", "enumDescriptions": ["Place them with other members of the same kind.", "Place them at the end."], "description": "The insertion location of properties, events, and methods When implement interface or abstract class.", "order": 10}, "dotnet.typeMembers.propertyGenerationBehavior": {"type": "string", "enum": ["preferThrowingProperties", "preferAutoProperties"], "default": "preferThrowingProperties", "enumDescriptions": ["Prefer throwing properties.", "Prefer auto properties."], "description": "Generation behavior of properties when implement interface or abstract class.", "order": 10}, "dotnet.codeLens.enableReferencesCodeLens": {"type": "boolean", "default": true, "description": "Specifies whether the references CodeLens should be shown. (Previously `csharp.referencesCodeLens.enabled`)"}, "dotnet.codeLens.enableTestsCodeLens": {"type": "boolean", "default": true, "description": "Specifies whether the run and debug test CodeLens should be shown. (Previously `csharp.testsCodeLens.enabled`)"}, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": {"type": "boolean", "default": true, "description": "Enables support for showing unimported types and unimported extension methods in completion lists. When committed, the appropriate using directive will be added at the top of the current file. (Previously `omnisharp.enableImportCompletion`)", "order": 20}, "dotnet.completion.showNameCompletionSuggestions": {"type": "boolean", "default": "true", "description": "Perform automatic object name completion for the members that you have recently selected.", "order": 20}, "dotnet.completion.provideRegexCompletions": {"type": "boolean", "default": "true", "description": "Show regular expressions in completion list.", "order": 20}, "dotnet.completion.triggerCompletionInArgumentLists": {"type": "boolean", "default": "true", "description": "Automatically show completion list in argument lists", "order": 20}, "dotnet.backgroundAnalysis.analyzerDiagnosticsScope": {"type": "string", "enum": ["openFiles", "fullSolution", "none"], "default": "openFiles", "enumDescriptions": ["Open documents", "Entire solution", "None"], "description": "Run background code analysis for: (Previously `omnisharp.enableRoslynAnalyzers`)", "order": 30}, "dotnet.backgroundAnalysis.compilerDiagnosticsScope": {"type": "string", "enum": ["openFiles", "fullSolution", "none"], "default": "openFiles", "enumDescriptions": ["Open documents", "Entire solution", "None"], "description": "Show compiler errors and warnings for:", "order": 30}, "dotnet.highlighting.highlightRelatedRegexComponents": {"type": "boolean", "default": "true", "description": "Highlight related regular expression components under cursor.", "order": 40}, "dotnet.highlighting.highlightRelatedJsonComponents": {"type": "boolean", "default": "true", "description": "Highlight related JSON components under cursor.", "order": 40}, "csharp.inlayHints.enableInlayHintsForImplicitObjectCreation": {"type": "boolean", "default": false, "description": "Show hints for implicit object creation", "order": 50}, "csharp.inlayHints.enableInlayHintsForImplicitVariableTypes": {"type": "boolean", "default": false, "description": "Show hints for variables with inferred types", "order": 50}, "csharp.inlayHints.enableInlayHintsForLambdaParameterTypes": {"type": "boolean", "default": false, "description": "Show hints for lambda parameter types", "order": 50}, "csharp.inlayHints.enableInlayHintsForTypes": {"type": "boolean", "default": false, "description": "Display inline type hints", "order": 50}, "dotnet.inlayHints.enableInlayHintsForIndexerParameters": {"type": "boolean", "default": false, "description": "Show hints for indexers", "order": 50}, "dotnet.inlayHints.enableInlayHintsForLiteralParameters": {"type": "boolean", "default": false, "description": "Show hints for literals", "order": 50}, "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": {"type": "boolean", "default": false, "description": "Show hints for 'new' expressions", "order": 50}, "dotnet.inlayHints.enableInlayHintsForOtherParameters": {"type": "boolean", "default": false, "description": "Show hints for everything else", "order": 50}, "dotnet.inlayHints.enableInlayHintsForParameters": {"type": "boolean", "default": false, "description": "Display inline parameter name hints", "order": 50}, "dotnet.inlayHints.suppressInlayHintsForParametersThatDifferOnlyBySuffix": {"type": "boolean", "default": false, "description": "Suppress hints when parameter names differ only by suffix", "order": 50}, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchArgumentName": {"type": "boolean", "default": false, "description": "Suppress hints when argument matches parameter name", "order": 50}, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchMethodIntent": {"type": "boolean", "default": false, "description": "Suppress hints when parameter name matches the method's intent", "order": 50}, "dotnet.navigation.navigateToDecompiledSources": {"type": "boolean", "default": "true", "description": "Enable navigation to decomplied sources.", "order": 60}, "dotnet.navigation.navigateToSourceLinkAndEmbeddedSources": {"type": "boolean", "default": "true", "description": "Enable navigation to source link and embedded sources.", "order": 60}, "dotnet.quickInfo.showRemarksInQuickInfo": {"type": "boolean", "default": "true", "description": "Show remarks information when display symbol.", "order": 70}, "dotnet.symbolSearch.searchReferenceAssemblies": {"type": "boolean", "default": true, "description": "Search symbols in reference assemblies. It affects features requires symbol searching, such as add imports.", "order": 80}}}, {"title": "Debugger", "order": 8, "properties": {"csharp.debug.stopAtEntry": {"type": "boolean", "markdownDescription": "If true, the debugger should stop at the entry point of the target. This option defaults to `false`.", "default": false}, "csharp.debug.console": {"type": "string", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["The target process's console input (stdin) and output (stdout/stderr) are routed through the VS Code Debug Console.", "The target process will run inside VS Code's integrated terminal.", "The target process will run inside its own external terminal. When using this mode, you will need to switch focus between Visual Studio Code and the external terminal window."], "markdownDescription": "**Note:** _This option is only used for console projects launched with the `dotnet` debug configuration type_.\n\nIndicates which console the target program should be launched into. See https://aka.ms/VSCode-CS-LaunchJson-Console for more information.", "default": "internalConsole"}, "csharp.debug.sourceFileMap": {"type": "object", "markdownDescription": "Maps build-time paths to local source locations. All instances of build-time path will be replaced with the local source path.\n\nExample:\n\n`{\"<build-path>\":\"<local-source-path>\"}`", "additionalProperties": {"type": "string"}, "default": {}}, "csharp.debug.justMyCode": {"type": "boolean", "markdownDescription": "When enabled (the default), the debugger only displays and steps into user code (\"My Code\"), ignoring system code and other code that is optimized or that does not have debugging symbols. [More information](https://aka.ms/VSCode-CS-LaunchJson-JustMyCode)", "default": true}, "csharp.debug.requireExactSource": {"type": "boolean", "markdownDescription": "Flag to require current source code to match the pdb. This option defaults to `true`.", "default": true}, "csharp.debug.enableStepFiltering": {"type": "boolean", "markdownDescription": "Flag to enable stepping over Properties and Operators. This option defaults to `true`.", "default": true}, "csharp.debug.logging.exceptions": {"type": "boolean", "markdownDescription": "Flag to determine whether exception messages should be logged to the output window. This option defaults to `true`.", "default": true}, "csharp.debug.logging.moduleLoad": {"type": "boolean", "markdownDescription": "Flag to determine whether module load events should be logged to the output window. This option defaults to `true`.", "default": true}, "csharp.debug.logging.programOutput": {"type": "boolean", "markdownDescription": "Flag to determine whether program output should be logged to the output window when not using an external console. This option defaults to `true`.", "default": true}, "csharp.debug.logging.browserStdOut": {"type": "boolean", "markdownDescription": "Flag to determine if stdout text from the launching the web browser should be logged to the output window. This option defaults to `true`.", "default": true}, "csharp.debug.logging.elapsedTiming": {"type": "boolean", "markdownDescription": "If true, protocol message logging will include `adapterElapsedTime` and `engineElapsedTime` properties to indicate the amount of time, in microseconds, that a request took. This option defaults to `false`.", "default": false}, "csharp.debug.logging.threadExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when a thread in the target process exits. This option defaults to `false`.", "default": false}, "csharp.debug.logging.processExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when the target process exits, or debugging is stopped. This option defaults to `true`.", "default": true}, "csharp.debug.logging.engineLogging": {"type": "boolean", "deprecationMessage": "The setting 'logging.engineLogging' has been deprecated in favor of 'logging.diagnosticsLog.protocolMessages'.", "default": false}, "csharp.debug.logging.diagnosticsLog.protocolMessages": {"type": "boolean", "markdownDescription": "Flag to determine whether DAP protocol messages exchanged between the C# debugger and the UI should be logged to the output window. This option defaults to `false`.", "default": false}, "csharp.debug.logging.diagnosticsLog.dispatcherMessages": {"type": "string", "enum": ["none", "error", "important", "normal"], "enumDescriptions": ["Do not print additional diagnostic messages.", "Print error-level diagnostic messages.", "Print important diagnostic messages.", "Print all non-verbose diagnostic messages."], "markdownDescription": "Controls which messages are printed to the output window from the debugger's dispatcher. If not specified, this will default to `none` unless one of the verbose log settings are enabled (`debugEngineAPITracing`, `debugRuntimeEventTracing`, `expressionEvaluationTracing` or `startDebuggingTracing`), in which case the default changes to `normal`.", "default": "none"}, "csharp.debug.logging.diagnosticsLog.debugEngineAPITracing": {"type": "string", "enum": ["none", "error", "all"], "enumDescriptions": ["Disable tracing API calls", "Print failures from debugger API calls.", "Print all debugger API calls. This is very verbose."], "markdownDescription": "Controls if API calls to Microsoft.VisualStudio.Debugger.Engine/vsdebugeng.h should be printed to the output window. This option defaults to `none`.", "default": "none"}, "csharp.debug.logging.diagnosticsLog.debugRuntimeEventTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for events raised by the underlying runtime should be enabled. This option defaults to `false`.", "default": false}, "csharp.debug.logging.diagnosticsLog.expressionEvaluationTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for expression evaluation should be enabled. This option defaults to `false`.", "default": false}, "csharp.debug.logging.diagnosticsLog.startDebuggingTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for start debugging should be enabled. This option defaults to `false`.", "default": false}, "csharp.debug.logging.consoleUsageMessage": {"type": "boolean", "description": "Controls if a message is logged when the target process calls a 'Console.Read*' API and stdin is redirected to the console.", "default": true}, "csharp.debug.suppressJITOptimizations": {"type": "boolean", "markdownDescription": "If true, when an optimized module (.dll compiled in the Release configuration) loads in the target process, the debugger will ask the Just-In-Time compiler to generate code with optimizations disabled. [More information](https://aka.ms/VSCode-CS-LaunchJson-SuppressJITOptimizations)", "default": false}, "csharp.debug.symbolOptions.searchPaths": {"type": "array", "items": {"type": "string"}, "description": "Array of symbol server URLs (example: http​://MyExampleSymbolServer) or directories (example: /build/symbols) to search for .pdb files. These directories will be searched in addition to the default locations -- next to the module and the path where the pdb was originally dropped to.", "default": []}, "csharp.debug.symbolOptions.searchMicrosoftSymbolServer": {"type": "boolean", "description": "If 'true' the Microsoft Symbol server (https​://msdl.microsoft.com​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "csharp.debug.symbolOptions.searchNuGetOrgSymbolServer": {"type": "boolean", "description": "If 'true' the NuGet.org symbol server (https​://symbols.nuget.org​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "csharp.debug.symbolOptions.cachePath": {"type": "string", "description": "Directory where symbols downloaded from symbol servers should be cached. If unspecified, on Windows the debugger will default to %TEMP%\\SymbolCache, and on Linux and macOS the debugger will default to ~/.dotnet/symbolcache.", "default": ""}, "csharp.debug.symbolOptions.moduleFilter.mode": {"type": "string", "enum": ["loadAllButExcluded", "loadOnlyIncluded"], "enumDescriptions": ["Load symbols for all modules unless the module is in the 'excludedModules' array.", "Do not attempt to load symbols for ANY module unless it is in the 'includedModules' array, or it is included through the 'includeSymbolsNextToModules' setting."], "description": "Controls which of the two basic operating modes the module filter operates in.", "default": "loadAllButExcluded"}, "csharp.debug.symbolOptions.moduleFilter.excludedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should NOT load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadAllButExcluded'.", "default": []}, "csharp.debug.symbolOptions.moduleFilter.includedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": []}, "csharp.debug.symbolOptions.moduleFilter.includeSymbolsNextToModules": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will still check next to the module itself and the launching executable, but it will not check paths on the symbol search list. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}, "csharp.debug.symbolOptions.moduleFilter.includeSymbolsOnDemand": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will attempt to download symbols when it detects symbols are needed, such as when trying to step into the module. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}, "csharp.debug.expressionEvaluationOptions.allowImplicitFuncEval": {"type": "boolean", "description": "When true (the default state), the debugger will automatically call property `get` methods and other implicit function calls.", "default": true}, "csharp.debug.expressionEvaluationOptions.allowToString": {"type": "boolean", "markdownDescription": "When true (the default state), the debugger will automatically call `ToString` to format objects. This option has no effect if `allowImplicitFuncEval` is `false`.", "default": true}, "csharp.debug.expressionEvaluationOptions.allowFastEvaluate": {"type": "boolean", "description": "When true (the default state), the debugger will attempt faster evaluation by simulating execution of simple properties and methods.", "default": true}, "csharp.debug.expressionEvaluationOptions.showRawValues": {"type": "boolean", "description": "When true, the debugger will show raw structure of objects in variables windows.", "default": false}, "dotnet.unitTestDebuggingOptions": {"type": "object", "description": "Options to use with the debugger when launching for unit test debugging. (Previously `csharp.unitTestDebuggingOptions`)", "default": {}, "properties": {"sourceFileMap": {"type": "object", "markdownDescription": "Maps build-time paths to local source locations. All instances of build-time path will be replaced with the local source path.\n\nExample:\n\n`{\"<build-path>\":\"<local-source-path>\"}`", "additionalProperties": {"type": "string"}}, "justMyCode": {"type": "boolean", "markdownDescription": "When enabled (the default), the debugger only displays and steps into user code (\"My Code\"), ignoring system code and other code that is optimized or that does not have debugging symbols. [More information](https://aka.ms/VSCode-CS-LaunchJson-JustMyCode)", "default": true}, "requireExactSource": {"type": "boolean", "markdownDescription": "Flag to require current source code to match the pdb. This option defaults to `true`.", "default": true}, "enableStepFiltering": {"type": "boolean", "markdownDescription": "Flag to enable stepping over Properties and Operators. This option defaults to `true`.", "default": true}, "logging": {"description": "Flags to determine what types of messages should be logged to the output window.", "type": "object", "required": [], "default": {}, "properties": {"exceptions": {"type": "boolean", "markdownDescription": "Flag to determine whether exception messages should be logged to the output window. This option defaults to `true`.", "default": true}, "moduleLoad": {"type": "boolean", "markdownDescription": "Flag to determine whether module load events should be logged to the output window. This option defaults to `true`.", "default": true}, "programOutput": {"type": "boolean", "markdownDescription": "Flag to determine whether program output should be logged to the output window when not using an external console. This option defaults to `true`.", "default": true}, "threadExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when a thread in the target process exits. This option defaults to `false`.", "default": false}, "processExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when the target process exits, or debugging is stopped. This option defaults to `true`.", "default": true}}}, "suppressJITOptimizations": {"type": "boolean", "markdownDescription": "If true, when an optimized module (.dll compiled in the Release configuration) loads in the target process, the debugger will ask the Just-In-Time compiler to generate code with optimizations disabled. [More information](https://aka.ms/VSCode-CS-LaunchJson-SuppressJITOptimizations)", "default": false}, "symbolOptions": {"description": "Options to control how symbols (.pdb files) are found and loaded.", "default": {"searchPaths": [], "searchMicrosoftSymbolServer": false, "searchNuGetOrgSymbolServer": false}, "type": "object", "properties": {"searchPaths": {"type": "array", "items": {"type": "string"}, "description": "Array of symbol server URLs (example: http​://MyExampleSymbolServer) or directories (example: /build/symbols) to search for .pdb files. These directories will be searched in addition to the default locations -- next to the module and the path where the pdb was originally dropped to.", "default": []}, "searchMicrosoftSymbolServer": {"type": "boolean", "description": "If 'true' the Microsoft Symbol server (https​://msdl.microsoft.com​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "searchNuGetOrgSymbolServer": {"type": "boolean", "description": "If 'true' the NuGet.org symbol server (https​://symbols.nuget.org​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "cachePath": {"type": "string", "description": "Directory where symbols downloaded from symbol servers should be cached. If unspecified, on Windows the debugger will default to %TEMP%\\SymbolCache, and on Linux and macOS the debugger will default to ~/.dotnet/symbolcache.", "default": ""}, "moduleFilter": {"description": "Provides options to control which modules (.dll files) the debugger will attempt to load symbols (.pdb files) for.", "default": {"mode": "loadAllButExcluded", "excludedModules": []}, "type": "object", "required": ["mode"], "properties": {"mode": {"type": "string", "enum": ["loadAllButExcluded", "loadOnlyIncluded"], "enumDescriptions": ["Load symbols for all modules unless the module is in the 'excludedModules' array.", "Do not attempt to load symbols for ANY module unless it is in the 'includedModules' array, or it is included through the 'includeSymbolsNextToModules' setting."], "description": "Controls which of the two basic operating modes the module filter operates in.", "default": "loadAllButExcluded"}, "excludedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should NOT load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadAllButExcluded'.", "default": []}, "includedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": []}, "includeSymbolsNextToModules": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will still check next to the module itself and the launching executable, but it will not check paths on the symbol search list. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}, "includeSymbolsOnDemand": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will attempt to download symbols when it detects symbols are needed, such as when trying to step into the module. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}}}}}, "sourceLinkOptions": {"markdownDescription": "Options to control how Source Link connects to web servers. [More information](https://aka.ms/VSCode-DotNet-SourceLink)", "default": {"*": {"enabled": true}}, "type": "object", "additionalItems": {"type": "object", "properties": {"enabled": {"title": "boolean", "markdownDescription": "Is Source Link enabled for this URL? If unspecified, this option defaults to `true`.", "default": true}}}}, "expressionEvaluationOptions": {"description": "Options to control how the debugger evaluates expressions in data tips, the debug view's 'Watch' and 'Variables' sections, or in the Debug Console.", "default": {}, "type": "object", "properties": {"allowImplicitFuncEval": {"type": "boolean", "description": "When true (the default state), the debugger will automatically call property `get` methods and other implicit function calls.", "default": true}, "allowToString": {"type": "boolean", "markdownDescription": "When true (the default state), the debugger will automatically call `ToString` to format objects. This option has no effect if `allowImplicitFuncEval` is `false`.", "default": true}, "allowFastEvaluate": {"type": "boolean", "description": "When true (the default state), the debugger will attempt faster evaluation by simulating execution of simple properties and methods.", "default": true}, "showRawValues": {"type": "boolean", "description": "When true, the debugger will show raw structure of objects in variables windows.", "default": false}}}, "targetArchitecture": {"type": "string", "markdownDescription": "[Only supported in local macOS debugging]\n\nThe architecture of the debuggee. This will automatically be detected unless this parameter is set. Allowed values are `x86_64` or `arm64`.", "enum": ["x86_64", "arm64"]}, "type": {"type": "string", "enum": ["coreclr", "clr"], "markdownDescription": "Type type of code to debug. Can be either `coreclr` for .NET Core debugging, or `clr` for Desktop .NET Framework. `clr` only works on Windows as the Desktop framework is Windows-only.", "default": "coreclr"}, "debugServer": {"type": "number", "description": "For debug extension development only: if a port is specified VS Code tries to connect to a debug adapter running in server mode", "default": 4711}}}, "dotnet.unitTests.runSettingsPath": {"type": "string", "markdownDescription": "Path to the .runsettings file which should be used when running unit tests. (Previously `omnisharp.testRunSettings`)"}}}, {"title": "LSP Server", "order": 9, "properties": {"dotnet.preferCSharpExtension": {"scope": "resource", "type": "boolean", "default": false, "description": "Forces projects to load with the C# extension only.  This can be useful when using legacy project types that are not supported by C# Dev Kit. (Requires window reload)"}, "dotnet.server.path": {"type": "string", "scope": "machine-overridable", "description": "Specifies the absolute path to the server (LSP or O#) executable. When left empty the version pinned to the C# Extension is used. (Previously `omnisharp.path`)"}, "dotnet.server.componentPaths": {"type": "object", "description": "Allows overriding the folder path for built in components of the language server (for example, override the .roslynDevKit path in the extension directory to use locally built components)", "properties": {"roslynDevKit": {"description": "Overrides the folder path for the .roslynDevKit component of the language server", "type": "string"}, "xamlTools": {"description": "Overrides the folder path for the .xamlTools component of the language server", "type": "string"}}, "default": {}}, "dotnet.server.startTimeout": {"type": "number", "scope": "machine-overridable", "default": 120000, "description": "Specifies a timeout (in ms) for the client to successfully start and connect to the language server."}, "dotnet.server.waitForDebugger": {"type": "boolean", "scope": "machine-overridable", "default": false, "description": "Passes the --debug flag when launching the server to allow a debugger to be attached. (Previously `omnisharp.waitForDebugger`)"}, "dotnet.server.extensionPaths": {"scope": "machine-overridable", "type": ["array", null], "items": {"type": "string"}, "default": null, "description": "Override for path to language server --extension arguments"}, "dotnet.server.crashDumpPath": {"scope": "machine-overridable", "type": "string", "default": null, "description": "Sets a folder path where crash dumps are written to if the language server crashes.  Must be writeable by the user."}, "dotnet.server.suppressLspErrorToasts": {"type": "boolean", "default": false, "description": "Suppresses error toasts from showing up if the server encounters a recoverable error."}, "dotnet.server.suppressMiscellaneousFilesToasts": {"type": "boolean", "default": false, "description": "Suppress warning toasts from showing up if the active document is outside the open workspace."}, "dotnet.server.useServerGC": {"type": "boolean", "default": true, "description": "Configure the language server to use .NET server garbage collection.  Server garbage collection generally provides better performance at the expensive of higher memory consumption."}, "dotnet.enableXamlTools": {"scope": "machine-overridable", "type": "boolean", "default": true, "description": "Enables XAML tools when using C# Dev Kit"}, "dotnet.projects.binaryLogPath": {"scope": "machine-overridable", "type": "string", "default": null, "description": "Sets a path where MSBuild binary logs are written to when loading projects, to help diagnose loading errors."}, "dotnet.projects.enableAutomaticRestore": {"type": "boolean", "default": true, "description": "Enables automatic NuGet restore if the extension detects assets are missing."}, "razor.languageServer.directory": {"type": "string", "scope": "machine-overridable", "description": "Overrides the path to the Razor Language Server directory.", "order": 90}, "razor.languageServer.debug": {"type": "boolean", "scope": "machine-overridable", "default": false, "description": "Specifies whether to wait for debug attach when launching the language server.", "order": 90}, "razor.languageServer.forceRuntimeCodeGeneration": {"type": "boolean", "scope": "machine-overridable", "default": null, "description": "(EXPERIMENTAL) Enable combined design time/runtime code generation for Razor files", "order": 90}, "razor.languageServer.suppressLspErrorToasts": {"type": "boolean", "default": true, "description": "Suppresses error toasts from showing up if the server encounters a recoverable error."}, "razor.languageServer.useNewFormattingEngine": {"type": "boolean", "default": true, "description": "Use the new Razor formatting engine."}}}, {"title": "OmniSharp", "order": 10, "properties": {"dotnet.server.useOmnisharp": {"type": "boolean", "default": false, "description": "Switches to use the Omnisharp server for language features when enabled (requires restart). This option will not be honored with C# Dev Kit installed.", "order": 0}, "omnisharp.dotnetPath": {"type": "string", "scope": "machine-overridable", "description": "Specifies the path to a dotnet installation directory to use instead of the default system one. This only influences the dotnet installation to use for hosting the OmniSharp server itself. Example: \"/home/<USER>/mycustomdotnetdirectory\"."}, "csharp.format.enable": {"type": "boolean", "default": true, "description": "Enable/disable default C# formatter (requires restart)."}, "csharp.suppressDotnetInstallWarning": {"type": "boolean", "default": false, "description": "Suppress the warning that the .NET Core SDK is not on the path."}, "csharp.suppressDotnetRestoreNotification": {"type": "boolean", "default": false, "description": "Suppress the notification window to perform a 'dotnet restore' when dependencies can't be resolved."}, "csharp.suppressProjectJsonWarning": {"type": "boolean", "default": false, "description": "Suppress the warning that project.json is no longer a supported project format for .NET Core applications"}, "csharp.suppressBuildAssetsNotification": {"type": "boolean", "default": false, "description": "Suppress the notification window to add missing assets to build or debug the application."}, "csharp.suppressHiddenDiagnostics": {"type": "boolean", "default": true, "description": "Suppress 'hidden' diagnostics (such as 'unnecessary using directives') from appearing in the editor or the Problems pane."}, "csharp.referencesCodeLens.filteredSymbols": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Array of custom symbol names for which CodeLens should be disabled."}, "csharp.maxProjectFileCountForDiagnosticAnalysis": {"type": "number", "default": 1000, "description": "Specifies the maximum number of files for which diagnostics are reported for the whole workspace. If this limit is exceeded, diagnostics will be shown for currently opened files only. Specify 0 or less to disable the limit completely."}, "csharp.semanticHighlighting.enabled": {"type": "boolean", "default": true, "description": "Enable/disable Semantic Highlighting for C# files (Razor files currently unsupported). Defaults to false. Close open files for changes to take effect.", "scope": "window"}, "csharp.showOmnisharpLogOnError": {"type": "boolean", "default": true, "description": "Shows the OmniSharp log in the Output pane when OmniSharp reports an error."}, "omnisharp.useModernNet": {"type": "boolean", "default": true, "scope": "window", "title": "Use .NET 6 build of OmniSharp", "description": "Use OmniSharp build for .NET 6. This version _does not_ support non-SDK-style .NET Framework projects, including Unity. SDK-style Framework, .NET Core, and .NET 5+ projects should see significant performance improvements."}, "omnisharp.sdkPath": {"type": "string", "scope": "window", "description": "Specifies the path to a .NET SDK installation to use for project loading instead of the highest version installed. Applies when \"useModernNet\" is set to true. Example: /home/<USER>/dotnet/sdks/6.0.300."}, "omnisharp.sdkVersion": {"type": "string", "scope": "window", "description": "Specifies the version of the .NET SDK to use for project loading instead of the highest version installed. Applies when \"useModernNet\" is set to true. Example: 6.0.300."}, "omnisharp.sdkIncludePrereleases": {"type": "boolean", "scope": "window", "default": true, "description": "Specifies whether to include preview versions of the .NET SDK when determining which version to use for project loading. Applies when \"useModernNet\" is set to true."}, "omnisharp.monoPath": {"type": "string", "scope": "machine", "description": "Specifies the path to a mono installation to use when \"useModernNet\" is set to false, instead of the default system one. Example: \"/Library/Frameworks/Mono.framework/Versions/Current\""}, "omnisharp.loggingLevel": {"type": "string", "default": "information", "enum": ["trace", "debug", "information", "warning", "error", "critical"], "description": "Specifies the level of logging output from the OmniSharp server."}, "omnisharp.autoStart": {"type": "boolean", "default": true, "description": "Specifies whether the OmniSharp server will be automatically started or not. If false, OmniSharp can be started with the 'Restart OmniSharp' command"}, "omnisharp.projectFilesExcludePattern": {"type": "string", "default": "**/node_modules/**,**/.git/**,**/bower_components/**", "description": "The exclude pattern used by OmniSharp to find all project files."}, "omnisharp.projectLoadTimeout": {"type": "number", "default": 60, "description": "The time Visual Studio Code will wait for the OmniSharp server to start. Time is expressed in seconds."}, "omnisharp.maxProjectResults": {"type": "number", "default": 250, "description": "The maximum number of projects to be shown in the 'Select Project' dropdown (maximum 250)."}, "omnisharp.useEditorFormattingSettings": {"type": "boolean", "default": true, "description": "Specifes whether OmniSharp should use VS Code editor settings for C# code formatting (use of tabs, indentation size)."}, "omnisharp.minFindSymbolsFilterLength": {"type": "number", "default": 0, "description": "The minimum number of characters to enter before 'Go to Symbol in Workspace' operation shows any results."}, "omnisharp.maxFindSymbolsItems": {"type": "number", "default": 1000, "description": "The maximum number of items that 'Go to Symbol in Workspace' operation can show. The limit is applied only when a positive number is specified here."}, "omnisharp.disableMSBuildDiagnosticWarning": {"type": "boolean", "default": false, "description": "Specifies whether notifications should be shown if OmniSharp encounters warnings or errors loading a project. Note that these warnings/errors are always emitted to the OmniSharp log"}, "omnisharp.enableMsBuildLoadProjectsOnDemand": {"type": "boolean", "default": false, "description": "If true, MSBuild project system will only load projects for files that were opened in the editor. This setting is useful for big C# codebases and allows for faster initialization of code navigation features only for projects that are relevant to code that is being edited. With this setting enabled OmniSharp may load fewer projects and may thus display incomplete reference lists for symbols."}, "omnisharp.enableEditorConfigSupport": {"type": "boolean", "default": true, "description": "Enables support for reading code style, naming convention and analyzer settings from .editorconfig."}, "omnisharp.enableDecompilationSupport": {"type": "boolean", "default": false, "scope": "machine", "description": "Enables support for decompiling external references instead of viewing metadata."}, "omnisharp.enableLspDriver": {"type": "boolean", "default": false, "description": "Enables support for the experimental language protocol based engine (requires reload to setup bindings correctly)"}, "omnisharp.enableAsyncCompletion": {"type": "boolean", "default": false, "description": "(EXPERIMENTAL) Enables support for resolving completion edits asynchronously. This can speed up time to show the completion list, particularly override and partial method completion lists, at the cost of slight delays after inserting a completion item. Most completion items will have no noticeable impact with this feature, but typing immediately after inserting an override or partial method completion, before the insert is completed, can have unpredictable results."}, "omnisharp.dotNetCliPaths": {"type": "array", "items": {"type": "string"}, "description": "Paths to a local download of the .NET CLI to use for running any user code.", "uniqueItems": true}, "razor.plugin.path": {"type": "string", "scope": "machine", "description": "Overrides the path to the Razor plugin dll."}, "razor.devmode": {"type": "boolean", "default": false, "description": "Forces the extension to run in a mode that enables local Razor.VSCode development."}, "razor.format.enable": {"type": "boolean", "scope": "window", "default": true, "description": "Enable/disable default Razor formatter."}, "razor.format.codeBlockBraceOnNextLine": {"type": "boolean", "scope": "window", "default": false, "description": "Forces the open brace after an @code or @functions directive to be on the following line."}, "razor.completion.commitElementsWithSpace": {"type": "boolean", "scope": "window", "default": false, "description": "Specifies whether to commit tag helper and component elements with a space."}}}], "jsonValidation": [{"fileMatch": ["appsettings.json", "appsettings.*.json"], "url": "https://json.schemastore.org/appsettings"}, {"fileMatch": "omnisharp.json", "url": "http://json.schemastore.org/omnisharp"}, {"fileMatch": "global.json", "url": "http://json.schemastore.org/global"}, {"fileMatch": "launchSettings.json", "url": "https://json.schemastore.org/launchsettings.json"}], "commands": [{"command": "o.restart", "title": "<PERSON><PERSON>", "category": "OmniSharp", "enablement": "dotnet.server.activationContext == 'OmniSharp'"}, {"command": "o.pickProjectAndStart", "title": "Select Project", "category": "OmniSharp", "enablement": "dotnet.server.activationContext == 'OmniSharp'"}, {"command": "dotnet.openSolution", "title": "Open Solution", "category": ".NET", "enablement": "dotnet.server.activationContext == 'Roslyn'"}, {"command": "o.fixAll.solution", "title": "Fix all occurrences of a code issue within solution", "category": "OmniSharp", "enablement": "dotnet.server.activationContext == 'OmniSharp'"}, {"command": "o.fixAll.project", "title": "Fix all occurrences of a code issue within project", "category": "OmniSharp", "enablement": "dotnet.server.activationContext == 'OmniSharp'"}, {"command": "o.fixAll.document", "title": "Fix all occurrences of a code issue within document", "category": "OmniSharp", "enablement": "dotnet.server.activationContext == 'OmniSharp'"}, {"command": "o.reanalyze.allProjects", "title": "Analyze all projects", "category": "OmniSharp", "enablement": "dotnet.server.activationContext == 'OmniSharp'"}, {"command": "o.reanalyze.currentProject", "title": "Analyze current project", "category": "OmniSharp", "enablement": "dotnet.server.activationContext == 'OmniSharp'"}, {"command": "dotnet.generateAssets", "title": "Generate Assets for Build and Debug", "category": ".NET"}, {"command": "dotnet.restore.project", "title": "Restore Project", "category": ".NET", "enablement": "dotnet.server.activationContext == 'Roslyn' || dotnet.server.activationContext == 'OmniSharp'"}, {"command": "dotnet.restore.all", "title": "Restore All Projects", "category": ".NET", "enablement": "dotnet.server.activationContext == 'Roslyn' || dotnet.server.activationContext == 'OmniSharp'"}, {"command": "csharp.listProcess", "title": "List process for attach", "category": "CSharp"}, {"command": "csharp.listRemoteProcess", "title": "List processes on remote connection for attach", "category": "CSharp"}, {"command": "csharp.listRemoteDockerProcess", "title": "List processes on Docker connection", "category": "CSharp"}, {"command": "csharp.attachToProcess", "title": "Attach to a .NET 5+ or .NET Core process", "category": "Debug"}, {"command": "csharp.reportIssue", "title": "Report an issue", "category": "CSharp"}, {"command": "csharp.showDecompilationTerms", "title": "Show the decompiler terms agreement", "category": "CSharp", "enablement": "dotnet.server.activationContext == 'OmniSharp'"}, {"command": "extension.showRazorCSharpWindow", "title": "Show Razor CSharp", "category": "Razor"}, {"command": "extension.showRazorHtmlWindow", "title": "Show Razor Html", "category": "Razor"}, {"command": "razor.reportIssue", "title": "Report a Razor issue", "category": "Razor"}, {"command": "dotnet.test.runTestsInContext", "title": "Run Tests in Context", "category": ".NET", "enablement": "dotnet.server.activationContext == 'Roslyn' || dotnet.server.activationContext == 'OmniSharp'"}, {"command": "dotnet.test.debugTestsInContext", "title": "Debug Tests in Context", "category": ".NET", "enablement": "dotnet.server.activationContext == 'Roslyn' || dotnet.server.activationContext == 'OmniSharp'"}, {"command": "dotnet.restartServer", "title": "Restart Language Server", "category": ".NET", "enablement": "dotnet.server.activationContext == 'Roslyn'"}], "keybindings": [{"command": "o.showOutput", "key": "Ctrl+Shift+F9", "mac": "Cmd+Shift+F9"}], "snippets": [{"language": "csharp", "path": "./snippets/csharp.json"}], "breakpoints": [{"language": "csharp"}, {"language": "razor"}, {"language": "qsharp"}, {"language": "aspnetcorerazor"}], "debuggers": [{"type": "coreclr", "label": ".NET 5+ and .NET Core", "hiddenWhen": "dotnet.debug.serviceBrokerAvailable", "languages": ["csharp", "razor", "qsharp", "aspnetcorerazor"], "variables": {"pickProcess": "csharp.listProcess", "pickRemoteProcess": "csharp.listRemoteProcess", "pickRemoteDockerProcess": "csharp.listRemoteDockerProcess"}, "aiKey": "********************************-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"launch": {"type": "object", "required": ["program"], "properties": {"program": {"type": "string", "markdownDescription": "Path to the application dll or .NET Core host executable to launch.\nThis property normally takes the form: `${workspaceFolder}/bin/Debug/(target-framework)/(project-name.dll)`\n\nExample: `${workspaceFolder}/bin/Debug/netcoreapp1.1/MyProject.dll`\n\nWhere:\n`(target-framework)` is the framework that the debugged project is being built for. This is normally found in the project file as the `TargetFramework` property.\n\n`(project-name.dll)` is the name of debugged project's build output dll. This is normally the same as the project file name but with a '.dll' extension.", "default": "${workspaceFolder}/bin/Debug/<insert-target-framework-here>/<insert-project-name-here>.dll"}, "cwd": {"type": "string", "description": "Path to the working directory of the program being debugged. Default is the current workspace.", "default": "${workspaceFolder}"}, "args": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the program.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the program.", "default": ""}]}, "stopAtEntry": {"type": "boolean", "markdownDescription": "If true, the debugger should stop at the entry point of the target. This option defaults to `false`.", "default": false}, "launchBrowser": {"description": "Describes options to launch a web browser as part of launch", "default": {"enabled": true}, "type": "object", "required": ["enabled"], "properties": {"enabled": {"type": "boolean", "description": "Whether web browser launch is enabled. This option defaults to `true`.", "default": true}, "args": {"type": "string", "description": "The arguments to pass to the command to open the browser. This is used only if the platform-specific element (`osx`, `linux` or `windows`) doesn't specify a value for `args`. Use ${auto-detect-url} to automatically use the address the server is listening to.", "default": "${auto-detect-url}"}, "osx": {"description": "OSX-specific web launch configuration options. By default, this will start the browser using `open`.", "default": {"command": "open", "args": "${auto-detect-url}"}, "type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The executable which will start the web browser.", "default": "open"}, "args": {"type": "string", "description": "The arguments to pass to the command to open the browser. Use ${auto-detect-url} to automatically use the address the server is listening to.", "default": "${auto-detect-url}"}}}, "linux": {"description": "Linux-specific web launch configuration options. By default, this will start the browser using `xdg-open`.", "default": {"command": "xdg-open", "args": "${auto-detect-url}"}, "type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The executable which will start the web browser.", "default": "xdg-open"}, "args": {"type": "string", "description": "The arguments to pass to the command to open the browser. Use ${auto-detect-url} to automatically use the address the server is listening to.", "default": "${auto-detect-url}"}}}, "windows": {"description": "Windows-specific web launch configuration options. By default, this will start the browser using `cmd /c start`.", "default": {"command": "cmd.exe", "args": "/C start ${auto-detect-url}"}, "type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The executable which will start the web browser.", "default": "cmd.exe"}, "args": {"type": "string", "description": "The arguments to pass to the command to open the browser. Use ${auto-detect-url} to automatically use the address the server is listening to.", "default": "/C start ${auto-detect-url}"}}}}}, "env": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the program.", "default": {}}, "envFile": {"type": "string", "markdownDescription": "Environment variables passed to the program by a file. E.g. `${workspaceFolder}/.env`", "default": "${workspaceFolder}/.env"}, "console": {"type": "string", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["The target process's console input (stdin) and output (stdout/stderr) are routed through the VS Code Debug Console.", "The target process will run inside VS Code's integrated terminal.", "The target process will run inside its own external terminal. When using this mode, you will need to switch focus between Visual Studio Code and the external terminal window."], "markdownDescription": "Indicates which console the target program should be launched into. See https://aka.ms/VSCode-CS-LaunchJson-Console for more information.", "settingsDescription": "**Note:** _This option is only used for console projects launched with the `dotnet` debug configuration type_.\n\nIndicates which console the target program should be launched into. See https://aka.ms/VSCode-CS-LaunchJson-Console for more information.", "default": "internalConsole"}, "externalConsole": {"type": "boolean", "markdownDescription": "Attribute `externalConsole` is deprecated, use `console` instead. This option defaults to `false`.", "default": false}, "launchSettingsFilePath": {"type": "string", "markdownDescription": "The path to a launchSettings.json file. If this isn't set, the debugger will search in `{cwd}/Properties/launchSettings.json`.", "default": "${workspaceFolder}/Properties/launchSettings.json"}, "launchSettingsProfile": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "If specified, indicates the name of the profile in launchSettings.json to use. This is ignored if launchSettings.json is not found. launchSettings.json will be read from the path specified should be the 'launchSettingsFilePath' property, or {cwd}/Properties/launchSettings.json if that isn't set. If this is set to null or an empty string then launchSettings.json is ignored. If this value is not specified the first 'Project' profile will be used.", "default": "<insert-profile-name>"}, "sourceFileMap": {"type": "object", "markdownDescription": "Maps build-time paths to local source locations. All instances of build-time path will be replaced with the local source path.\n\nExample:\n\n`{\"<build-path>\":\"<local-source-path>\"}`", "additionalProperties": {"type": "string"}, "default": {}}, "justMyCode": {"type": "boolean", "markdownDescription": "When enabled (the default), the debugger only displays and steps into user code (\"My Code\"), ignoring system code and other code that is optimized or that does not have debugging symbols. [More information](https://aka.ms/VSCode-CS-LaunchJson-JustMyCode)", "default": true}, "requireExactSource": {"type": "boolean", "markdownDescription": "Flag to require current source code to match the pdb. This option defaults to `true`.", "default": true}, "enableStepFiltering": {"type": "boolean", "markdownDescription": "Flag to enable stepping over Properties and Operators. This option defaults to `true`.", "default": true}, "logging": {"description": "Flags to determine what types of messages should be logged to the output window.", "type": "object", "required": [], "default": {}, "properties": {"exceptions": {"type": "boolean", "markdownDescription": "Flag to determine whether exception messages should be logged to the output window. This option defaults to `true`.", "default": true}, "moduleLoad": {"type": "boolean", "markdownDescription": "Flag to determine whether module load events should be logged to the output window. This option defaults to `true`.", "default": true}, "programOutput": {"type": "boolean", "markdownDescription": "Flag to determine whether program output should be logged to the output window when not using an external console. This option defaults to `true`.", "default": true}, "browserStdOut": {"type": "boolean", "markdownDescription": "Flag to determine if stdout text from the launching the web browser should be logged to the output window. This option defaults to `true`.", "default": true}, "elapsedTiming": {"type": "boolean", "markdownDescription": "If true, protocol message logging will include `adapterElapsedTime` and `engineElapsedTime` properties to indicate the amount of time, in microseconds, that a request took. This option defaults to `false`.", "default": false}, "threadExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when a thread in the target process exits. This option defaults to `false`.", "default": false}, "processExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when the target process exits, or debugging is stopped. This option defaults to `true`.", "default": true}, "engineLogging": {"type": "boolean", "deprecationMessage": "The setting 'logging.engineLogging' has been deprecated in favor of 'logging.diagnosticsLog.protocolMessages'.", "default": false}, "diagnosticsLog": {"description": "Settings to control which messages are printed to the output window from the debugger's diagnostics log. This log is meant to help troubleshoot problems with the debugger.", "type": "object", "required": [], "default": {}, "properties": {"protocolMessages": {"type": "boolean", "markdownDescription": "Flag to determine whether DAP protocol messages exchanged between the C# debugger and the UI should be logged to the output window. This option defaults to `false`.", "default": false}, "dispatcherMessages": {"type": "string", "enum": ["none", "error", "important", "normal"], "enumDescriptions": ["Do not print additional diagnostic messages.", "Print error-level diagnostic messages.", "Print important diagnostic messages.", "Print all non-verbose diagnostic messages."], "markdownDescription": "Controls which messages are printed to the output window from the debugger's dispatcher. If not specified, this will default to `none` unless one of the verbose log settings are enabled (`debugEngineAPITracing`, `debugRuntimeEventTracing`, `expressionEvaluationTracing` or `startDebuggingTracing`), in which case the default changes to `normal`.", "default": "none"}, "debugEngineAPITracing": {"type": "string", "enum": ["none", "error", "all"], "enumDescriptions": ["Disable tracing API calls", "Print failures from debugger API calls.", "Print all debugger API calls. This is very verbose."], "markdownDescription": "Controls if API calls to Microsoft.VisualStudio.Debugger.Engine/vsdebugeng.h should be printed to the output window. This option defaults to `none`.", "default": "none"}, "debugRuntimeEventTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for events raised by the underlying runtime should be enabled. This option defaults to `false`.", "default": false}, "expressionEvaluationTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for expression evaluation should be enabled. This option defaults to `false`.", "default": false}, "startDebuggingTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for start debugging should be enabled. This option defaults to `false`.", "default": false}}}, "consoleUsageMessage": {"type": "boolean", "description": "Controls if a message is logged when the target process calls a 'Console.Read*' API and stdin is redirected to the console.", "default": true}}}, "pipeTransport": {"description": "When present, this tells the debugger to connect to a remote computer using another executable as a pipe that will relay standard input/output between VS Code and the .NET Core debugger backend executable (vsdbg).", "type": "object", "required": ["debugger<PERSON><PERSON>"], "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": [], "debuggerPath": "enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg"}, "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "debuggerPath": {"type": "string", "description": "The full path to the debugger on the target machine.", "default": "enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg"}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "windows": {"description": "Windows-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example 'c:\\tools\\plink.exe'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}, "osx": {"description": "OSX-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}, "linux": {"description": "Linux-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}}}, "suppressJITOptimizations": {"type": "boolean", "markdownDescription": "If true, when an optimized module (.dll compiled in the Release configuration) loads in the target process, the debugger will ask the Just-In-Time compiler to generate code with optimizations disabled. [More information](https://aka.ms/VSCode-CS-LaunchJson-SuppressJITOptimizations)", "default": false}, "symbolOptions": {"description": "Options to control how symbols (.pdb files) are found and loaded.", "default": {"searchPaths": [], "searchMicrosoftSymbolServer": false, "searchNuGetOrgSymbolServer": false}, "type": "object", "properties": {"searchPaths": {"type": "array", "items": {"type": "string"}, "description": "Array of symbol server URLs (example: http​://MyExampleSymbolServer) or directories (example: /build/symbols) to search for .pdb files. These directories will be searched in addition to the default locations -- next to the module and the path where the pdb was originally dropped to.", "default": []}, "searchMicrosoftSymbolServer": {"type": "boolean", "description": "If 'true' the Microsoft Symbol server (https​://msdl.microsoft.com​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "searchNuGetOrgSymbolServer": {"type": "boolean", "description": "If 'true' the NuGet.org symbol server (https​://symbols.nuget.org​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "cachePath": {"type": "string", "description": "Directory where symbols downloaded from symbol servers should be cached. If unspecified, on Windows the debugger will default to %TEMP%\\SymbolCache, and on Linux and macOS the debugger will default to ~/.dotnet/symbolcache.", "default": ""}, "moduleFilter": {"description": "Provides options to control which modules (.dll files) the debugger will attempt to load symbols (.pdb files) for.", "default": {"mode": "loadAllButExcluded", "excludedModules": []}, "type": "object", "required": ["mode"], "properties": {"mode": {"type": "string", "enum": ["loadAllButExcluded", "loadOnlyIncluded"], "enumDescriptions": ["Load symbols for all modules unless the module is in the 'excludedModules' array.", "Do not attempt to load symbols for ANY module unless it is in the 'includedModules' array, or it is included through the 'includeSymbolsNextToModules' setting."], "description": "Controls which of the two basic operating modes the module filter operates in.", "default": "loadAllButExcluded"}, "excludedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should NOT load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadAllButExcluded'.", "default": []}, "includedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": []}, "includeSymbolsNextToModules": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will still check next to the module itself and the launching executable, but it will not check paths on the symbol search list. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}, "includeSymbolsOnDemand": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will attempt to download symbols when it detects symbols are needed, such as when trying to step into the module. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}}}}}, "sourceLinkOptions": {"markdownDescription": "Options to control how Source Link connects to web servers. [More information](https://aka.ms/VSCode-DotNet-SourceLink)", "default": {"*": {"enabled": true}}, "type": "object", "additionalItems": {"type": "object", "properties": {"enabled": {"title": "boolean", "markdownDescription": "Is Source Link enabled for this URL? If unspecified, this option defaults to `true`.", "default": true}}}}, "expressionEvaluationOptions": {"description": "Options to control how the debugger evaluates expressions in data tips, the debug view's 'Watch' and 'Variables' sections, or in the Debug Console.", "default": {}, "type": "object", "properties": {"allowImplicitFuncEval": {"type": "boolean", "description": "When true (the default state), the debugger will automatically call property `get` methods and other implicit function calls.", "default": true}, "allowToString": {"type": "boolean", "markdownDescription": "When true (the default state), the debugger will automatically call `ToString` to format objects. This option has no effect if `allowImplicitFuncEval` is `false`.", "default": true}, "allowFastEvaluate": {"type": "boolean", "description": "When true (the default state), the debugger will attempt faster evaluation by simulating execution of simple properties and methods.", "default": true}, "showRawValues": {"type": "boolean", "description": "When true, the debugger will show raw structure of objects in variables windows.", "default": false}}}, "targetOutputLogPath": {"type": "string", "description": "When set, text that the target application writes to stdout and stderr (ex: Console.WriteLine) will be saved to the specified file. This option is ignored if console is set to something other than internalConsole. E.g. '${workspaceFolder}/out.txt'", "default": ""}, "targetArchitecture": {"type": "string", "markdownDescription": "[Only supported in local macOS debugging]\n\nThe architecture of the debuggee. This will automatically be detected unless this parameter is set. Allowed values are `x86_64` or `arm64`.", "enum": ["x86_64", "arm64"]}, "checkForDevCert": {"type": "boolean", "description": "If you are launching a web project on Windows or macOS and this is enabled, the debugger will check if the computer has a self-signed HTTPS certificate used to develop web servers running on https endpoints. If unspecified, defaults to true when `serverReadyAction` is set. This option does nothing on Linux, VS Code remote, and VS Code Web UI scenarios. If the HTTPS certificate is not found or isn't trusted, the user will be prompted to install/trust it.", "default": true}}}, "attach": {"type": "object", "required": [], "properties": {"processName": {"type": "string", "default": "", "markdownDescription": "The process name to attach to. If this is used, `processId` should not be used."}, "processId": {"anyOf": [{"type": "string", "markdownDescription": "The process id to attach to. Use \"\" to get a list of running processes to attach to. If `processId` used, `processName` should not be used.", "default": ""}, {"type": "integer", "markdownDescription": "The process id to attach to. Use \"\" to get a list of running processes to attach to. If `processId` used, `processName` should not be used.", "default": 0}]}, "sourceFileMap": {"type": "object", "markdownDescription": "Maps build-time paths to local source locations. All instances of build-time path will be replaced with the local source path.\n\nExample:\n\n`{\"<build-path>\":\"<local-source-path>\"}`", "additionalProperties": {"type": "string"}}, "justMyCode": {"type": "boolean", "markdownDescription": "When enabled (the default), the debugger only displays and steps into user code (\"My Code\"), ignoring system code and other code that is optimized or that does not have debugging symbols. [More information](https://aka.ms/VSCode-CS-LaunchJson-JustMyCode)", "default": true}, "requireExactSource": {"type": "boolean", "markdownDescription": "Flag to require current source code to match the pdb. This option defaults to `true`.", "default": true}, "enableStepFiltering": {"type": "boolean", "markdownDescription": "Flag to enable stepping over Properties and Operators. This option defaults to `true`.", "default": true}, "logging": {"description": "Flags to determine what types of messages should be logged to the output window.", "type": "object", "required": [], "default": {}, "properties": {"exceptions": {"type": "boolean", "markdownDescription": "Flag to determine whether exception messages should be logged to the output window. This option defaults to `true`.", "default": true}, "moduleLoad": {"type": "boolean", "markdownDescription": "Flag to determine whether module load events should be logged to the output window. This option defaults to `true`.", "default": true}, "programOutput": {"type": "boolean", "markdownDescription": "Flag to determine whether program output should be logged to the output window when not using an external console. This option defaults to `true`.", "default": true}, "browserStdOut": {"type": "boolean", "markdownDescription": "Flag to determine if stdout text from the launching the web browser should be logged to the output window. This option defaults to `true`.", "default": true}, "elapsedTiming": {"type": "boolean", "markdownDescription": "If true, protocol message logging will include `adapterElapsedTime` and `engineElapsedTime` properties to indicate the amount of time, in microseconds, that a request took. This option defaults to `false`.", "default": false}, "threadExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when a thread in the target process exits. This option defaults to `false`.", "default": false}, "processExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when the target process exits, or debugging is stopped. This option defaults to `true`.", "default": true}, "engineLogging": {"type": "boolean", "deprecationMessage": "The setting 'logging.engineLogging' has been deprecated in favor of 'logging.diagnosticsLog.protocolMessages'.", "default": false}, "diagnosticsLog": {"description": "Settings to control which messages are printed to the output window from the debugger's diagnostics log. This log is meant to help troubleshoot problems with the debugger.", "type": "object", "required": [], "default": {}, "properties": {"protocolMessages": {"type": "boolean", "markdownDescription": "Flag to determine whether DAP protocol messages exchanged between the C# debugger and the UI should be logged to the output window. This option defaults to `false`.", "default": false}, "dispatcherMessages": {"type": "string", "enum": ["none", "error", "important", "normal"], "enumDescriptions": ["Do not print additional diagnostic messages.", "Print error-level diagnostic messages.", "Print important diagnostic messages.", "Print all non-verbose diagnostic messages."], "markdownDescription": "Controls which messages are printed to the output window from the debugger's dispatcher. If not specified, this will default to `none` unless one of the verbose log settings are enabled (`debugEngineAPITracing`, `debugRuntimeEventTracing`, `expressionEvaluationTracing` or `startDebuggingTracing`), in which case the default changes to `normal`.", "default": "none"}, "debugEngineAPITracing": {"type": "string", "enum": ["none", "error", "all"], "enumDescriptions": ["Disable tracing API calls", "Print failures from debugger API calls.", "Print all debugger API calls. This is very verbose."], "markdownDescription": "Controls if API calls to Microsoft.VisualStudio.Debugger.Engine/vsdebugeng.h should be printed to the output window. This option defaults to `none`.", "default": "none"}, "debugRuntimeEventTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for events raised by the underlying runtime should be enabled. This option defaults to `false`.", "default": false}, "expressionEvaluationTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for expression evaluation should be enabled. This option defaults to `false`.", "default": false}, "startDebuggingTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for start debugging should be enabled. This option defaults to `false`.", "default": false}}}, "consoleUsageMessage": {"type": "boolean", "description": "Controls if a message is logged when the target process calls a 'Console.Read*' API and stdin is redirected to the console.", "default": true}}}, "pipeTransport": {"description": "When present, this tells the debugger to connect to a remote computer using another executable as a pipe that will relay standard input/output between VS Code and the .NET Core debugger backend executable (vsdbg).", "type": "object", "required": ["debugger<PERSON><PERSON>"], "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": [], "debuggerPath": "enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg"}, "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "debuggerPath": {"type": "string", "description": "The full path to the debugger on the target machine.", "default": "enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg"}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "windows": {"description": "Windows-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example 'c:\\tools\\plink.exe'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}, "osx": {"description": "OSX-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}, "linux": {"description": "Linux-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}}}, "suppressJITOptimizations": {"type": "boolean", "markdownDescription": "If true, when an optimized module (.dll compiled in the Release configuration) loads in the target process, the debugger will ask the Just-In-Time compiler to generate code with optimizations disabled. [More information](https://aka.ms/VSCode-CS-LaunchJson-SuppressJITOptimizations)", "default": false}, "symbolOptions": {"description": "Options to control how symbols (.pdb files) are found and loaded.", "default": {"searchPaths": [], "searchMicrosoftSymbolServer": false, "searchNuGetOrgSymbolServer": false}, "type": "object", "properties": {"searchPaths": {"type": "array", "items": {"type": "string"}, "description": "Array of symbol server URLs (example: http​://MyExampleSymbolServer) or directories (example: /build/symbols) to search for .pdb files. These directories will be searched in addition to the default locations -- next to the module and the path where the pdb was originally dropped to.", "default": []}, "searchMicrosoftSymbolServer": {"type": "boolean", "description": "If 'true' the Microsoft Symbol server (https​://msdl.microsoft.com​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "searchNuGetOrgSymbolServer": {"type": "boolean", "description": "If 'true' the NuGet.org symbol server (https​://symbols.nuget.org​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "cachePath": {"type": "string", "description": "Directory where symbols downloaded from symbol servers should be cached. If unspecified, on Windows the debugger will default to %TEMP%\\SymbolCache, and on Linux and macOS the debugger will default to ~/.dotnet/symbolcache.", "default": ""}, "moduleFilter": {"description": "Provides options to control which modules (.dll files) the debugger will attempt to load symbols (.pdb files) for.", "default": {"mode": "loadAllButExcluded", "excludedModules": []}, "type": "object", "required": ["mode"], "properties": {"mode": {"type": "string", "enum": ["loadAllButExcluded", "loadOnlyIncluded"], "enumDescriptions": ["Load symbols for all modules unless the module is in the 'excludedModules' array.", "Do not attempt to load symbols for ANY module unless it is in the 'includedModules' array, or it is included through the 'includeSymbolsNextToModules' setting."], "description": "Controls which of the two basic operating modes the module filter operates in.", "default": "loadAllButExcluded"}, "excludedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should NOT load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadAllButExcluded'.", "default": []}, "includedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": []}, "includeSymbolsNextToModules": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will still check next to the module itself and the launching executable, but it will not check paths on the symbol search list. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}, "includeSymbolsOnDemand": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will attempt to download symbols when it detects symbols are needed, such as when trying to step into the module. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}}}}}, "sourceLinkOptions": {"markdownDescription": "Options to control how Source Link connects to web servers. [More information](https://aka.ms/VSCode-DotNet-SourceLink)", "default": {"*": {"enabled": true}}, "type": "object", "additionalItems": {"type": "object", "properties": {"enabled": {"title": "boolean", "markdownDescription": "Is Source Link enabled for this URL? If unspecified, this option defaults to `true`.", "default": true}}}}, "expressionEvaluationOptions": {"description": "Options to control how the debugger evaluates expressions in data tips, the debug view's 'Watch' and 'Variables' sections, or in the Debug Console.", "default": {}, "type": "object", "properties": {"allowImplicitFuncEval": {"type": "boolean", "description": "When true (the default state), the debugger will automatically call property `get` methods and other implicit function calls.", "default": true}, "allowToString": {"type": "boolean", "markdownDescription": "When true (the default state), the debugger will automatically call `ToString` to format objects. This option has no effect if `allowImplicitFuncEval` is `false`.", "default": true}, "allowFastEvaluate": {"type": "boolean", "description": "When true (the default state), the debugger will attempt faster evaluation by simulating execution of simple properties and methods.", "default": true}, "showRawValues": {"type": "boolean", "description": "When true, the debugger will show raw structure of objects in variables windows.", "default": false}}}, "targetArchitecture": {"type": "string", "markdownDescription": "[Only supported in local macOS debugging]\n\nThe architecture of the debuggee. This will automatically be detected unless this parameter is set. Allowed values are `x86_64` or `arm64`.", "enum": ["x86_64", "arm64"]}}}}, "configurationSnippets": [{"label": ".NET: Launch Executable file (Console)", "description": "This snippet is used to launch a new process under the .NET debugger (coreclr), specifying the path to the executable to launch. In most cases, the \".NET: Launch C# project\" snippet is a better choice. This snippet is useful when the project was built outside this VS Code instance or you want to host your .NET Code in a custom executable, such as a specific version of 'dotnet.exe'/'dotnet' or the .NET Code is hosted by a native application. This snippet is for console applications.", "body": {"name": ".NET Core Launch (console)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "^\"\\${workspaceFolder}/bin/Debug/${1:<target-framework>}/${2:<project-name.dll>}\"", "args": [], "cwd": "^\"\\${workspaceFolder}\"", "stopAtEntry": false, "console": "internalConsole"}}, {"label": ".NET: Attach to a .NET process", "description": "Attach the .NET debugger (coreclr) to a running process. This can also be done using the 'Attach to a .NET 5+ or .NET Core process' command.", "body": {"name": ".NET Core Attach", "type": "coreclr", "request": "attach"}}, {"label": ".NET: Launch Executable file (Web)", "description": "This snippet is used to launch a new process under the .NET debugger (coreclr), specifying the path to the executable to launch. In most cases, the \".NET: Launch C# project\" snippet is a better choice. This snippet is useful when the project was built outside this VS Code instance or you want to host your .NET Code in a custom executable, such as a specific version of 'dotnet.exe'/'dotnet' or the .NET Code is hosted by a native application. This snippet is for web (ASP.NET Core) applications.", "body": {"name": ".NET Core Launch (web)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "^\"\\${workspaceFolder}/bin/Debug/${1:<target-framework>}/${2:<project-name.dll>}\"", "args": [], "cwd": "^\"\\${workspaceFolder}\"", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\\\bNow listening on:\\\\s+(https?://\\\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "^\"\\${workspaceFolder}/Views\""}}}, {"label": ".NET: Remote debugging - Launch Executable file (Console)", "description": "This snippet shows how to remote debug .NET Code **without** using VS Code remoting. It should be used in cases where you want to build your project locally but run it on another computer.", "body": {"name": ".NET Core Launch (console)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "^\"\\${workspaceFolder}/bin/Debug/${1:<target-framework>}/${2:<project-name.dll>}\"", "args": [], "cwd": "^\"\\${workspaceFolder}\"", "stopAtEntry": false, "console": "internalConsole", "pipeTransport": {"pipeCwd": "^\"\\${workspaceFolder}\"", "pipeProgram": "^\"${3:enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'}\"", "pipeArgs": [], "debuggerPath": "^\"${4:enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg}\""}}}, {"label": ".NET: Remote debugging - Attach to a .NET process", "description": "This snippet shows how to remote debug .NET Code **without** using VS Code remoting. It should be used in cases where you want to build your project locally but run it on another computer.", "body": {"name": ".NET Core Attach", "type": "coreclr", "request": "attach", "pipeTransport": {"pipeCwd": "^\"\\${workspaceFolder}\"", "pipeProgram": "^\"${1:enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'}\"", "pipeArgs": [], "debuggerPath": "^\"${2:enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg}\""}}}, {"label": ".NET: Web Assembly - Launch hosted Blazor project", "description": "This snippet is used to launch a new process under the Blazor WebAssembly debugger (blazorwasm), specifying the path to the executable to launch. In most cases, the \".NET: Launch C# project\" snippet is a better choice, but this snippet can be used to have full control over all launch options. This snippet is for hosted Blazor projects, which is a project that has a backend ASP.NET Core app to serve its files.", "body": {"name": "Launch and Debug Hosted Blazor WebAssembly App", "type": "b<PERSON><PERSON><PERSON><PERSON>", "request": "launch", "hosted": true, "program": "^\"\\${workspaceFolder}/bin/Debug/${1:<target-framework>}/${2:<project-name.dll>}\"", "cwd": "^\"\\${workspaceFolder}\""}}, {"label": ".NET: Web Assembly - Launch standalone Blazor project", "description": "This snippet is used to launch a new process under the Blazor WebAssembly debugger (blazorwasm). In most cases, the \".NET: Launch C# project\" snippet is a better choice, but this snippet can be used to have full control over all launch options. This snippet is for standalone Blazor projects, which is a project that does not have a backend ASP.NET Core app to serve its files.", "body": {"name": "Launch and Debug Standalone Blazor WebAssembly App", "type": "b<PERSON><PERSON><PERSON><PERSON>", "request": "launch", "cwd": "^\"\\${workspaceFolder}\""}}]}, {"type": "clr", "when": "workspacePlatform == windows", "hiddenWhen": "true", "label": ".NET Framework 4.x", "languages": ["csharp", "razor", "qsharp", "aspnetcorerazor"], "variables": {"pickProcess": "csharp.listProcess", "pickRemoteProcess": "csharp.listRemoteProcess", "pickRemoteDockerProcess": "csharp.listRemoteDockerProcess"}, "aiKey": "********************************-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"launch": {"type": "object", "required": ["program"], "properties": {"program": {"type": "string", "markdownDescription": "Path to the application dll or .NET Core host executable to launch.\nThis property normally takes the form: `${workspaceFolder}/bin/Debug/(target-framework)/(project-name.dll)`\n\nExample: `${workspaceFolder}/bin/Debug/netcoreapp1.1/MyProject.dll`\n\nWhere:\n`(target-framework)` is the framework that the debugged project is being built for. This is normally found in the project file as the `TargetFramework` property.\n\n`(project-name.dll)` is the name of debugged project's build output dll. This is normally the same as the project file name but with a '.dll' extension.", "default": "${workspaceFolder}/bin/Debug/<insert-target-framework-here>/<insert-project-name-here>.dll"}, "cwd": {"type": "string", "description": "Path to the working directory of the program being debugged. Default is the current workspace.", "default": "${workspaceFolder}"}, "args": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the program.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the program.", "default": ""}]}, "stopAtEntry": {"type": "boolean", "markdownDescription": "If true, the debugger should stop at the entry point of the target. This option defaults to `false`.", "default": false}, "launchBrowser": {"description": "Describes options to launch a web browser as part of launch", "default": {"enabled": true}, "type": "object", "required": ["enabled"], "properties": {"enabled": {"type": "boolean", "description": "Whether web browser launch is enabled. This option defaults to `true`.", "default": true}, "args": {"type": "string", "description": "The arguments to pass to the command to open the browser. This is used only if the platform-specific element (`osx`, `linux` or `windows`) doesn't specify a value for `args`. Use ${auto-detect-url} to automatically use the address the server is listening to.", "default": "${auto-detect-url}"}, "osx": {"description": "OSX-specific web launch configuration options. By default, this will start the browser using `open`.", "default": {"command": "open", "args": "${auto-detect-url}"}, "type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The executable which will start the web browser.", "default": "open"}, "args": {"type": "string", "description": "The arguments to pass to the command to open the browser. Use ${auto-detect-url} to automatically use the address the server is listening to.", "default": "${auto-detect-url}"}}}, "linux": {"description": "Linux-specific web launch configuration options. By default, this will start the browser using `xdg-open`.", "default": {"command": "xdg-open", "args": "${auto-detect-url}"}, "type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The executable which will start the web browser.", "default": "xdg-open"}, "args": {"type": "string", "description": "The arguments to pass to the command to open the browser. Use ${auto-detect-url} to automatically use the address the server is listening to.", "default": "${auto-detect-url}"}}}, "windows": {"description": "Windows-specific web launch configuration options. By default, this will start the browser using `cmd /c start`.", "default": {"command": "cmd.exe", "args": "/C start ${auto-detect-url}"}, "type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The executable which will start the web browser.", "default": "cmd.exe"}, "args": {"type": "string", "description": "The arguments to pass to the command to open the browser. Use ${auto-detect-url} to automatically use the address the server is listening to.", "default": "/C start ${auto-detect-url}"}}}}}, "env": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the program.", "default": {}}, "envFile": {"type": "string", "markdownDescription": "Environment variables passed to the program by a file. E.g. `${workspaceFolder}/.env`", "default": "${workspaceFolder}/.env"}, "console": {"type": "string", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["The target process's console input (stdin) and output (stdout/stderr) are routed through the VS Code Debug Console.", "The target process will run inside VS Code's integrated terminal.", "The target process will run inside its own external terminal. When using this mode, you will need to switch focus between Visual Studio Code and the external terminal window."], "markdownDescription": "Indicates which console the target program should be launched into. See https://aka.ms/VSCode-CS-LaunchJson-Console for more information.", "settingsDescription": "**Note:** _This option is only used for console projects launched with the `dotnet` debug configuration type_.\n\nIndicates which console the target program should be launched into. See https://aka.ms/VSCode-CS-LaunchJson-Console for more information.", "default": "internalConsole"}, "externalConsole": {"type": "boolean", "markdownDescription": "Attribute `externalConsole` is deprecated, use `console` instead. This option defaults to `false`.", "default": false}, "launchSettingsFilePath": {"type": "string", "markdownDescription": "The path to a launchSettings.json file. If this isn't set, the debugger will search in `{cwd}/Properties/launchSettings.json`.", "default": "${workspaceFolder}/Properties/launchSettings.json"}, "launchSettingsProfile": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "If specified, indicates the name of the profile in launchSettings.json to use. This is ignored if launchSettings.json is not found. launchSettings.json will be read from the path specified should be the 'launchSettingsFilePath' property, or {cwd}/Properties/launchSettings.json if that isn't set. If this is set to null or an empty string then launchSettings.json is ignored. If this value is not specified the first 'Project' profile will be used.", "default": "<insert-profile-name>"}, "sourceFileMap": {"type": "object", "markdownDescription": "Maps build-time paths to local source locations. All instances of build-time path will be replaced with the local source path.\n\nExample:\n\n`{\"<build-path>\":\"<local-source-path>\"}`", "additionalProperties": {"type": "string"}, "default": {}}, "justMyCode": {"type": "boolean", "markdownDescription": "When enabled (the default), the debugger only displays and steps into user code (\"My Code\"), ignoring system code and other code that is optimized or that does not have debugging symbols. [More information](https://aka.ms/VSCode-CS-LaunchJson-JustMyCode)", "default": true}, "requireExactSource": {"type": "boolean", "markdownDescription": "Flag to require current source code to match the pdb. This option defaults to `true`.", "default": true}, "enableStepFiltering": {"type": "boolean", "markdownDescription": "Flag to enable stepping over Properties and Operators. This option defaults to `true`.", "default": true}, "logging": {"description": "Flags to determine what types of messages should be logged to the output window.", "type": "object", "required": [], "default": {}, "properties": {"exceptions": {"type": "boolean", "markdownDescription": "Flag to determine whether exception messages should be logged to the output window. This option defaults to `true`.", "default": true}, "moduleLoad": {"type": "boolean", "markdownDescription": "Flag to determine whether module load events should be logged to the output window. This option defaults to `true`.", "default": true}, "programOutput": {"type": "boolean", "markdownDescription": "Flag to determine whether program output should be logged to the output window when not using an external console. This option defaults to `true`.", "default": true}, "browserStdOut": {"type": "boolean", "markdownDescription": "Flag to determine if stdout text from the launching the web browser should be logged to the output window. This option defaults to `true`.", "default": true}, "elapsedTiming": {"type": "boolean", "markdownDescription": "If true, protocol message logging will include `adapterElapsedTime` and `engineElapsedTime` properties to indicate the amount of time, in microseconds, that a request took. This option defaults to `false`.", "default": false}, "threadExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when a thread in the target process exits. This option defaults to `false`.", "default": false}, "processExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when the target process exits, or debugging is stopped. This option defaults to `true`.", "default": true}, "engineLogging": {"type": "boolean", "deprecationMessage": "The setting 'logging.engineLogging' has been deprecated in favor of 'logging.diagnosticsLog.protocolMessages'.", "default": false}, "diagnosticsLog": {"description": "Settings to control which messages are printed to the output window from the debugger's diagnostics log. This log is meant to help troubleshoot problems with the debugger.", "type": "object", "required": [], "default": {}, "properties": {"protocolMessages": {"type": "boolean", "markdownDescription": "Flag to determine whether DAP protocol messages exchanged between the C# debugger and the UI should be logged to the output window. This option defaults to `false`.", "default": false}, "dispatcherMessages": {"type": "string", "enum": ["none", "error", "important", "normal"], "enumDescriptions": ["Do not print additional diagnostic messages.", "Print error-level diagnostic messages.", "Print important diagnostic messages.", "Print all non-verbose diagnostic messages."], "markdownDescription": "Controls which messages are printed to the output window from the debugger's dispatcher. If not specified, this will default to `none` unless one of the verbose log settings are enabled (`debugEngineAPITracing`, `debugRuntimeEventTracing`, `expressionEvaluationTracing` or `startDebuggingTracing`), in which case the default changes to `normal`.", "default": "none"}, "debugEngineAPITracing": {"type": "string", "enum": ["none", "error", "all"], "enumDescriptions": ["Disable tracing API calls", "Print failures from debugger API calls.", "Print all debugger API calls. This is very verbose."], "markdownDescription": "Controls if API calls to Microsoft.VisualStudio.Debugger.Engine/vsdebugeng.h should be printed to the output window. This option defaults to `none`.", "default": "none"}, "debugRuntimeEventTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for events raised by the underlying runtime should be enabled. This option defaults to `false`.", "default": false}, "expressionEvaluationTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for expression evaluation should be enabled. This option defaults to `false`.", "default": false}, "startDebuggingTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for start debugging should be enabled. This option defaults to `false`.", "default": false}}}, "consoleUsageMessage": {"type": "boolean", "description": "Controls if a message is logged when the target process calls a 'Console.Read*' API and stdin is redirected to the console.", "default": true}}}, "pipeTransport": {"description": "When present, this tells the debugger to connect to a remote computer using another executable as a pipe that will relay standard input/output between VS Code and the .NET Core debugger backend executable (vsdbg).", "type": "object", "required": ["debugger<PERSON><PERSON>"], "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": [], "debuggerPath": "enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg"}, "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "debuggerPath": {"type": "string", "description": "The full path to the debugger on the target machine.", "default": "enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg"}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "windows": {"description": "Windows-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example 'c:\\tools\\plink.exe'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}, "osx": {"description": "OSX-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}, "linux": {"description": "Linux-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}}}, "suppressJITOptimizations": {"type": "boolean", "markdownDescription": "If true, when an optimized module (.dll compiled in the Release configuration) loads in the target process, the debugger will ask the Just-In-Time compiler to generate code with optimizations disabled. [More information](https://aka.ms/VSCode-CS-LaunchJson-SuppressJITOptimizations)", "default": false}, "symbolOptions": {"description": "Options to control how symbols (.pdb files) are found and loaded.", "default": {"searchPaths": [], "searchMicrosoftSymbolServer": false, "searchNuGetOrgSymbolServer": false}, "type": "object", "properties": {"searchPaths": {"type": "array", "items": {"type": "string"}, "description": "Array of symbol server URLs (example: http​://MyExampleSymbolServer) or directories (example: /build/symbols) to search for .pdb files. These directories will be searched in addition to the default locations -- next to the module and the path where the pdb was originally dropped to.", "default": []}, "searchMicrosoftSymbolServer": {"type": "boolean", "description": "If 'true' the Microsoft Symbol server (https​://msdl.microsoft.com​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "searchNuGetOrgSymbolServer": {"type": "boolean", "description": "If 'true' the NuGet.org symbol server (https​://symbols.nuget.org​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "cachePath": {"type": "string", "description": "Directory where symbols downloaded from symbol servers should be cached. If unspecified, on Windows the debugger will default to %TEMP%\\SymbolCache, and on Linux and macOS the debugger will default to ~/.dotnet/symbolcache.", "default": ""}, "moduleFilter": {"description": "Provides options to control which modules (.dll files) the debugger will attempt to load symbols (.pdb files) for.", "default": {"mode": "loadAllButExcluded", "excludedModules": []}, "type": "object", "required": ["mode"], "properties": {"mode": {"type": "string", "enum": ["loadAllButExcluded", "loadOnlyIncluded"], "enumDescriptions": ["Load symbols for all modules unless the module is in the 'excludedModules' array.", "Do not attempt to load symbols for ANY module unless it is in the 'includedModules' array, or it is included through the 'includeSymbolsNextToModules' setting."], "description": "Controls which of the two basic operating modes the module filter operates in.", "default": "loadAllButExcluded"}, "excludedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should NOT load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadAllButExcluded'.", "default": []}, "includedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": []}, "includeSymbolsNextToModules": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will still check next to the module itself and the launching executable, but it will not check paths on the symbol search list. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}, "includeSymbolsOnDemand": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will attempt to download symbols when it detects symbols are needed, such as when trying to step into the module. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}}}}}, "sourceLinkOptions": {"markdownDescription": "Options to control how Source Link connects to web servers. [More information](https://aka.ms/VSCode-DotNet-SourceLink)", "default": {"*": {"enabled": true}}, "type": "object", "additionalItems": {"type": "object", "properties": {"enabled": {"title": "boolean", "markdownDescription": "Is Source Link enabled for this URL? If unspecified, this option defaults to `true`.", "default": true}}}}, "expressionEvaluationOptions": {"description": "Options to control how the debugger evaluates expressions in data tips, the debug view's 'Watch' and 'Variables' sections, or in the Debug Console.", "default": {}, "type": "object", "properties": {"allowImplicitFuncEval": {"type": "boolean", "description": "When true (the default state), the debugger will automatically call property `get` methods and other implicit function calls.", "default": true}, "allowToString": {"type": "boolean", "markdownDescription": "When true (the default state), the debugger will automatically call `ToString` to format objects. This option has no effect if `allowImplicitFuncEval` is `false`.", "default": true}, "allowFastEvaluate": {"type": "boolean", "description": "When true (the default state), the debugger will attempt faster evaluation by simulating execution of simple properties and methods.", "default": true}, "showRawValues": {"type": "boolean", "description": "When true, the debugger will show raw structure of objects in variables windows.", "default": false}}}, "targetOutputLogPath": {"type": "string", "description": "When set, text that the target application writes to stdout and stderr (ex: Console.WriteLine) will be saved to the specified file. This option is ignored if console is set to something other than internalConsole. E.g. '${workspaceFolder}/out.txt'", "default": ""}, "targetArchitecture": {"type": "string", "markdownDescription": "[Only supported in local macOS debugging]\n\nThe architecture of the debuggee. This will automatically be detected unless this parameter is set. Allowed values are `x86_64` or `arm64`.", "enum": ["x86_64", "arm64"]}, "checkForDevCert": {"type": "boolean", "description": "If you are launching a web project on Windows or macOS and this is enabled, the debugger will check if the computer has a self-signed HTTPS certificate used to develop web servers running on https endpoints. If unspecified, defaults to true when `serverReadyAction` is set. This option does nothing on Linux, VS Code remote, and VS Code Web UI scenarios. If the HTTPS certificate is not found or isn't trusted, the user will be prompted to install/trust it.", "default": true}}}, "attach": {"type": "object", "required": [], "properties": {"processName": {"type": "string", "default": "", "markdownDescription": "The process name to attach to. If this is used, `processId` should not be used."}, "processId": {"anyOf": [{"type": "string", "markdownDescription": "The process id to attach to. Use \"\" to get a list of running processes to attach to. If `processId` used, `processName` should not be used.", "default": ""}, {"type": "integer", "markdownDescription": "The process id to attach to. Use \"\" to get a list of running processes to attach to. If `processId` used, `processName` should not be used.", "default": 0}]}, "sourceFileMap": {"type": "object", "markdownDescription": "Maps build-time paths to local source locations. All instances of build-time path will be replaced with the local source path.\n\nExample:\n\n`{\"<build-path>\":\"<local-source-path>\"}`", "additionalProperties": {"type": "string"}}, "justMyCode": {"type": "boolean", "markdownDescription": "When enabled (the default), the debugger only displays and steps into user code (\"My Code\"), ignoring system code and other code that is optimized or that does not have debugging symbols. [More information](https://aka.ms/VSCode-CS-LaunchJson-JustMyCode)", "default": true}, "requireExactSource": {"type": "boolean", "markdownDescription": "Flag to require current source code to match the pdb. This option defaults to `true`.", "default": true}, "enableStepFiltering": {"type": "boolean", "markdownDescription": "Flag to enable stepping over Properties and Operators. This option defaults to `true`.", "default": true}, "logging": {"description": "Flags to determine what types of messages should be logged to the output window.", "type": "object", "required": [], "default": {}, "properties": {"exceptions": {"type": "boolean", "markdownDescription": "Flag to determine whether exception messages should be logged to the output window. This option defaults to `true`.", "default": true}, "moduleLoad": {"type": "boolean", "markdownDescription": "Flag to determine whether module load events should be logged to the output window. This option defaults to `true`.", "default": true}, "programOutput": {"type": "boolean", "markdownDescription": "Flag to determine whether program output should be logged to the output window when not using an external console. This option defaults to `true`.", "default": true}, "browserStdOut": {"type": "boolean", "markdownDescription": "Flag to determine if stdout text from the launching the web browser should be logged to the output window. This option defaults to `true`.", "default": true}, "elapsedTiming": {"type": "boolean", "markdownDescription": "If true, protocol message logging will include `adapterElapsedTime` and `engineElapsedTime` properties to indicate the amount of time, in microseconds, that a request took. This option defaults to `false`.", "default": false}, "threadExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when a thread in the target process exits. This option defaults to `false`.", "default": false}, "processExit": {"type": "boolean", "markdownDescription": "Controls if a message is logged when the target process exits, or debugging is stopped. This option defaults to `true`.", "default": true}, "engineLogging": {"type": "boolean", "deprecationMessage": "The setting 'logging.engineLogging' has been deprecated in favor of 'logging.diagnosticsLog.protocolMessages'.", "default": false}, "diagnosticsLog": {"description": "Settings to control which messages are printed to the output window from the debugger's diagnostics log. This log is meant to help troubleshoot problems with the debugger.", "type": "object", "required": [], "default": {}, "properties": {"protocolMessages": {"type": "boolean", "markdownDescription": "Flag to determine whether DAP protocol messages exchanged between the C# debugger and the UI should be logged to the output window. This option defaults to `false`.", "default": false}, "dispatcherMessages": {"type": "string", "enum": ["none", "error", "important", "normal"], "enumDescriptions": ["Do not print additional diagnostic messages.", "Print error-level diagnostic messages.", "Print important diagnostic messages.", "Print all non-verbose diagnostic messages."], "markdownDescription": "Controls which messages are printed to the output window from the debugger's dispatcher. If not specified, this will default to `none` unless one of the verbose log settings are enabled (`debugEngineAPITracing`, `debugRuntimeEventTracing`, `expressionEvaluationTracing` or `startDebuggingTracing`), in which case the default changes to `normal`.", "default": "none"}, "debugEngineAPITracing": {"type": "string", "enum": ["none", "error", "all"], "enumDescriptions": ["Disable tracing API calls", "Print failures from debugger API calls.", "Print all debugger API calls. This is very verbose."], "markdownDescription": "Controls if API calls to Microsoft.VisualStudio.Debugger.Engine/vsdebugeng.h should be printed to the output window. This option defaults to `none`.", "default": "none"}, "debugRuntimeEventTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for events raised by the underlying runtime should be enabled. This option defaults to `false`.", "default": false}, "expressionEvaluationTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for expression evaluation should be enabled. This option defaults to `false`.", "default": false}, "startDebuggingTracing": {"type": "boolean", "markdownDescription": "Flag to determine whether verbose tracing for start debugging should be enabled. This option defaults to `false`.", "default": false}}}, "consoleUsageMessage": {"type": "boolean", "description": "Controls if a message is logged when the target process calls a 'Console.Read*' API and stdin is redirected to the console.", "default": true}}}, "pipeTransport": {"description": "When present, this tells the debugger to connect to a remote computer using another executable as a pipe that will relay standard input/output between VS Code and the .NET Core debugger backend executable (vsdbg).", "type": "object", "required": ["debugger<PERSON><PERSON>"], "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": [], "debuggerPath": "enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg"}, "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "debuggerPath": {"type": "string", "description": "The full path to the debugger on the target machine.", "default": "enter the path for the debugger on the target machine, for example ~/vsdbg/vsdbg"}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "windows": {"description": "Windows-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example 'c:\\tools\\plink.exe'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}, "osx": {"description": "OSX-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}, "linux": {"description": "Linux-specific pipe launch configuration options", "default": {"pipeCwd": "${workspaceFolder}", "pipeProgram": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'", "pipeArgs": []}, "type": "object", "properties": {"pipeCwd": {"type": "string", "description": "The fully qualified path to the working directory for the pipe program.", "default": "${workspaceFolder}"}, "pipeProgram": {"type": "string", "description": "The fully qualified pipe command to execute.", "default": "enter the fully qualified path for the pipe program name, for example '/usr/bin/ssh'"}, "pipeArgs": {"anyOf": [{"type": "array", "description": "Command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "items": {"type": "string"}, "default": []}, {"type": "string", "description": "Stringified version of command line arguments passed to the pipe program. Token ${debuggerCommand} in pipeArgs will get replaced by the full debugger command, this token can be specified inline with other arguments. If ${debuggerCommand} isn't used in any argument, the full debugger command will be instead be added to the end of the argument list.", "default": ""}], "default": []}, "quoteArgs": {"type": "boolean", "description": "Should arguments that contain characters that need to be quoted (example: spaces) be quoted? Defaults to 'true'. If set to false, the debugger command will no longer be automatically quoted.", "default": true}, "pipeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables passed to the pipe program.", "default": {}}}}}}, "suppressJITOptimizations": {"type": "boolean", "markdownDescription": "If true, when an optimized module (.dll compiled in the Release configuration) loads in the target process, the debugger will ask the Just-In-Time compiler to generate code with optimizations disabled. [More information](https://aka.ms/VSCode-CS-LaunchJson-SuppressJITOptimizations)", "default": false}, "symbolOptions": {"description": "Options to control how symbols (.pdb files) are found and loaded.", "default": {"searchPaths": [], "searchMicrosoftSymbolServer": false, "searchNuGetOrgSymbolServer": false}, "type": "object", "properties": {"searchPaths": {"type": "array", "items": {"type": "string"}, "description": "Array of symbol server URLs (example: http​://MyExampleSymbolServer) or directories (example: /build/symbols) to search for .pdb files. These directories will be searched in addition to the default locations -- next to the module and the path where the pdb was originally dropped to.", "default": []}, "searchMicrosoftSymbolServer": {"type": "boolean", "description": "If 'true' the Microsoft Symbol server (https​://msdl.microsoft.com​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "searchNuGetOrgSymbolServer": {"type": "boolean", "description": "If 'true' the NuGet.org symbol server (https​://symbols.nuget.org​/download/symbols) is added to the symbols search path. If unspecified, this option defaults to 'false'.", "default": false}, "cachePath": {"type": "string", "description": "Directory where symbols downloaded from symbol servers should be cached. If unspecified, on Windows the debugger will default to %TEMP%\\SymbolCache, and on Linux and macOS the debugger will default to ~/.dotnet/symbolcache.", "default": ""}, "moduleFilter": {"description": "Provides options to control which modules (.dll files) the debugger will attempt to load symbols (.pdb files) for.", "default": {"mode": "loadAllButExcluded", "excludedModules": []}, "type": "object", "required": ["mode"], "properties": {"mode": {"type": "string", "enum": ["loadAllButExcluded", "loadOnlyIncluded"], "enumDescriptions": ["Load symbols for all modules unless the module is in the 'excludedModules' array.", "Do not attempt to load symbols for ANY module unless it is in the 'includedModules' array, or it is included through the 'includeSymbolsNextToModules' setting."], "description": "Controls which of the two basic operating modes the module filter operates in.", "default": "loadAllButExcluded"}, "excludedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should NOT load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadAllButExcluded'.", "default": []}, "includedModules": {"type": "array", "items": {"type": "string"}, "description": "Array of modules that the debugger should load symbols for. Wildcards (example: MyCompany.*.dll) are supported.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": []}, "includeSymbolsNextToModules": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will still check next to the module itself and the launching executable, but it will not check paths on the symbol search list. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}, "includeSymbolsOnDemand": {"type": "boolean", "description": "If true, for any module NOT in the 'includedModules' array, the debugger will attempt to download symbols when it detects symbols are needed, such as when trying to step into the module. This option defaults to 'true'.\n\nThis property is ignored unless 'mode' is set to 'loadOnlyIncluded'.", "default": true}}}}}, "sourceLinkOptions": {"markdownDescription": "Options to control how Source Link connects to web servers. [More information](https://aka.ms/VSCode-DotNet-SourceLink)", "default": {"*": {"enabled": true}}, "type": "object", "additionalItems": {"type": "object", "properties": {"enabled": {"title": "boolean", "markdownDescription": "Is Source Link enabled for this URL? If unspecified, this option defaults to `true`.", "default": true}}}}, "expressionEvaluationOptions": {"description": "Options to control how the debugger evaluates expressions in data tips, the debug view's 'Watch' and 'Variables' sections, or in the Debug Console.", "default": {}, "type": "object", "properties": {"allowImplicitFuncEval": {"type": "boolean", "description": "When true (the default state), the debugger will automatically call property `get` methods and other implicit function calls.", "default": true}, "allowToString": {"type": "boolean", "markdownDescription": "When true (the default state), the debugger will automatically call `ToString` to format objects. This option has no effect if `allowImplicitFuncEval` is `false`.", "default": true}, "allowFastEvaluate": {"type": "boolean", "description": "When true (the default state), the debugger will attempt faster evaluation by simulating execution of simple properties and methods.", "default": true}, "showRawValues": {"type": "boolean", "description": "When true, the debugger will show raw structure of objects in variables windows.", "default": false}}}, "targetArchitecture": {"type": "string", "markdownDescription": "[Only supported in local macOS debugging]\n\nThe architecture of the debuggee. This will automatically be detected unless this parameter is set. Allowed values are `x86_64` or `arm64`.", "enum": ["x86_64", "arm64"]}}}}}, {"type": "b<PERSON><PERSON><PERSON><PERSON>", "label": "Blazor WebAssembly Debug", "hiddenWhen": "dotnet.debug.serviceBrokerAvailable", "initialConfigurations": [{"type": "b<PERSON><PERSON><PERSON><PERSON>", "name": "Launch and Debug Blazor WebAssembly Application", "request": "launch"}], "configurationAttributes": {"launch": {"properties": {"cwd": {"type": "string", "description": "The directory of the Blazor WebAssembly app, defaults to the workspace folder.", "default": "${workspaceFolder}"}, "url": {"type": "string", "description": "The URL of the application", "default": "https://localhost:5001"}, "browser": {"type": "string", "description": "The debugging browser to launch (Edge or Chrome)", "default": "edge", "enum": ["chrome", "edge"]}, "trace": {"type": ["boolean", "string"], "default": "true", "enum": ["verbose", true], "description": "If true, verbose logs from JS debugger are sent to log file. If 'verbose', send logs to console."}, "hosted": {"type": "boolean", "default": "false", "description": "True if the app is a hosted Blazor WebAssembly app, false otherwise."}, "webRoot": {"type": "string", "default": "${workspaceFolder}", "description": "Specifies the absolute path to the webserver root."}, "timeout": {"type": "number", "default": 30000, "description": "Retry for this number of milliseconds to connect to browser."}, "program": {"type": "string", "default": "${workspaceFolder}/Server/bin/Debug/<target-framework>/<target-dll>", "description": "The path of the DLL to execute when launching a hosted server app"}, "env": {"type": "object", "description": "Environment variables passed to dotnet. Only valid for hosted apps."}, "dotNetConfig": {"description": "Options passed to the underlying .NET debugger. For more info, see https://github.com/dotnet/vscode-csharp/blob/main/debugger.md.", "type": "object", "required": [], "default": {}, "properties": {"justMyCode": {"type": "boolean", "description": "Optional flag to only show user code.", "default": true}, "logging": {"description": "Optional flags to determine what types of messages should be logged to the output window. Applicable only for the app server of hosted Blazor WASM apps.", "type": "object", "required": [], "default": {}, "properties": {"exceptions": {"type": "boolean", "description": "Optional flag to determine whether exception messages should be logged to the output window.", "default": true}, "moduleLoad": {"type": "boolean", "description": "Optional flag to determine whether module load events should be logged to the output window.", "default": true}, "programOutput": {"type": "boolean", "description": "Optional flag to determine whether program output should be logged to the output window when not using an external console.", "default": true}, "engineLogging": {"type": "boolean", "description": "Optional flag to determine whether diagnostic engine logs should be logged to the output window.", "default": false}, "browserStdOut": {"type": "boolean", "description": "Optional flag to determine if stdout text from the launching the web browser should be logged to the output window.", "default": true}, "elapsedTiming": {"type": "boolean", "description": "If true, engine logging will include `adapterElapsedTime` and `engineElapsedTime` properties to indicate the amount of time, in microseconds, that a request took.", "default": false}, "threadExit": {"type": "boolean", "description": "Controls if a message is logged when a thread in the target process exits. Default: `false`.", "default": false}, "processExit": {"type": "boolean", "description": "Controls if a message is logged when the target process exits, or debugging is stopped. Default: `true`.", "default": true}}}, "sourceFileMap": {"type": "object", "description": "Optional source file mappings passed to the debug engine. Example: '{ \"C:\\foo\":\"/home/<USER>/foo\" }'", "additionalProperties": {"type": "string"}, "default": {"<insert-source-path-here>": "<insert-target-path-here>"}}}}, "browserConfig": {"description": "Options based to the underlying JavaScript debugger. For more info, see https://github.com/microsoft/vscode-js-debug/blob/master/OPTIONS.md.", "type": "object", "required": [], "default": {}, "properties": {"outputCapture": {"enum": ["console", "std"], "description": "From where to capture output messages: the default debug API if set to `console`, or stdout/stderr streams if set to `std`.", "default": "console"}}}}}, "attach": {"properties": {"url": {"type": "string", "description": "The URL of the application", "default": "https://localhost:5001"}, "cwd": {"type": "string", "description": "The directory of the Blazor WebAssembly app, defaults to the workspace folder.", "default": "${workspaceFolder}"}, "browser": {"type": "string", "description": "The debugging browser to launch (Edge or Chrome)", "default": "chrome", "enum": ["chrome", "edge"]}, "trace": {"type": ["boolean", "string"], "default": "true", "enum": ["verbose", true], "description": "If true, verbose logs from JS debugger are sent to log file. If 'verbose', send logs to console."}, "webRoot": {"type": "string", "default": "${workspaceFolder}", "description": "Specifies the absolute path to the webserver root."}, "timeout": {"type": "number", "default": 30000, "description": "Retry for this number of milliseconds to connect to browser."}}}}}, {"type": "dotnet", "label": "C#", "hiddenWhen": "!dotnet.debug.serviceBrokerAvailable", "languages": ["csharp", "razor", "aspnetcorerazor"], "variables": {}, "aiKey": "********************************-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"launch": {"type": "object", "required": ["projectPath"], "properties": {"projectPath": {"type": "string", "description": "Path to the .csproj file.", "default": "${workspaceFolder}/<insert-project-name-here>.csproj"}, "launchConfigurationId": {"type": "string", "description": "The launch configuration id to use. Empty string will use the current active configuration."}}}}, "configurationSnippets": [{"label": ".NET: Launch C# project", "description": "This snippet configures VS Code to debug a C# project. Debug options (example: arguments to the executable) can be configured through the '<project-directory>/Properties/launchSettings.json' file.", "body": {"name": "C#: ${1:<project-name>} Debug", "type": "dotnet", "request": "launch", "projectPath": "^\"\\${workspaceFolder}/${2:<relative-path-to-project-folder>}${1:<project-name>}.csproj\""}}]}, {"type": "monovsdbg", "label": ".NET Core using Mono Runtime", "hiddenWhen": "true", "languages": ["csharp"], "aiKey": "********************************-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255"}], "semanticTokenTypes": [{"id": "razorComponentElement", "description": "A Razor component element"}, {"id": "razorComponentAttribute", "description": "A Razor component attribute"}, {"id": "razorTagHelperElement", "description": "A Razor TagHelper Element"}, {"id": "razorTagHelperAttribute", "description": "A Razor TagHelper Attribute"}, {"id": "razorTransition", "description": "A Razor transition"}, {"id": "razorDirectiveAttribute", "description": "A Razor Directive Attribute"}, {"id": "razorDirectiveColon", "description": "A colon between directive attribute parameters"}, {"id": "razorDirective", "description": "A Razor directive such as 'code' or 'function'"}, {"id": "razorComment", "description": "A Razor comment"}, {"id": "markupCommentPunctuation", "description": "The '@' or '*' of a <PERSON><PERSON> comment."}, {"id": "markupTagDelimiter", "description": "Markup delimiters like '<', '>', and '/'."}, {"id": "markupOperator", "description": "Delimiter for Markup Attribute Key-Value pairs."}, {"id": "markupElement", "description": "The name of a Markup element."}, {"id": "markupAttribute", "description": "The name of a Markup attribute."}, {"id": "markupAttributeQuote", "description": "A token that represents an attribute quote in a Markup attribute."}, {"id": "markupAttributeValue", "description": "The value of a Markup attribute."}, {"id": "markupComment", "description": "The contents of a <PERSON><PERSON> comment."}, {"id": "markupCommentPunctuation", "description": "The begining or ending punctuation of a <PERSON><PERSON> comment."}, {"id": "excludedCode", "description": "A token that represents inactive code."}, {"id": "controlKeyword", "description": "A token that represents a control-flow keyword.", "superType": "keyword"}, {"id": "operatorOverloaded", "description": "A declaration or reference to an overloaded operator."}, {"id": "whitespace", "description": "A token that represents whitespace."}, {"id": "text", "description": "A token that represents text."}, {"id": "preprocessorText", "description": "Text associated with a preprocessor directive."}, {"id": "punctuation", "description": "A token that represents punctuation."}, {"id": "stringVerbatim", "superType": "string", "description": "A token that represents a verbatim string."}, {"id": "stringEscapeCharacter", "superType": "string", "description": "An escape character within a string."}, {"id": "recordClass", "superType": "class", "description": "A definition or reference to a record class type."}, {"id": "delegate", "superType": "method", "description": "A definition or reference to a delegate type."}, {"id": "module", "superType": "namespace", "description": "A definition or reference to a module name."}, {"id": "recordStruct", "superType": "struct", "description": "A definition or reference to a record struct type."}, {"id": "field", "superType": "property", "description": "A definition or reference to a field."}, {"id": "constant", "superType": "variable", "description": "A definition or reference to a constant."}, {"id": "extensionMethod", "superType": "method", "description": "A definition or reference to an extension method"}, {"id": "xmlDocCommentAttributeName", "description": "A token that represents an attribute in an XML documentation comment"}, {"id": "xmlDocCommentAttributeQuotes", "description": "A token that represents an attribute quote in an XML documentation comment"}, {"id": "xmlDocCommentAttributeValue", "description": "A token that represents an attribute value in an XML documentation comment"}, {"id": "xmlDocCommentCDataSection", "description": "A token that represents a CDATA section in an XML documentation comment"}, {"id": "xmlDocCommentComment", "description": "A token that represents a comment in an XML documentation comment"}, {"id": "xmlDocCommentDelimiter", "description": "A token that represents a delimeter in an XML documentation comment"}, {"id": "xmlDocCommentEntityReference", "description": "A token that represents reference to an entity in an XML documentation comment"}, {"id": "xmlDocCommentName", "description": "A token that represents a name in an XML documentation comment"}, {"id": "xmlDocCommentProcessingInstruction", "description": "A token that represents a processing instruction in an XML documentation comment"}, {"id": "xmlDocCommentText", "description": "A token that represents text in an XML documentation comment"}, {"id": "xmlLiteralAttributeName", "description": "A token that represents an attribute name in an XML literal"}, {"id": "xmlLiteralAttributeQuotes", "description": "A token that represents an attribute quote in an XML literal"}, {"id": "xmlLiteralAttributeValue", "description": "A token that represents an attribute value in an XML literal"}, {"id": "xmlLiteralCDataSection", "description": "A token that represents a CDATA section in an XML literal"}, {"id": "xmlLiteralComment", "description": "A token that represents a comment in an XML literal"}, {"id": "xmlLiteralDelimiter", "description": "A token that represents a delimiter in an XML literal"}, {"id": "xmlLiteralEmbeddedExpression", "description": "A token that represents an emebedded expression in an XML literal"}, {"id": "xmlLiteralEntityReference", "description": "A token that represents a reference to an entity in an XML literal"}, {"id": "xmlLiteralName", "description": "A token that represents a name in an XML literal"}, {"id": "xmlLiteralProcessingInstruction", "description": "A token that represents a processing instruction in an XML literal"}, {"id": "xmlLiteralText", "description": "A token that represents text in an XML literal"}, {"id": "regexComment", "description": "A token that represents a comment in a regex string"}, {"id": "regexCharacterClass", "description": "A token that represents a character class in a regex string"}, {"id": "regexAnchor", "description": "A token that represents an anchor in a regex string"}, {"id": "regexQuantifier", "description": "A token that represents a quantifier in a regex string"}, {"id": "regexGrouping", "description": "A token that represents a group in a regex string"}, {"id": "regexAlternation", "description": "A token that represents an alternation in a regex string"}, {"id": "regexText", "description": "A token that represents text in a regex string"}, {"id": "regexSelfEscapedCharacter", "description": "A token that represents a self escaped character in a regex string"}, {"id": "regexOtherEscape", "description": "A token that represents an escape in a regex string"}, {"id": "jsonComment", "description": "A token that represents a comment in a JSON string"}, {"id": "jsonNumber", "description": "A token that represents a number in a JSON string"}, {"id": "jsonString", "description": "A token that represents a string in a JSON string"}, {"id": "jsonKeyword", "description": "A token that represents a keyword in a JSON string"}, {"id": "jsonText", "description": "A token that represents text in a JSON string"}, {"id": "jsonOperator", "description": "A token that represents an operator in a JSON string"}, {"id": "jsonPunctuation", "description": "A token that represents punctuation in a JSON string"}, {"id": "jsonArray", "description": "A token that represents an array in a JSON string"}, {"id": "jsonObject", "description": "A token that represents an object in a JSON string"}, {"id": "jsonPropertyName", "description": "A token that represents a property name in a JSON string"}, {"id": "jsonConstructorName", "description": "A token that represents a constructor in a JSON string"}], "semanticTokenModifiers": [], "semanticTokenScopes": [{"language": "aspnetcorerazor", "scopes": {"razorComponentElement": ["entity.name.class.element.component"], "razorComponentAttribute": ["entity.name.class.attribute.component"], "razorTagHelperElement": ["entity.name.class.element.taghelper"], "razorTagHelperAttribute": ["entity.name.class.attribute.taghelper"], "razorTransition": ["keyword.control.razor.transition"], "razorDirectiveAttribute": ["keyword.control.razor.directive.attribute", "keyword.control.cshtml.directive.attribute"], "razorDirectiveColon": ["keyword.control.razor.directive.colon", "keyword.control.cshtml.directive.colon"], "razorDirective": ["keyword.control.razor.directive", "keyword.control.cshtml.directive"], "razorComment": ["comment.block.razor"], "razorCommentTransition": ["meta.comment.razor", "keyword.control.cshtml.transition"], "razorCommentStar": ["keyword.control.razor.comment.star", "meta.comment.razor"], "angleBracket": ["punctuation.definition.tag"], "forwardSlash": ["punctuation.definition.tag"], "equals": ["punctuation.separator.key-value.html"], "markupElement": ["entity.name.tag.html"], "markupAttribute": ["entity.other.attribute-name.html"], "markupAttributeQuote": ["punctuation.definition.tag.html"], "markupAttributeValue": ["punctuation.definition.entity.html"], "markupComment": ["comment.block.html"], "markupCommentPunctuation": ["punctuation.definition.comment.html", "comment.block.html"], "markupTagDelimiter": ["punctuation.definition.tag.html"], "keyword": ["keyword.cs"], "excludedCode": ["support.other.excluded.cs"], "controlKeyword": ["keyword.control.cs"], "operatorOverloaded": ["entity.name.function.member.overload.cs"], "preprocessorText": ["meta.preprocessor.string.cs"], "punctuation": ["punctuation.cs"], "stringVerbatim": ["string.verbatim.cs"], "stringEscapeCharacter": ["constant.character.escape.cs"], "delegate": ["entity.name.type.delegate.cs"], "module": ["entity.name.type.module.cs"], "field": ["entity.name.variable.field.cs"], "constant": ["variable.other.constant"], "extensionMethod": ["entity.name.function.extension.cs"], "xmlDocCommentAttributeName": ["comment.documentation.attribute.name.cs"], "xmlDocCommentAttributeQuotes": ["comment.documentation.attribute.quotes.cs"], "xmlDocCommentAttributeValue": ["comment.documentation.attribute.value.cs"], "xmlDocCommentCDataSection": ["comment.documentation.cdata.cs"], "xmlDocCommentComment": ["comment.documentation.comment.cs"], "xmlDocCommentDelimiter": ["comment.documentation.delimiter.cs"], "xmlDocCommentEntityReference": ["comment.documentation.entityReference.cs"], "xmlDocCommentName": ["comment.documentation.name.cs"], "xmlDocCommentProcessingInstruction": ["comment.documentation.processingInstruction.cs"], "xmlDocCommentText": ["comment.documentation.cs"], "xmlLiteralAttributeName": ["entity.other.attribute-name.localname.xml"], "xmlLiteralAttributeQuotes": ["string.quoted.double.xml"], "xmlLiteralAttributeValue": ["meta.tag.xml"], "xmlLiteralCDataSection": ["string.quoted.double.xml"], "xmlLiteralComment": ["comment.block.xml"], "xmlLiteralDelimiter": ["text.xml"], "xmlLiteralEmbeddedExpression": ["meta.tag.xml"], "xmlLiteralEntityReference": ["meta.tag.xml"], "xmlLiteralName": ["entity.name.tag.localname.xml"], "xmlLiteralProcessingInstruction": ["meta.tag.xml"], "xmlLiteralText": ["text.xml"], "regexComment": ["string.regexp.comment.cs"], "regexCharacterClass": ["constant.character.character-class.regexp.cs"], "regexAnchor": ["keyword.control.anchor.regexp.cs"], "regexQuantifier": ["keyword.operator.quantifier.regexp.cs"], "regexGrouping": ["punctuation.definition.group.regexp.cs"], "regexAlternation": ["keyword.operator.or.regexp.cs"], "regexText": ["string.regexp"], "regexSelfEscapedCharacter": ["string.regexp.self-escaped-character.cs"], "regexOtherEscape": ["string.regexp.other-escape.cs"], "jsonComment": ["comment.line.double-slash.js"], "jsonNumber": ["constant.numeric.json"], "jsonString": ["string.quoted.double.json"], "jsonKeyword": ["constant.language.json"], "jsonText": ["string.quoted.double.json"], "jsonOperator": ["string.quoted.double.json"], "jsonPunctuation": ["punctuation.separator.dictionary.key-value.json"], "jsonArray": ["punctuation.definition.array.begin.json"], "jsonObject": ["punctuation.definition.dictionary.begin.json"], "jsonPropertyName": ["support.type.property-name.json"], "jsonConstructorName": ["support.type.property-name.json"]}}, {"language": "csharp", "scopes": {"typeParameter": ["entity.name.type.type-parameter"], "keyword": ["keyword.cs"], "excludedCode": ["support.other.excluded.cs"], "controlKeyword": ["keyword.control.cs"], "operatorOverloaded": ["entity.name.function.member.overload.cs"], "preprocessorText": ["meta.preprocessor.string.cs"], "punctuation": ["punctuation.cs"], "stringVerbatim": ["string.verbatim.cs"], "stringEscapeCharacter": ["constant.character.escape.cs"], "delegate": ["entity.name.type.delegate.cs"], "module": ["entity.name.type.module.cs"], "field": ["entity.name.variable.field.cs"], "constant": ["variable.other.constant"], "extensionMethod": ["entity.name.function.extension.cs"], "xmlDocCommentAttributeName": ["comment.documentation.attribute.name.cs"], "xmlDocCommentAttributeQuotes": ["comment.documentation.attribute.quotes.cs"], "xmlDocCommentAttributeValue": ["comment.documentation.attribute.value.cs"], "xmlDocCommentCDataSection": ["comment.documentation.cdata.cs"], "xmlDocCommentComment": ["comment.documentation.comment.cs"], "xmlDocCommentDelimiter": ["comment.documentation.delimiter.cs"], "xmlDocCommentEntityReference": ["comment.documentation.entityReference.cs"], "xmlDocCommentName": ["comment.documentation.name.cs"], "xmlDocCommentProcessingInstruction": ["comment.documentation.processingInstruction.cs"], "xmlDocCommentText": ["comment.documentation.cs"], "xmlLiteralAttributeName": ["entity.other.attribute-name.localname.xml"], "xmlLiteralAttributeQuotes": ["string.quoted.double.xml"], "xmlLiteralAttributeValue": ["meta.tag.xml"], "xmlLiteralCDataSection": ["string.quoted.double.xml"], "xmlLiteralComment": ["comment.block.xml"], "xmlLiteralDelimiter": ["text.xml"], "xmlLiteralEmbeddedExpression": ["meta.tag.xml"], "xmlLiteralEntityReference": ["meta.tag.xml"], "xmlLiteralName": ["entity.name.tag.localname.xml"], "xmlLiteralProcessingInstruction": ["meta.tag.xml"], "xmlLiteralText": ["text.xml"], "regexComment": ["string.regexp.comment.cs"], "regexCharacterClass": ["constant.character.character-class.regexp.cs"], "regexAnchor": ["keyword.control.anchor.regexp.cs"], "regexQuantifier": ["keyword.operator.quantifier.regexp.cs"], "regexGrouping": ["punctuation.definition.group.regexp.cs"], "regexAlternation": ["keyword.operator.or.regexp.cs"], "regexText": ["string.regexp"], "regexSelfEscapedCharacter": ["string.regexp.self-escaped-character.cs"], "regexOtherEscape": ["string.regexp.other-escape.cs"], "jsonComment": ["comment.line.double-slash.js"], "jsonNumber": ["constant.numeric.json"], "jsonString": ["string.quoted.double.json"], "jsonKeyword": ["constant.language.json"], "jsonText": ["string.quoted.double.json"], "jsonOperator": ["string.quoted.double.json"], "jsonPunctuation": ["punctuation.separator.dictionary.key-value.json"], "jsonArray": ["punctuation.definition.array.begin.json"], "jsonObject": ["punctuation.definition.dictionary.begin.json"], "jsonPropertyName": ["support.type.property-name.json"], "jsonConstructorName": ["support.type.property-name.json"]}}], "languages": [{"id": "aspnetcorerazor", "extensions": [".cshtml", ".razor"], "mimetypes": ["text/x-cshtml"], "configuration": "./src/razor/language-configuration.json", "aliases": ["ASP.NET Razor"]}, {"id": "xaml", "extensions": [".xaml"], "configuration": "./src/xaml/language-configuration.json", "aliases": ["XAML"]}], "grammars": [{"language": "aspnetcorerazor", "scopeName": "text.aspnetcorerazor", "path": "./src/razor/syntaxes/aspnetcorerazor.tmLanguage.json", "unbalancedBracketScopes": ["text.aspnetcorerazor"]}, {"language": "xaml", "scopeName": "source.xaml", "path": "./src/xaml/syntaxes/xaml.tmLanguage.json"}], "menus": {"commandPalette": [{"command": "dotnet.test.runTestsInContext", "when": "editorLangId == csharp && dotnet.server.activationContext == 'Roslyn' || dotnet.server.activationContext == 'OmniSharp'"}, {"command": "dotnet.test.debugTestsInContext", "when": "editorLangId == csharp && dotnet.server.activationContext == 'Roslyn' || dotnet.server.activationContext == 'OmniSharp'"}, {"command": "o.restart", "when": "dotnet.server.activationContext == 'OmniSharp'"}, {"command": "csharp.listProcess", "when": "false"}, {"command": "csharp.listRemoteProcess", "when": "false"}, {"command": "csharp.listRemoteDockerProcess", "when": "false"}], "editor/title": [{"command": "extension.showRazorCSharpWindow", "when": "resourceLangId == aspnetcorerazor"}, {"command": "extension.showRazorHtmlWindow", "when": "resourceLangId == aspnetcorerazor"}, {"command": "razor.reportIssue", "when": "resourceLangId == aspnetcorerazor"}], "editor/context": [{"command": "dotnet.test.runTestsInContext", "when": "editorLangId == csharp && (dotnet.server.activationContext == 'Roslyn' || dotnet.server.activationContext == 'OmniSharp')", "group": "2_dotnet@1"}, {"command": "dotnet.test.debugTestsInContext", "when": "editorLangId == csharp && (dotnet.server.activationContext == 'Roslyn' || dotnet.server.activationContext == 'OmniSharp')", "group": "2_dotnet@2"}]}, "viewsWelcome": [{"view": "debug", "contents": "[Generate C# Assets for Build and Debug](command:dotnet.generateAssets)\n\nTo learn more about launch.json, see [Configuring launch.json for C# debugging](https://aka.ms/VSCode-CS-LaunchJson).", "when": "debugStartLanguage == csharp && !dotnet.debug.serviceBrokerAvailable"}], "configurationDefaults": {"[xaml]": {"editor.wordBasedSuggestions": "off"}}}}, "location": {"$mid": 1, "path": "/Users/<USER>/.windsurf/extensions/ms-dotnettools.csharp-2.63.32-darwin-arm64", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "darwin-arm64", "publisherDisplayName": "ms-dot<PERSON><PERSON>s", "metadata": {"installedTimestamp": 1749166134985, "source": "gallery", "id": "d0bfc4ab-1d3a-4487-8782-7cf6027b4fff", "publisherId": "d05e23de-3974-4ff0-8d47-23ee77830092", "publisherDisplayName": "ms-dot<PERSON><PERSON>s", "targetPlatform": "darwin-arm64", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 604995358}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "ms-python.debugpy", "uuid": "4bd5d2c9-9d65-401a-b0b2-7498d9f17615"}, "manifest": {"name": "debugpy", "displayName": "Python Debugger", "description": "Python Debugger extension using debugpy.", "version": "2025.8.0", "publisher": "ms-python", "enabledApiProposals": ["portsAttributes", "debugVisualization", "contribViewsWelcome"], "license": "MIT", "homepage": "https://github.com/Microsoft/vscode-python-debugger", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-python-debugger.git"}, "bugs": {"url": "https://github.com/Microsoft/vscode-python-debugger/issues"}, "icon": "icon.png", "keywords": ["python", "debugger", "debugpy"], "engines": {"vscode": "^1.92.0"}, "categories": ["Debuggers"], "activationEvents": ["onDebugInitialConfigurations", "onDebugDynamicConfigurations:debugpy", "onDebugResolve:debugpy", "onLanguage:python"], "main": "./dist/extension.js", "l10n": "./l10n", "contributes": {"commands": [{"category": "Python Debugger", "command": "debugpy.debugInTerminal", "icon": "$(debug-alt)", "title": "Python Debugger: Debug Python File"}, {"category": "Python Debugger", "command": "debugpy.debugUsingLaunchConfig", "icon": "$(debug-alt)", "title": "Python Debugger: Debug using launch.json"}, {"category": "Python Debugger", "command": "debugpy.clearCacheAndReload", "title": "Clear Cache and Reload Window"}, {"category": "Python Debugger", "command": "debugpy.viewOutput", "icon": {"dark": "resources/dark/repl.svg", "light": "resources/light/repl.svg"}, "title": "Show Output"}, {"category": "Python Debugger", "command": "debugpy.reportIssue", "title": "Report Issue..."}], "menus": {"issue/reporter": [{"command": "debugpy.reportIssue"}], "commandPalette": [{"category": "Python Debugger", "command": "debugpy.clearCacheAndReload", "title": "Clear Cache and Reload Window"}, {"category": "Python Debugger", "command": "debugpy.debugInTerminal", "icon": "$(debug-alt)", "title": "Python Debugger: Debug Python File", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python Debugger", "command": "debugpy.debugUsingLaunchConfig", "icon": "$(debug-alt)", "title": "Python Debugger: Debug using launch.json", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python Debugger", "command": "debugpy.viewOutput", "title": "Show Output"}, {"category": "Python Debugger", "command": "debugpy.reportIssue", "title": "Report Issue...", "when": "!virtualWorkspace && shellExecutionSupported"}], "editor/title/run": [{"command": "debugpy.debugInTerminal", "title": "Python Debugger: Debug Python File", "when": "resourceLangId == python && !isInDiffEditor && !virtualWorkspace && shellExecutionSupported"}, {"command": "debugpy.debugUsingLaunchConfig", "title": "Python Debugger: Debug using launch.json", "when": "resourceLangId == python && !isInDiffEditor && !virtualWorkspace && shellExecutionSupported"}]}, "configuration": {"properties": {"debugpy.debugJustMyCode": {"default": true, "description": "When debugging only step through user-written code. Disable this to allow stepping into library code.", "scope": "resource", "type": "boolean"}, "debugpy.showPythonInlineValues": {"default": false, "description": "Whether to display inline values in the editor while debugging.", "scope": "resource", "type": "boolean", "tags": ["experimental"]}}, "title": "Python Debugger", "type": "object"}, "debuggers": [{"configurationAttributes": {"attach": {"properties": {"autoReload": {"default": {}, "description": "Configures automatic reload of code on edit.", "properties": {"enable": {"default": false, "description": "Automatically reload code on edit.", "type": "boolean"}, "exclude": {"default": ["**/.git/**", "**/.metadata/**", "**/__pycache__/**", "**/node_modules/**", "**/site-packages/**"], "description": "Glob patterns of paths to exclude from auto reload.", "items": {"type": "string"}, "type": "array"}, "include": {"default": ["**/*.py", "**/*.pyw"], "description": "Glob patterns of paths to include in auto reload.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "connect": {"label": "Attach by connecting to debugpy over a socket.", "properties": {"host": {"default": "127.0.0.1", "description": "Hostname or IP address to connect to.", "type": "string"}, "port": {"description": "Port to connect to.", "type": ["number", "string"]}}, "required": ["port"], "type": "object"}, "debugAdapterPath": {"description": "Path (fully qualified) to the python debug adapter executable.", "type": "string"}, "django": {"default": false, "description": "Django debugging.", "type": "boolean"}, "jinja": {"default": null, "description": "Jinja template debugging (e.g. Flask).", "enum": [false, null, true]}, "justMyCode": {"default": true, "description": "If true, show and debug only user-written code. If false, show and debug all code, including library calls.", "type": "boolean"}, "listen": {"label": "Attach by listening for incoming socket connection from debugpy", "properties": {"host": {"default": "127.0.0.1", "description": "Hostname or IP address of the interface to listen on.", "type": "string"}, "port": {"description": "Port to listen on.", "type": ["number", "string"]}}, "required": ["port"], "type": "object"}, "logToFile": {"default": false, "description": "Enable logging of debugger events to a log file. This file can be found in the debugpy extension install folder.", "type": "boolean"}, "pathMappings": {"default": [], "items": {"label": "Path mapping", "properties": {"localRoot": {"default": "${workspaceFolder}", "label": "Local source root.", "type": "string"}, "remoteRoot": {"default": "", "label": "Remote source root.", "type": "string"}}, "required": ["localRoot", "remoteRoot"], "type": "object"}, "label": "Path mappings.", "type": "array"}, "processId": {"anyOf": [{"default": "${command:pickProcess}", "description": "Use process picker to select a process to attach, or Process ID as integer.", "enum": ["${command:pickProcess}"]}, {"description": "ID of the local process to attach to.", "type": "integer"}]}, "redirectOutput": {"default": true, "description": "Redirect output.", "type": "boolean"}, "showReturnValue": {"default": true, "description": "Show return value of functions when stepping.", "type": "boolean"}, "subProcess": {"default": false, "description": "Whether to enable Sub Process debugging", "type": "boolean"}, "consoleName": {"default": "Python Debug Console", "description": "Display name of the debug console or terminal", "type": "string"}, "clientOS": {"default": null, "description": "OS that VS code is using.", "enum": ["windows", null, "unix"]}}}, "launch": {"properties": {"args": {"default": [], "description": "Command line arguments passed to the program. For string type arguments, it will pass through the shell as is, and therefore all shell variable expansions will apply. But for the array type, the values will be shell-escaped.", "items": {"type": "string"}, "anyOf": [{"default": "${command:pickArgs}", "enum": ["${command:pickArgs}"]}, {"type": ["array", "string"]}]}, "autoReload": {"default": {}, "description": "Configures automatic reload of code on edit.", "properties": {"enable": {"default": false, "description": "Automatically reload code on edit.", "type": "boolean"}, "exclude": {"default": ["**/.git/**", "**/.metadata/**", "**/__pycache__/**", "**/node_modules/**", "**/site-packages/**"], "description": "Glob patterns of paths to exclude from auto reload.", "items": {"type": "string"}, "type": "array"}, "include": {"default": ["**/*.py", "**/*.pyw"], "description": "Glob patterns of paths to include in auto reload.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "console": {"default": "integratedTerminal", "description": "Where to launch the debug target: internal console, integrated terminal, or external terminal.", "enum": ["externalTerminal", "integratedTerminal", "internalConsole"]}, "cwd": {"default": "${workspaceFolder}", "description": "Absolute path to the working directory of the program being debugged. Default is the root directory of the file (leave empty).", "type": "string"}, "debugAdapterPath": {"description": "Path (fully qualified) to the Python debug adapter executable.", "type": "string"}, "autoStartBrowser": {"default": false, "description": "Open external browser to launch the application", "type": "boolean"}, "django": {"default": false, "description": "Django debugging.", "type": "boolean"}, "env": {"additionalProperties": {"type": "string"}, "default": {}, "description": "Environment variables defined as a key value pair. Property ends up being the Environment Variable and the value of the property ends up being the value of the Env Variable.", "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "Absolute path to a file containing environment variable definitions.", "type": "string"}, "gevent": {"default": false, "description": "Enable debugging of gevent monkey-patched code.", "type": "boolean"}, "jinja": {"default": null, "description": "Jinja template debugging (e.g. Flask).", "enum": [false, null, true]}, "justMyCode": {"default": true, "description": "Debug only user-written code.", "type": "boolean"}, "logToFile": {"default": false, "description": "Enable logging of debugger events to a log file. This file can be found in the debugpy extension install folder.", "type": "boolean"}, "module": {"default": "", "description": "Name of the module to be debugged.", "type": "string"}, "pathMappings": {"default": [], "items": {"label": "Path mapping", "properties": {"localRoot": {"default": "${workspaceFolder}", "label": "Local source root.", "type": "string"}, "remoteRoot": {"default": "", "label": "Remote source root.", "type": "string"}}, "required": ["localRoot", "remoteRoot"], "type": "object"}, "label": "Path mappings.", "type": "array"}, "program": {"default": "${file}", "description": "Absolute path to the program.", "type": "string"}, "purpose": {"default": [], "description": "Tells extension to use this configuration for test debugging, or when using debug-in-terminal command.", "items": {"enum": ["debug-test", "debug-in-terminal"], "enumDescriptions": ["Use this configuration while debugging tests using test view or test debug commands.", "Use this configuration while debugging a file using debug in terminal button in the editor."]}, "type": "array"}, "pyramid": {"default": false, "description": "Whether debugging Pyramid applications.", "type": "boolean"}, "python": {"default": "${command:python.interpreterPath}", "description": "Absolute path to the Python interpreter executable; overrides workspace configuration if set.", "type": "string"}, "pythonArgs": {"default": [], "description": "Command-line arguments passed to the Python interpreter. To pass arguments to the debug target, use \"args\".", "items": {"type": "string"}, "type": "array"}, "redirectOutput": {"default": true, "description": "Redirect output.", "type": "boolean"}, "showReturnValue": {"default": true, "description": "Show return value of functions when stepping.", "type": "boolean"}, "stopOnEntry": {"default": false, "description": "Automatically stop after launch.", "type": "boolean"}, "subProcess": {"default": false, "description": "Whether to enable Sub Process debugging.", "type": "boolean"}, "sudo": {"default": false, "description": "Running debug program under elevated permissions (on Unix).", "type": "boolean"}, "guiEventLoop": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "The GUI event loop that's going to run. Possible values: \"matplotlib\", \"wx\", \"qt\", \"none\", or a custom function that'll be imported and run.", "type": "string"}, "consoleName": {"default": "Python Debug Console", "description": "Display name of the debug console or terminal", "type": "string"}, "clientOS": {"default": null, "description": "OS that VS code is using.", "enum": ["windows", null, "unix"]}}}}, "configurationSnippets": [], "label": "Python Debugger", "languages": ["python"], "type": "debugpy", "variables": {"pickProcess": "debugpy.pickLocalProcess", "pickArgs": "debugpy.pickArgs"}, "when": "!virtualWorkspace && shellExecutionSupported"}], "debugVisualizers": [{"id": "inlineHexDecoder", "when": "debugConfigurationType == 'debugpy' && (variableType == 'float' || variableType == 'int')"}], "viewsWelcome": [{"view": "debug", "contents": "\n[Show automatic Python configurations](command:workbench.action.debug.selectandstart?%5B%22debugpy%22%5D)\n", "when": "dynamicPythonConfigAvailable && !virtualWorkspace"}]}, "extensionDependencies": ["ms-python.python"], "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "format-check": "prettier --check 'src/**/*.ts' 'build/**/*.yml' '.github/**/*.yml'", "format-fix": "prettier --write 'src/**/*.ts' 'build/**/*.yml' '.github/**/*.yml'", "test": "node ./out/test/runTest.js", "vsce-package": "npx @vscode/vsce package -o python-debugger.vsix"}, "devDependencies": {"@types/chai": "^4.3.4", "@types/chai-as-promised": "^7.1.8", "@types/fs-extra": "^11.0.4", "@types/glob": "^7.2.0", "@types/lodash": "^4.14.191", "@types/mocha": "^10.0.7", "@types/node": "18.x", "@types/semver": "^7.3.13", "@types/sinon": "^10.0.13", "@types/vscode": "^1.87.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vscode/test-electron": "^2.3.9", "@vscode/vsce": "^2.24.0", "chai": "^4.3.7", "chai-as-promised": "^7.1.1", "eslint": "^8.50.0", "glob": "^8.0.3", "mocha": "^10.7.0", "prettier": "^3.0.3", "semver": "^7.5.4", "sinon": "^15.0.2", "ts-loader": "^9.3.1", "ts-mockito": "^2.6.1", "typemoq": "^2.1.0", "typescript": "^5.5.4", "webpack": "^5.87.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@vscode/debugadapter": "^1.65.0", "@vscode/debugprotocol": "^1.65.0", "@vscode/extension-telemetry": "^0.8.5", "@vscode/python-extension": "^1.0.5", "fs-extra": "^11.2.0", "iconv-lite": "^0.6.3", "jsonc-parser": "^3.2.0", "lodash": "^4.17.21", "vscode-languageclient": "^8.0.2"}}, "location": {"$mid": 1, "path": "/Users/<USER>/.windsurf/extensions/ms-python.debugpy-2025.8.0-darwin-arm64", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "darwin-arm64", "publisherDisplayName": "ms-python", "metadata": {"installedTimestamp": 1749180719373, "source": "gallery", "id": "4bd5d2c9-9d65-401a-b0b2-7498d9f17615", "publisherId": "998b010b-e2af-44a5-a6cd-0b5fd3b9b6f8", "publisherDisplayName": "ms-python", "targetPlatform": "darwin-arm64", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 21476220}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "ms-python.python", "uuid": "f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5"}, "manifest": {"name": "python", "displayName": "Python", "description": "Python language support with extension access points for IntelliSense (Pylance), Debugging (Python Debugger), linting, formatting, refactoring, unit tests, and more.", "version": "2025.6.1", "featureFlags": {"usingNewInterpreterStorage": true}, "capabilities": {"untrustedWorkspaces": {"supported": "limited", "description": "Only Partial IntelliSense with Pylance is supported. Cannot execute Python with untrusted files."}, "virtualWorkspaces": {"supported": "limited", "description": "Only Partial IntelliSense supported."}}, "publisher": "ms-python", "enabledApiProposals": ["contribEditorContentMenu", "quickPickSortByLabel", "testObserver", "quickPickItemTooltip", "terminalDataWriteEvent", "terminalExecuteCommandEvent", "codeActionAI", "notebookReplDocument", "notebookVariableProvider"], "author": {"name": "Microsoft Corporation"}, "license": "MIT", "homepage": "https://github.com/Microsoft/vscode-python", "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-python"}, "bugs": {"url": "https://github.com/Microsoft/vscode-python/issues"}, "qna": "https://github.com/microsoft/vscode-python/discussions/categories/q-a", "icon": "icon.png", "galleryBanner": {"color": "#1e415e", "theme": "dark"}, "engines": {"vscode": "^1.94.0-20240918"}, "enableTelemetry": true, "keywords": ["python", "django", "unittest", "multi-root ready"], "categories": ["Programming Languages", "Debuggers", "Other", "Data Science", "Machine Learning"], "activationEvents": ["onDebugInitialConfigurations", "onLanguage:python", "onDebugResolve:python", "onCommand:python.copilotSetupTests", "workspaceContains:mspythonconfig.json", "workspaceContains:pyproject.toml", "workspaceContains:Pipfile", "workspaceContains:setup.py", "workspaceContains:requirements.txt", "workspaceContains:manage.py", "workspaceContains:app.py", "workspaceContains:.venv", "workspaceContains:.conda"], "main": "./out/client/extension", "browser": "./dist/extension.browser.js", "l10n": "./l10n", "contributes": {"problemMatchers": [{"name": "python", "owner": "python", "source": "python", "fileLocation": "autoDetect", "pattern": [{"regexp": "^.*File \\\"([^\\\"]|.*)\\\", line (\\d+).*", "file": 1, "line": 2}, {"regexp": "^\\s*(.*)\\s*$"}, {"regexp": "^\\s*(.*Error.*)$", "message": 1}]}], "walkthroughs": [{"id": "pythonWelcome", "title": "Get Started with Python Development", "description": "Your first steps to set up a Python project with all the powerful tools and features that the Python extension has to offer!", "when": "workspacePlatform != webworker", "steps": [{"id": "python.createPythonFolder", "title": "Open a Python project folder", "description": "[Open](command:workbench.action.files.openFolder) or create a project folder.\n[Open Project Folder](command:workbench.action.files.openFolder)", "media": {"svg": "resources/walkthrough/open-folder.svg", "altText": "Open a Python file or a folder with a Python project."}, "when": "workspaceFolderCount = 0"}, {"id": "python.createPythonFile", "title": "Create a Python file", "description": "[Open](command:toSide:workbench.action.files.openFile) or [create](command:toSide:workbench.action.files.newUntitledFile?%7B%22languageId%22%3A%22python%22%7D) a Python file - make sure to save it as \".py\".\n[Create Python File](command:toSide:workbench.action.files.newUntitledFile?%7B%22languageId%22%3A%22python%22%7D)", "media": {"svg": "resources/walkthrough/open-folder.svg", "altText": "Open a Python file or a folder with a Python project."}}, {"id": "python.installPythonWin8", "title": "Install Python", "description": "The Python Extension requires Python to be installed. Install Python [from python.org](https://www.python.org/downloads).\n\n[Install Python](https://www.python.org/downloads)\n", "media": {"markdown": "resources/walkthrough/install-python-windows-8.md"}, "when": "workspacePlatform == windows && showInstallPythonTile"}, {"id": "python.installPythonMac", "title": "Install Python", "description": "The Python Extension requires Python to be installed. Install Python 3 through the terminal.\n[Install Python via <PERSON><PERSON>](command:python.installPythonOnMac)\n", "media": {"markdown": "resources/walkthrough/install-python-macos.md"}, "when": "workspacePlatform == mac && showInstallPythonTile", "command": "workbench.action.terminal.new"}, {"id": "python.installPythonLinux", "title": "Install Python", "description": "The Python Extension requires Python to be installed. Install Python 3 through the terminal.\n[Install Python via terminal](command:python.installPythonOnLinux)\n", "media": {"markdown": "resources/walkthrough/install-python-linux.md"}, "when": "workspacePlatform == linux && showInstallPythonTile", "command": "workbench.action.terminal.new"}, {"id": "python.createEnvironment", "title": "Select or create a Python environment", "description": "Create an environment for your Python project or use [Select Python Interpreter](command:python.setInterpreter) to select an existing one.\n[Create Environment](command:python.createEnvironment)\n**Tip**: Run the ``Python: Create Environment`` command in the [Command Palette](command:workbench.action.showCommands).", "media": {"svg": "resources/walkthrough/create-environment.svg", "altText": "Creating a Python environment from the Command Palette"}}, {"id": "python.runAndDebug", "title": "Run and debug your Python file", "description": "Open your Python file  and click on the play button on the top right of the editor, or press F5 when on the file and select \"Python File\" to run with the debugger. \n  \n[Learn more](https://code.visualstudio.com/docs/python/python-tutorial#_run-hello-world)", "media": {"svg": "resources/walkthrough/rundebug2.svg", "altText": "How to run and debug in VS Code with F5 or the play button on the top right."}}, {"id": "python.learnMoreWithDS", "title": "Keep exploring!", "description": "🎨 Explore all the features the Python extension has to offer by looking for \"Python\" in the [Command Palette](command:workbench.action.showCommands). \n 📈 Learn more about getting started with [data science](command:workbench.action.openWalkthrough?%7B%22category%22%3A%22ms-python.python%23pythonDataScienceWelcome%22%2C%22step%22%3A%22ms-python.python%23python.createNewNotebook%22%7D) in Python. \n ✨ Take a look at our [Release Notes](https://aka.ms/AA8dxtb) to learn more about the latest features. \n \n[Follow along with the Python Tutorial](https://aka.ms/AA8dqti)", "media": {"altText": "Image representing our documentation page and mailing list resources.", "svg": "resources/walkthrough/learnmore.svg"}}]}, {"id": "pythonDataScienceWelcome", "title": "Get Started with Python for Data Science", "description": "Your first steps to getting started with a Data Science project with Python!", "when": "false", "steps": [{"id": "python.installJupyterExt", "title": "Install Jupyter extension", "description": "If you haven't already, install the [<PERSON><PERSON><PERSON> extension](command:workbench.extensions.search?\"ms-toolsai.jupyter\") to take full advantage of notebooks experiences in VS Code!\n \n[Search Jupyter extension](command:workbench.extensions.search?\"ms-toolsai.jupyter\")", "media": {"svg": "resources/walkthrough/data-science.svg", "altText": "Creating a new <PERSON><PERSON><PERSON> notebook"}}, {"id": "python.createNewNotebook", "title": "Create or open a Jupyter Notebook", "description": "Right click in the file explorer and create a new file with an .ipynb extension. Or, open the [Command Palette](command:workbench.action.showCommands) and run the command \n``Jupyter: Create New Blank Notebook``.\n[Create new Jupyter Notebook](command:toSide:jupyter.createnewnotebook)\n If you have an existing project, you can also [open a folder](command:workbench.action.files.openFolder) and/or clone a project from GitHub: [clone a Git repository](command:git.clone).", "media": {"svg": "resources/walkthrough/create-notebook.svg", "altText": "Creating a new <PERSON><PERSON><PERSON> notebook"}, "completionEvents": ["onCommand:jupyter.createnewnotebook", "onCommand:workbench.action.files.openFolder", "onCommand:workbench.action.files.openFileFolder"]}, {"id": "python.openInteractiveWindow", "title": "Open the Python Interactive Window", "description": "The Python Interactive Window is a Python shell where you can execute and view the results of your Python code. You can create cells on a Python file by typing ``#%%``.\n \nTo open the interactive window anytime, open the [Command Palette](command:workbench.action.showCommands) and run the command \n``Jupyter: Create Interactive Window``.\n[Open Interactive Window](command:jupyter.createnewinteractive)", "media": {"svg": "resources/walkthrough/interactive-window.svg", "altText": "Opening Python interactive window"}, "completionEvents": ["onCommand:jupyter.createnewinteractive"]}, {"id": "python.dataScienceLearnMore", "title": "Find out more!", "description": "📒 Take a look into the [<PERSON><PERSON><PERSON> extension](command:workbench.extensions.search?\"ms-toolsai.jupyter\") features, by looking for \"Jupyter\" in the [Command Palette](command:workbench.action.showCommands). \n 🏃🏻 Find out more features in our [Tutorials](https://aka.ms/AAdjzpd).  \n[Learn more](https://aka.ms/AAdar6q)", "media": {"svg": "resources/walkthrough/learnmore.svg", "altText": "Image representing our documentation page and mailing list resources."}}]}], "breakpoints": [{"language": "html"}, {"language": "jinja"}, {"language": "python"}, {"language": "django-html"}, {"language": "django-txt"}], "commands": [{"title": "New Python File", "shortTitle": "Python File", "category": "Python", "command": "python.createNewFile"}, {"category": "Python", "command": "python.analysis.restartLanguageServer", "title": "Restart Language Server"}, {"category": "Python", "command": "python.clearCacheAndReload", "title": "Clear Cache and Reload Window"}, {"category": "Python", "command": "python.clearWorkspaceInterpreter", "title": "Clear Workspace Interpreter Setting"}, {"category": "Python", "command": "python.configureTests", "title": "Configure Tests"}, {"category": "Python", "command": "python.createTerminal", "title": "Create Terminal"}, {"category": "Python", "command": "python.createEnvironment", "title": "Create Environment..."}, {"category": "Python", "command": "python.createEnvironment-button", "title": "Create Environment..."}, {"category": "Python", "command": "python.execInTerminal", "title": "Run Python File in Terminal"}, {"category": "Python", "command": "python.execInTerminal-icon", "icon": "$(play)", "title": "Run Python File"}, {"category": "Python", "command": "python.execInDedicatedTerminal", "icon": "$(play)", "title": "Run Python File in Dedicated Terminal"}, {"category": "Python", "command": "python.execSelectionInDjangoShell", "title": "Run Selection/Line in Django Shell"}, {"category": "Python", "command": "python.execSelectionInTerminal", "title": "Run Selection/Line in Python Terminal"}, {"category": "Python", "command": "python.execInREPL", "title": "Run Selection/Line in Native Python REPL"}, {"category": "Python", "command": "python.reportIssue", "title": "Report Issue..."}, {"category": "Test", "command": "testing.reRunFailTests", "icon": "$(run-errors)", "title": "<PERSON><PERSON> Failed Tests"}, {"category": "Python", "command": "python.setInterpreter", "title": "Select Interpreter"}, {"category": "Python", "command": "python.startREPL", "title": "Start Terminal REPL"}, {"category": "Python", "command": "python.startNativeREPL", "title": "Start Native Python REPL"}, {"category": "Python", "command": "python.viewLanguageServerOutput", "enablement": "python.hasLanguageServerOutputChannel", "title": "Show Language Server Output"}, {"category": "Python", "command": "python.viewOutput", "icon": {"dark": "resources/dark/repl.svg", "light": "resources/light/repl.svg"}, "title": "Show Output"}, {"category": "Python", "command": "python.installJupyter", "title": "Install the Jupyter extension"}], "configuration": {"properties": {"python.activeStateToolPath": {"default": "state", "description": "Path to the State Tool executable for ActiveState runtimes (version 0.36+).", "scope": "machine-overridable", "type": "string"}, "python.autoComplete.extraPaths": {"default": [], "description": "List of paths to libraries and the like that need to be imported by auto complete engine. E.g. when using Google App SDK, the paths are not in system path, hence need to be added into this list.", "scope": "resource", "type": "array", "uniqueItems": true}, "python.createEnvironment.contentButton": {"default": "hide", "markdownDescription": "Show or hide Create Environment button in the editor for `requirements.txt` or other dependency files.", "scope": "machine-overridable", "type": "string", "enum": ["show", "hide"]}, "python.createEnvironment.trigger": {"default": "prompt", "markdownDescription": "Detect if environment creation is required for the current project", "scope": "machine-overridable", "type": "string", "enum": ["off", "prompt"]}, "python.condaPath": {"default": "", "description": "Path to the conda executable to use for activation (version 4.4+).", "scope": "machine", "type": "string"}, "python.defaultInterpreterPath": {"default": "python", "markdownDescription": "Path to default Python to use when extension loads up for the first time, no longer used once an interpreter is selected for the workspace. See [here](https://aka.ms/AAfekmf) to understand when this is used", "scope": "machine-overridable", "type": "string"}, "python.envFile": {"default": "${workspaceFolder}/.env", "description": "Absolute path to a file containing environment variable definitions.", "scope": "resource", "type": "string"}, "python.experiments.enabled": {"default": true, "description": "Enables A/B tests experiments in the Python extension. If enabled, you may get included in proposed enhancements and/or features.", "scope": "window", "type": "boolean"}, "python.experiments.optInto": {"default": [], "markdownDescription": "List of experiments to opt into. If empty, user is assigned the default experiment groups. See [here](https://github.com/microsoft/vscode-python/wiki/AB-Experiments) for more details.", "items": {"enum": ["All", "pythonSurveyNotification", "pythonPromptNewToolsExt", "pythonTerminalEnvVarActivation", "pythonDiscoveryUsingWorkers", "pythonTestAdapter"], "enumDescriptions": ["Combined list of all experiments.", "Denotes the Python Survey Notification experiment.", "Denotes the Python Prompt New Tools Extension experiment.", "Enables use of environment variables to activate terminals instead of sending activation commands.", "Enables use of worker threads to do heavy computation when discovering interpreters.", "Denotes the Python Test Adapter experiment."]}, "scope": "window", "type": "array", "uniqueItems": true}, "python.experiments.optOutFrom": {"default": [], "markdownDescription": "List of experiments to opt out of. If empty, user is assigned the default experiment groups. See [here](https://github.com/microsoft/vscode-python/wiki/AB-Experiments) for more details.", "items": {"enum": ["All", "pythonSurveyNotification", "pythonPromptNewToolsExt", "pythonTerminalEnvVarActivation", "pythonDiscoveryUsingWorkers", "pythonTestAdapter"], "enumDescriptions": ["Combined list of all experiments.", "Denotes the Python Survey Notification experiment.", "Denotes the Python Prompt New Tools Extension experiment.", "Enables use of environment variables to activate terminals instead of sending activation commands.", "Enables use of worker threads to do heavy computation when discovering interpreters.", "Denotes the Python Test Adapter experiment."]}, "scope": "window", "type": "array", "uniqueItems": true}, "python.globalModuleInstallation": {"default": false, "description": "Whether to install Python modules globally when not using an environment.", "scope": "resource", "type": "boolean"}, "python.languageServer": {"default": "<PERSON><PERSON><PERSON>", "description": "Defines type of the language server.", "enum": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "None"], "enumDescriptions": ["Automatically select a language server: <PERSON><PERSON>nce if installed and available, otherwise fallback to Jedi.", "Use Jedi behind the Language Server Protocol (LSP) as a language server.", "Use Pylance as a language server.", "Disable language server capabilities."], "scope": "window", "type": "string"}, "python.interpreter.infoVisibility": {"default": "onPythonRelated", "description": "Controls when to display information of selected interpreter in the status bar.", "enum": ["never", "onPythonRelated", "always"], "enumDescriptions": ["Never display information.", "Only display information if Python-related files are opened.", "Always display information."], "scope": "machine", "type": "string"}, "python.logging.level": {"default": "error", "deprecationMessage": "This setting is deprecated. Please use command `Developer: Set Log Level...` to set logging level.", "description": "The logging level the extension logs at, defaults to 'error'", "enum": ["debug", "error", "info", "off", "warn"], "scope": "machine", "type": "string"}, "python.missingPackage.severity": {"default": "Hint", "description": "Set severity of missing packages in requirements.txt or pyproject.toml", "enum": ["Error", "Hint", "Information", "Warning"], "scope": "resource", "type": "string"}, "python.locator": {"default": "js", "description": "[Experimental] Select implementation of environment locators. This is an experimental setting while we test native environment location.", "enum": ["js", "native"], "tags": ["onExP", "preview"], "scope": "machine", "type": "string"}, "python.pipenvPath": {"default": "pipenv", "description": "Path to the pipenv executable to use for activation.", "scope": "machine-overridable", "type": "string"}, "python.poetryPath": {"default": "poetry", "description": "Path to the poetry executable.", "scope": "machine-overridable", "type": "string"}, "python.pixiToolPath": {"default": "pixi", "description": "Path to the pixi executable.", "scope": "machine-overridable", "type": "string"}, "python.terminal.activateEnvInCurrentTerminal": {"default": false, "description": "Activate Python Environment in the current Terminal on load of the Extension.", "scope": "resource", "type": "boolean"}, "python.terminal.activateEnvironment": {"default": true, "description": "Activate Python Environment in all Terminals created.", "scope": "resource", "type": "boolean"}, "python.terminal.executeInFileDir": {"default": false, "description": "When executing a file in the terminal, whether to use execute in the file's directory, instead of the current open folder.", "scope": "resource", "type": "boolean"}, "python.terminal.focusAfterLaunch": {"default": false, "description": "When launching a python terminal, whether to focus the cursor on the terminal.", "scope": "resource", "type": "boolean"}, "python.terminal.launchArgs": {"default": [], "description": "Python launch arguments to use when executing a file in the terminal.", "scope": "resource", "type": "array"}, "python.terminal.shellIntegration.enabled": {"default": false, "markdownDescription": "Enable [shell integration](https://code.visualstudio.com/docs/terminal/shell-integration) for the terminals running python. Shell integration enhances the terminal experience by enabling command decorations, run recent command, improving accessibility among other things.", "scope": "resource", "type": "boolean", "tags": ["preview"]}, "python.REPL.enableREPLSmartSend": {"default": true, "description": "Toggle Smart Send for the Python REPL. Smart Send enables sending the smallest runnable block of code to the REPL on Shift+Enter and moves the cursor accordingly.", "scope": "resource", "type": "boolean"}, "python.REPL.sendToNativeREPL": {"default": false, "description": "Toggle to send code to Python REPL instead of the terminal on execution. Turning this on will change the behavior for both Smart Send and Run Selection/Line in the Context Menu.", "scope": "resource", "type": "boolean"}, "python.REPL.provideVariables": {"default": true, "description": "Toggle to provide variables for the REPL variable view for the native REPL.", "scope": "resource", "type": "boolean"}, "python.testing.autoTestDiscoverOnSaveEnabled": {"default": true, "description": "Enable auto run test discovery when saving a test file.", "scope": "resource", "type": "boolean"}, "python.testing.autoTestDiscoverOnSavePattern": {"default": "**/*.py", "description": "Glob pattern used to determine which files are used by autoTestDiscoverOnSaveEnabled.", "scope": "resource", "type": "string"}, "python.testing.cwd": {"default": null, "description": "Optional working directory for tests.", "scope": "resource", "type": "string"}, "python.testing.debugPort": {"default": 3000, "description": "Port number used for debugging of tests.", "scope": "resource", "type": "number"}, "python.testing.promptToConfigure": {"default": true, "description": "Prompt to configure a test framework if potential tests directories are discovered.", "scope": "resource", "type": "boolean"}, "python.testing.pytestArgs": {"default": [], "description": "Arguments passed in. Each argument is a separate item in the array.", "items": {"type": "string"}, "scope": "resource", "type": "array"}, "python.testing.pytestEnabled": {"default": false, "description": "Enable testing using pytest.", "scope": "resource", "type": "boolean"}, "python.testing.pytestPath": {"default": "pytest", "description": "Path to pytest. You can use a custom version of pytest by modifying this setting to include the full path.", "scope": "machine-overridable", "type": "string"}, "python.testing.unittestArgs": {"default": ["-v", "-s", ".", "-p", "*test*.py"], "description": "Arguments passed in. Each argument is a separate item in the array.", "items": {"type": "string"}, "scope": "resource", "type": "array"}, "python.testing.unittestEnabled": {"default": false, "description": "Enable testing using unittest.", "scope": "resource", "type": "boolean"}, "python.venvFolders": {"default": [], "description": "Folders in your home directory to look into for virtual environments (supports pyenv, direnv and virtualenvwrapper by default).", "items": {"type": "string"}, "scope": "machine", "type": "array", "uniqueItems": true}, "python.venvPath": {"default": "", "description": "Path to folder with a list of Virtual Environments (e.g. ~/.pyenv, ~/Envs, ~/.virtualenvs).", "scope": "machine", "type": "string"}}, "title": "Python", "type": "object"}, "debuggers": [{"configurationAttributes": {"attach": {"properties": {"connect": {"label": "Attach by connecting to debugpy over a socket.", "properties": {"host": {"default": "127.0.0.1", "description": "Hostname or IP address to connect to.", "type": "string"}, "port": {"description": "Port to connect to.", "type": "number"}}, "required": ["port"], "type": "object"}, "debugAdapterPath": {"description": "Path (fully qualified) to the python debug adapter executable.", "type": "string"}, "django": {"default": false, "description": "Django debugging.", "type": "boolean"}, "host": {"default": "127.0.0.1", "description": "Hostname or IP address to connect to.", "type": "string"}, "jinja": {"default": null, "description": "Jinja template debugging (e.g. Flask).", "enum": [false, null, true]}, "justMyCode": {"default": true, "description": "If true, show and debug only user-written code. If false, show and debug all code, including library calls.", "type": "boolean"}, "listen": {"label": "Attach by listening for incoming socket connection from debugpy", "properties": {"host": {"default": "127.0.0.1", "description": "Hostname or IP address of the interface to listen on.", "type": "string"}, "port": {"description": "Port to listen on.", "type": "number"}}, "required": ["port"], "type": "object"}, "logToFile": {"default": false, "description": "Enable logging of debugger events to a log file.", "type": "boolean"}, "pathMappings": {"default": [], "items": {"label": "Path mapping", "properties": {"localRoot": {"default": "${workspaceFolder}", "label": "Local source root.", "type": "string"}, "remoteRoot": {"default": "", "label": "Remote source root.", "type": "string"}}, "required": ["localRoot", "remoteRoot"], "type": "object"}, "label": "Path mappings.", "type": "array"}, "port": {"description": "Port to connect to.", "type": "number"}, "processId": {"anyOf": [{"default": "${command:pickProcess}", "description": "Use process picker to select a process to attach, or Process ID as integer.", "enum": ["${command:pickProcess}"]}, {"description": "ID of the local process to attach to.", "type": "integer"}]}, "redirectOutput": {"default": true, "description": "Redirect output.", "type": "boolean"}, "showReturnValue": {"default": true, "description": "Show return value of functions when stepping.", "type": "boolean"}, "subProcess": {"default": false, "description": "Whether to enable Sub Process debugging", "type": "boolean"}}}, "launch": {"properties": {"args": {"default": [], "description": "Command line arguments passed to the program.", "items": {"type": "string"}, "type": ["array", "string"]}, "autoReload": {"default": {}, "description": "Configures automatic reload of code on edit.", "properties": {"enable": {"default": false, "description": "Automatically reload code on edit.", "type": "boolean"}, "exclude": {"default": ["**/.git/**", "**/.metadata/**", "**/__pycache__/**", "**/node_modules/**", "**/site-packages/**"], "description": "Glob patterns of paths to exclude from auto reload.", "items": {"type": "string"}, "type": "array"}, "include": {"default": ["**/*.py", "**/*.pyw"], "description": "Glob patterns of paths to include in auto reload.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "console": {"default": "integratedTerminal", "description": "Where to launch the debug target: internal console, integrated terminal, or external terminal.", "enum": ["externalTerminal", "integratedTerminal", "internalConsole"]}, "consoleTitle": {"default": "Python Debug Console", "description": "Display name of the debug console or terminal"}, "cwd": {"default": "${workspaceFolder}", "description": "Absolute path to the working directory of the program being debugged. Default is the root directory of the file (leave empty).", "type": "string"}, "debugAdapterPath": {"description": "Path (fully qualified) to the python debug adapter executable.", "type": "string"}, "django": {"default": false, "description": "Django debugging.", "type": "boolean"}, "env": {"additionalProperties": {"type": "string"}, "default": {}, "description": "Environment variables defined as a key value pair. Property ends up being the Environment Variable and the value of the property ends up being the value of the Env Variable.", "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "Absolute path to a file containing environment variable definitions.", "type": "string"}, "gevent": {"default": false, "description": "Enable debugging of gevent monkey-patched code.", "type": "boolean"}, "host": {"default": "localhost", "description": "IP address of the of the local debug server (default is localhost).", "type": "string"}, "jinja": {"default": null, "description": "Jinja template debugging (e.g. Flask).", "enum": [false, null, true]}, "justMyCode": {"default": true, "description": "Debug only user-written code.", "type": "boolean"}, "logToFile": {"default": false, "description": "Enable logging of debugger events to a log file.", "type": "boolean"}, "module": {"default": "", "description": "Name of the module to be debugged.", "type": "string"}, "pathMappings": {"default": [], "items": {"label": "Path mapping", "properties": {"localRoot": {"default": "${workspaceFolder}", "label": "Local source root.", "type": "string"}, "remoteRoot": {"default": "", "label": "Remote source root.", "type": "string"}}, "required": ["localRoot", "remoteRoot"], "type": "object"}, "label": "Path mappings.", "type": "array"}, "port": {"default": 0, "description": "Debug port (default is 0, resulting in the use of a dynamic port).", "type": "number"}, "program": {"default": "${file}", "description": "Absolute path to the program.", "type": "string"}, "purpose": {"default": [], "description": "Tells extension to use this configuration for test debugging, or when using debug-in-terminal command.", "items": {"enum": ["debug-test", "debug-in-terminal"], "enumDescriptions": ["Use this configuration while debugging tests using test view or test debug commands.", "Use this configuration while debugging a file using debug in terminal button in the editor."]}, "type": "array"}, "pyramid": {"default": false, "description": "Whether debugging Pyramid applications", "type": "boolean"}, "python": {"default": "${command:python.interpreterPath}", "description": "Absolute path to the Python interpreter executable; overrides workspace configuration if set.", "type": "string"}, "pythonArgs": {"default": [], "description": "Command-line arguments passed to the Python interpreter. To pass arguments to the debug target, use \"args\".", "items": {"type": "string"}, "type": "array"}, "redirectOutput": {"default": true, "description": "Redirect output.", "type": "boolean"}, "showReturnValue": {"default": true, "description": "Show return value of functions when stepping.", "type": "boolean"}, "stopOnEntry": {"default": false, "description": "Automatically stop after launch.", "type": "boolean"}, "subProcess": {"default": false, "description": "Whether to enable Sub Process debugging", "type": "boolean"}, "sudo": {"default": false, "description": "Running debug program under elevated permissions (on Unix).", "type": "boolean"}}}}, "deprecated": "This configuration will be deprecated soon. Please replace `python` with `debugpy` to use the new Python Debugger extension.", "configurationSnippets": [], "label": "Python", "languages": ["python"], "type": "python", "variables": {"pickProcess": "python.pickLocalProcess"}, "when": "!virtualWorkspace && shellExecutionSupported", "hiddenWhen": "true"}], "grammars": [{"language": "pip-requirements", "path": "./syntaxes/pip-requirements.tmLanguage.json", "scopeName": "source.pip-requirements"}], "jsonValidation": [{"fileMatch": ".condarc", "url": "./schemas/condarc.json"}, {"fileMatch": "environment.yml", "url": "./schemas/conda-environment.json"}, {"fileMatch": "meta.yaml", "url": "./schemas/conda-meta.json"}], "keybindings": [{"command": "python.execSelectionInTerminal", "key": "shift+enter", "when": "editorTextFocus && editorLangId == python && !findInputFocussed && !replaceInputFocussed && !jupyter.ownsSelection && !notebookEditorFocused && !isCompositeNotebook"}, {"command": "python.execInREPL", "key": "shift+enter", "when": "config.python.REPL.sendToNativeREPL && editorLangId == python && editorTextFocus && !jupyter.ownsSelection && !notebookEditorFocused && !isCompositeNotebook"}, {"command": "python.execInREPLEnter", "key": "enter", "when": "!config.interactiveWindow.executeWithShiftEnter && isCompositeNotebook && activeEditor == 'workbench.editor.repl' && !inlineChatFocused && !notebookCellListFocused"}, {"command": "python.execInInteractiveWindowEnter", "key": "enter", "when": "!config.interactiveWindow.executeWithShiftEnter && isCompositeNotebook && activeEditor == 'workbench.editor.interactive' && !inlineChatFocused && !notebookCellListFocused"}], "languages": [{"aliases": ["<PERSON><PERSON>"], "extensions": [".j2", ".jinja2"], "id": "jinja"}, {"aliases": ["pip requirements", "requirements.txt"], "configuration": "./languages/pip-requirements.json", "filenamePatterns": ["**/*requirements*.{txt, in}", "**/*constraints*.txt", "**/requirements/*.{txt,in}", "**/constraints/*.txt"], "filenames": ["constraints.txt", "requirements.in", "requirements.txt"], "id": "pip-requirements"}, {"filenames": [".condarc"], "id": "yaml"}, {"filenames": [".flake8", ".pep8", ".pyl<PERSON><PERSON>", ".pypi<PERSON>"], "id": "ini"}, {"filenames": ["Pipfile", "poetry.lock", "uv.lock"], "id": "toml"}, {"filenames": ["Pipfile.lock"], "id": "json"}], "menus": {"issue/reporter": [{"command": "python.reportIssue"}], "commandPalette": [{"category": "Python", "command": "python.analysis.restartLanguageServer", "title": "Restart Language Server", "when": "!virtualWorkspace && shellExecutionSupported && (editorLangId == python || notebookType == jupyter-notebook)"}, {"category": "Python", "command": "python.clearCacheAndReload", "title": "Clear Cache and Reload Window", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.clearWorkspaceInterpreter", "title": "Clear Workspace Interpreter Setting", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.configureTests", "title": "Configure Tests", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.createEnvironment", "title": "Create Environment...", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.createEnvironment-button", "title": "Create Environment...", "when": "false"}, {"category": "Python", "command": "python.createTerminal", "title": "Create Terminal", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.execInTerminal", "title": "Run Python File in Terminal", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python", "command": "python.execInTerminal-icon", "icon": "$(play)", "title": "Run Python File", "when": "false"}, {"category": "Python", "command": "python.execInDedicatedTerminal", "icon": "$(play)", "title": "Run Python File in Dedicated Terminal", "when": "false"}, {"category": "Python", "command": "python.execSelectionInDjangoShell", "title": "Run Selection/Line in Django Shell", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python", "command": "python.execSelectionInTerminal", "title": "Run Selection/Line in Python Terminal", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python", "command": "python.execInREPL", "title": "Run Selection/Line in Native Python REPL", "when": "false"}, {"category": "Python", "command": "python.reportIssue", "title": "Report Issue...", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Test", "command": "testing.reRunFailTests", "icon": "$(run-errors)", "title": "<PERSON><PERSON> Failed Tests", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.setInterpreter", "title": "Select Interpreter", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.startREPL", "title": "Start Terminal REPL", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.startNativeREPL", "title": "Start Native Python REPL", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.viewLanguageServerOutput", "enablement": "python.hasLanguageServerOutputChannel", "title": "Show Language Server Output", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.viewOutput", "title": "Show Output", "when": "!virtualWorkspace && shellExecutionSupported"}], "editor/content": [{"group": "Python", "command": "python.createEnvironment-button", "when": "showCreateEnvButton && resourceLangId == pip-requirements && !virtualWorkspace && shellExecutionSupported && !inDiffEditor && !isMergeResultEditor && pythonDepsNotInstalled"}, {"group": "Python", "command": "python.createEnvironment-button", "when": "showCreateEnvButton && resourceFilename == pyproject.toml && pipInstallableToml && !virtualWorkspace && shellExecutionSupported  && !inDiffEditor && !isMergeResultEditor && pythonDepsNotInstalled"}], "editor/context": [{"submenu": "python.run", "group": "Python", "when": "editorLangId == python && !virtualWorkspace && shellExecutionSupported && isWorkspaceTrusted && !inChat  && notebookType != jupyter-notebook"}, {"submenu": "python.runFileInteractive", "group": "Jupyter2", "when": "editorLangId == python && !virtualWorkspace && shellExecutionSupported && !isJupyterInstalled && isWorkspaceTrusted && !inChat"}], "python.runFileInteractive": [{"command": "python.installJupyter", "group": "Jupyter2", "when": "resourceLangId == python && !virtualWorkspace && shellExecutionSupported"}], "python.run": [{"command": "python.execInTerminal", "group": "Python", "when": "resourceLangId == python && !virtualWorkspace && shellExecutionSupported"}, {"command": "python.execSelectionInDjangoShell", "group": "Python", "when": "editorHasSelection && editorLangId == python && python.isDjangoProject && !virtualWorkspace && shellExecutionSupported"}, {"command": "python.execSelectionInTerminal", "group": "Python", "when": "!config.python.REPL.sendToNativeREPL && editorFocus && editorLangId == python && !virtualWorkspace && shellExecutionSupported"}, {"command": "python.execInREPL", "group": "Python", "when": "editorFocus && editorLangId == python && !virtualWorkspace && shellExecutionSupported && config.python.REPL.sendToNativeREPL"}], "editor/title/run": [{"command": "python.execInTerminal-icon", "group": "navigation@0", "title": "Run Python File", "when": "resourceLangId == python && !isInDiffEditor && !virtualWorkspace && shellExecutionSupported"}, {"command": "python.execInDedicatedTerminal", "group": "navigation@0", "title": "Run Python File in Dedicated Terminal", "when": "resourceLangId == python && !isInDiffEditor && !virtualWorkspace && shellExecutionSupported"}], "explorer/context": [{"command": "python.execInTerminal", "group": "Python", "when": "resourceLangId == python && !virtualWorkspace && shellExecutionSupported"}], "file/newFile": [{"command": "python.createNewFile", "group": "file", "when": "!virtualWorkspace"}], "view/title": [{"command": "testing.reRunFailTests", "when": "view == workbench.view.testing && hasFailedTests && !virtualWorkspace && shellExecutionSupported", "group": "navigation@1"}]}, "submenus": [{"id": "python.run", "label": "Run Python", "icon": "$(play)"}, {"id": "python.runFileInteractive", "label": "Run in Interactive window"}], "viewsWelcome": [{"view": "testing", "contents": "Configure a test framework to see your tests here.\n[Configure Python Tests](command:python.configureTests)", "when": "!virtualWorkspace && shellExecutionSupported"}], "yamlValidation": [{"fileMatch": ".condarc", "url": "./schemas/condarc.json"}, {"fileMatch": "environment.yml", "url": "./schemas/conda-environment.json"}, {"fileMatch": "meta.yaml", "url": "./schemas/conda-meta.json"}]}, "copilot": {"tests": {"getSetupConfirmation": "python.copilotSetupTests"}}, "scripts": {"package": "gulp clean && gulp prePublishBundle && vsce package -o ms-python-insiders.vsix", "prePublish": "gulp clean && gulp prePublishNonBundle", "compile": "tsc -watch -p ./", "compileApi": "node ./node_modules/typescript/lib/tsc.js -b ./pythonExtensionApi/tsconfig.json", "compiled": "deemon npm run compile", "kill-compiled": "deemon --kill npm run compile", "checkDependencies": "gulp checkDependencies", "test": "node ./out/test/standardTest.js && node ./out/test/multiRootTest.js", "test:unittests": "mocha --config ./build/.mocha.unittests.json", "test:unittests:cover": "nyc --no-clean --nycrc-path ./build/.nycrc mocha --config ./build/.mocha.unittests.json", "test:functional": "mocha --require source-map-support/register --config ./build/.mocha.functional.json", "test:functional:perf": "node --inspect-brk ./node_modules/mocha/bin/_mocha --require source-map-support/register --config ./build/.mocha.functional.perf.json", "test:functional:memleak": "node --inspect-brk ./node_modules/mocha/bin/_mocha --require source-map-support/register --config ./build/.mocha.functional.json", "test:functional:cover": "nyc --no-clean --nycrc-path ./build/.nycrc mocha --require source-map-support/register --config ./build/.mocha.functional.json", "test:cover:report": "nyc --nycrc-path ./build/.nycrc  report --reporter=text --reporter=html --reporter=text-summary --reporter=cobertura", "testDebugger": "node ./out/test/testBootstrap.js ./out/test/debuggerTest.js", "testDebugger:cover": "nyc --no-clean --use-spawn-wrap --nycrc-path ./build/.nycrc --require source-map-support/register node ./out/test/debuggerTest.js", "testSingleWorkspace": "node ./out/test/testBootstrap.js ./out/test/standardTest.js", "testSingleWorkspace:cover": "nyc --no-clean --use-spawn-wrap --nycrc-path ./build/.nycrc --require source-map-support/register node ./out/test/standardTest.js", "preTestJediLSP": "node ./out/test/languageServers/jedi/lspSetup.js", "testJediLSP": "node ./out/test/languageServers/jedi/lspSetup.js && cross-env CODE_TESTS_WORKSPACE=src/test VSC_PYTHON_CI_TEST_GREP='Language Server:' node ./out/test/testBootstrap.js ./out/test/standardTest.js && node ./out/test/languageServers/jedi/lspTeardown.js", "testMultiWorkspace": "node ./out/test/testBootstrap.js ./out/test/multiRootTest.js", "testPerformance": "node ./out/test/testBootstrap.js ./out/test/performanceTest.js", "testSmoke": "cross-env INSTALL_JUPYTER_EXTENSION=true \"node ./out/test/smokeTest.js\"", "testInsiders": "cross-env VSC_PYTHON_CI_TEST_VSC_CHANNEL=insiders INSTALL_PYLANCE_EXTENSION=true TEST_FILES_SUFFIX=insiders.test CODE_TESTS_WORKSPACE=src/testMultiRootWkspc/smokeTests \"node ./out/test/standardTest.js\"", "lint-staged": "node gulpfile.js", "lint": "eslint  src build pythonExtensionApi", "lint-fix": "eslint --fix src build pythonExtensionApi gulpfile.js", "format-check": "prettier --check 'src/**/*.ts' 'build/**/*.js' '.github/**/*.yml' gulpfile.js", "format-fix": "prettier --write 'src/**/*.ts' 'build/**/*.js' '.github/**/*.yml' gulpfile.js", "clean": "gulp clean", "addExtensionPackDependencies": "gulp addExtensionPackDependencies", "updateBuildNumber": "gulp updateBuildNumber", "verifyBundle": "gulp verifyBundle", "webpack": "webpack"}, "dependencies": {"@iarna/toml": "^2.2.5", "@vscode/extension-telemetry": "^0.8.4", "arch": "^2.1.0", "fs-extra": "^11.2.0", "glob": "^7.2.0", "iconv-lite": "^0.6.3", "inversify": "^6.0.2", "jsonc-parser": "^3.0.0", "lodash": "^4.17.21", "minimatch": "^5.0.1", "named-js-regexp": "^1.3.3", "node-stream-zip": "^1.6.0", "reflect-metadata": "^0.2.2", "rxjs": "^6.5.4", "rxjs-compat": "^6.5.4", "semver": "^7.5.2", "stack-trace": "0.0.10", "sudo-prompt": "^9.2.1", "tmp": "^0.0.33", "uint64be": "^3.0.0", "unicode": "^14.0.0", "vscode-debugprotocol": "^1.28.0", "vscode-jsonrpc": "^9.0.0-next.5", "vscode-languageclient": "^10.0.0-next.12", "vscode-languageserver-protocol": "^3.17.6-next.10", "vscode-tas-client": "^0.1.84", "which": "^2.0.2", "winreg": "^1.2.4", "xml2js": "^0.5.0"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/bent": "^7.3.0", "@types/chai": "^4.1.2", "@types/chai-arrays": "^2.0.0", "@types/chai-as-promised": "^7.1.0", "@types/download": "^8.0.1", "@types/fs-extra": "^11.0.4", "@types/glob": "^7.2.0", "@types/lodash": "^4.14.104", "@types/mocha": "^9.1.0", "@types/node": "^22.5.0", "@types/semver": "^5.5.0", "@types/shortid": "^0.0.29", "@types/sinon": "^17.0.3", "@types/stack-trace": "0.0.29", "@types/tmp": "^0.0.33", "@types/vscode": "^1.93.0", "@types/which": "^2.0.1", "@types/winreg": "^1.2.30", "@types/xml2js": "^0.4.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vscode/test-electron": "^2.3.8", "@vscode/vsce": "^2.27.0", "bent": "^7.3.12", "chai": "^4.1.2", "chai-arrays": "^2.0.0", "chai-as-promised": "^7.1.1", "copy-webpack-plugin": "^9.1.0", "cross-env": "^7.0.3", "cross-spawn": "^6.0.5", "del": "^6.0.0", "download": "^8.0.0", "eslint": "^8.57.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-no-only-tests": "^3.3.0", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "expose-loader": "^3.1.0", "flat": "^5.0.2", "get-port": "^5.1.1", "gulp": "^5.0.0", "gulp-typescript": "^5.0.0", "mocha": "^11.1.0", "mocha-junit-reporter": "^2.0.2", "mocha-multi-reporters": "^1.1.7", "node-has-native-dependencies": "^1.0.2", "node-loader": "^1.0.2", "node-polyfill-webpack-plugin": "^1.1.4", "nyc": "^15.0.0", "prettier": "^2.0.2", "rewiremock": "^3.13.0", "shortid": "^2.2.8", "sinon": "^18.0.0", "source-map-support": "^0.5.12", "ts-loader": "^9.2.8", "ts-mockito": "^2.5.0", "ts-node": "^10.7.0", "tsconfig-paths-webpack-plugin": "^3.2.0", "typemoq": "^2.1.0", "typescript": "~5.2", "uuid": "^8.3.2", "webpack": "^5.76.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.9.2", "webpack-fix-default-import-plugin": "^1.0.3", "webpack-merge": "^5.8.0", "webpack-node-externals": "^3.0.0", "webpack-require-from": "^1.8.6", "worker-loader": "^3.0.8", "yargs": "^15.3.1"}, "extensionPack": ["ms-python.vscode-pylance", "ms-python.debugpy"]}, "location": {"$mid": 1, "path": "/Users/<USER>/.windsurf/extensions/ms-python.python-2025.6.1-darwin-arm64", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "darwin-arm64", "publisherDisplayName": "ms-python", "metadata": {"installedTimestamp": 1749180719373, "source": "gallery", "id": "f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5", "publisherId": "998b010b-e2af-44a5-a6cd-0b5fd3b9b6f8", "publisherDisplayName": "ms-python", "targetPlatform": "darwin-arm64", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 30120191}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "augment.vscode-augment", "uuid": "fc0e137d-e132-47ed-9455-c4636fa5b897"}, "manifest": {"name": "vscode-augment", "displayName": "Augment", "publisher": "Augment", "repository": {}, "private": true, "preview": false, "license": "https://www.augmentcode.com/terms-of-service", "description": "Augment yourself with the best AI pair programmer", "version": "0.477.2", "engines": {"vscode": "^1.82.0", "node": ">= 18.15.0", "npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "9"}, "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Snippets"], "activationEvents": ["onStartupFinished"], "icon": "icon.png", "galleryBanner": {"color": "#000000", "theme": "dark"}, "main": "./out/extension.js", "contributes": {"customEditors": [{"viewType": "rules.augment", "displayName": "Augment Rules Viewer", "selector": [{"filenamePattern": "**/.augment/rules/**/*.md"}], "priority": "default"}, {"viewType": "memories.augment", "displayName": "Augment Memories Viewer", "selector": [{"filenamePattern": "**/workspaceStorage/*/Augment.vscode-augment/Augment-Memories"}], "priority": "default"}], "configuration": [{"title": "Augment", "properties": {"augment.completions.enableAutomaticCompletions": {"type": "boolean", "order": 0, "default": true, "description": "Provide automatic inline code completions (manual code completions are always available)."}, "augment.completions.enableQuickSuggestions": {"type": "boolean", "order": 1, "default": true, "description": "Add Augment to the IntelliSense pop-up suggestions."}, "augment.completions.disableCompletionsByLanguage": {"type": "array", "order": 2, "default": ["git-commit", "scminput"], "markdownDescription": "Disable completions by [language identifiers](https://code.visualstudio.com/docs/languages/identifiers).", "items": {"type": "string"}, "uniqueItems": true}, "augment.enableEmptyFileHint": {"type": "boolean", "order": 3, "default": true, "description": "Display a hint to use Augment Chat when an empty file is open."}, "augment.conflictingCodingAssistantCheck": {"type": "boolean", "order": 4, "default": true, "description": "Check for conflicting coding assistants when starting up and installing extensions."}, "augment.advanced": {"type": "object", "order": 99999, "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for Augment access."}, "completionURL": {"type": "string", "default": "", "description": "URL of completion server."}, "completions": {"type": "object", "default": {}, "properties": {"timeoutMs": {"default": 800, "type": ["number", "null"], "description": "The default timeout for completions (in milliseconds)."}, "maxWaitMs": {"default": 1600, "type": ["number", "null"], "description": "The max timeout for completions items (in milliseconds). This allows Augment to retry completions that are cancelled due to changes in the editor."}, "addIntelliSenseSuggestions": {"default": true, "type": "boolean", "description": "Enable completions in the intellisense pop-up."}}}, "mcpServers": {"type": "array", "default": [{}], "items": {"type": "object", "properties": {"command": {"type": "string", "description": "The command to run the MCP server"}, "args": {"type": "array", "items": {"type": "string"}, "description": "Arguments to pass to the MCP server command"}, "timeoutMs": {"type": "number", "description": "Timeout in milliseconds for MCP server operations"}, "env": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Dictionary of Environment variables to set for the MCP server"}}}, "description": "List of MCP server configurations"}, "integrations": {"type": "object", "default": {}, "properties": {"atlassian": {"type": "object", "default": {}, "properties": {"serverUrl": {"type": "string", "default": "", "description": "Atlassian server URL"}, "personalApiToken": {"type": "string", "default": "", "description": "Personal API token for Atlassian"}, "username": {"type": "string", "default": "", "description": "Atlassian username"}}}, "notion": {"type": "object", "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for Notion"}}}, "linear": {"type": "object", "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for Linear"}}}, "github": {"type": "object", "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for GitHub"}}}}, "description": "Integration configurations for third-party services"}}}}}, {"title": "Next Edit", "properties": {"augment.nextEdit.enableBackgroundSuggestions": {"type": "boolean", "order": 0, "default": true, "description": "Enable Next Edit to run in the background and suggest changes in the editor."}, "augment.nextEdit.enableGlobalBackgroundSuggestions": {"type": "boolean", "order": 1, "default": false, "description": "Enable Next Edit to hint changes in files beyond the active editor tab."}, "augment.nextEdit.enableAutoApply": {"type": "boolean", "order": 2, "default": true, "description": "Automatically apply suggestions when you jump to them."}, "augment.nextEdit.showDiffInHover": {"type": "boolean", "order": 3, "default": false, "description": "Show a diff of the suggested change in the hover."}, "augment.nextEdit.highlightSuggestionsInTheEditor": {"type": "boolean", "order": 4, "default": false, "description": "Highlight all lines with a suggestion in addition to showing gutter icons and gray hint-text."}}}, {"title": "Experimental", "properties": {"augment.chat.userGuidelines": {"type": "string", "order": 5, "default": "", "description": "Edit this field on the Augment settings page."}}}], "commands": [{"command": "vscode-augment.autofixCommand", "title": "Augment: Autofix", "category": "Augment"}, {"category": "Augment", "command": "vscode-augment.internal-dv.o", "title": "View Diff"}, {"category": "Augment", "command": "vscode-augment.internal-dv.i", "title": "Code Instruction"}, {"category": "Augment", "command": "vscode-augment.internal-dv.aac", "title": "Accept All Chunks"}, {"category": "Augment", "command": "vscode-augment.internal-dv.afc", "title": "Accept Focused Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.rfc", "title": "Reject Focused Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.fpc", "title": "Focus Previous Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.fnc", "title": "Focus Next Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.c", "title": "Close Diff View"}, {"category": "Augment", "command": "vscode-augment.insertCompletion", "title": "Insert Completion"}, {"category": "Augment", "command": "vscode-augment.settings", "title": "$(gear) Edit Settings..."}, {"category": "Augment", "command": "vscode-augment.keyboard-shortcuts", "title": "$(keyboard) Edit Keyboard Shortcuts..."}, {"category": "Augment", "command": "vscode-augment.showDocs", "title": "Help", "icon": "$(question)"}, {"category": "Augment", "command": "vscode-augment.showAccountPage", "title": "Account & Billing", "icon": "$(note)"}, {"category": "Augment", "command": "vscode-augment.toggleAutomaticCompletionSetting", "title": "Toggle Automatic Completions"}, {"command": "vscode-augment.manageAccountCommunity", "title": "$(accounts-view-bar-icon) Manage Account (Community)", "when": "augment.userTier == 'community'"}, {"command": "vscode-augment.manageAccountProfessional", "title": "$(accounts-view-bar-icon) Manage Account (Self-Serve)", "when": "augment.userTier == 'professional'"}, {"command": "vscode-augment.manageAccountEnterprise", "title": "$(accounts-view-bar-icon) Manage Account (Enterprise)", "when": "augment.userTier == 'enterprise'"}, {"category": "Augment", "command": "vscode-augment.signIn", "title": "$(sign-in) Sign In"}, {"category": "Augment", "command": "vscode-augment.signOut", "title": "$(sign-out) Sign Out"}, {"category": "Augment", "command": "vscode-augment.chat.slash.fix", "title": "Fix using Augment"}, {"category": "Augment", "command": "vscode-augment.chat.slash.explain", "title": "Explain using Augment"}, {"category": "Augment", "command": "vscode-augment.chat.slash.test", "title": "Write test using Augment"}, {"category": "Augment", "command": "vscode-augment.chat.slash.document", "title": "Document using Augment"}, {"category": "Augment", "command": "vscode-augment.showHistoryPanel", "title": "$(history) Show History"}, {"category": "Augment", "command": "vscode-augment.copySessionId", "title": "Copy Session ID"}, {"category": "Augment", "command": "_vscode-augment.showSidebarWorkspaceContext", "title": "Manage Workspace Context", "icon": "$(folder-opened)"}, {"category": "Augment", "command": "_vscode-augment.showSidebarChat", "title": "Show Chat", "icon": "$(comment-discussion)"}, {"category": "Augment", "command": "vscode-augment.generateCommitMessage", "title": "Generate Commit Message with Augment", "icon": "$(sparkle)", "when": "vscode-augment.enableGenerateCommitMessage && gitOpenRepositoryCount != 0"}, {"category": "Augment", "command": "vscode-augment.showSettingsPanel", "title": "Settings", "icon": "$(settings-gear)"}, {"category": "Augment", "command": "vscode-augment.showAugmentCommands", "title": "Show Augment Commands", "icon": "$(menu)"}, {"category": "Augment", "command": "vscode-augment.focusAugmentPanel", "title": "$(layout-sidebar-left) Open Augment"}, {"category": "Augment", "command": "vscode-augment.startNewChat", "title": "Start New Chat"}, {"category": "Augment", "command": "vscode-augment.clear-recent-editing-history", "title": "Clear Recent Editing History"}, {"category": "Augment", "command": "vscode-augment.showRemoteAgentsPanel", "title": "Show Remote Agents Panel", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"category": "Augment", "command": "vscode-augment.openSshConfig", "title": "Open Augment SSH Config", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"category": "Augment", "command": "vscode-augment.next-edit.force", "title": "View Nearby Next Edit Suggestions (Forced)", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused)"}, {"category": "Augment", "command": "vscode-augment.next-edit.toggle-panel-horizontal-split", "title": "Toggle Side Panel Split", "icon": "$(split-horizontal)", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}, {"category": "Augment", "command": "vscode-augment.next-edit.update", "title": "Update Next Edit Suggestions", "icon": {"light": "media/next-edit/nextedit-update-light.svg", "dark": "media/next-edit/nextedit-update-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.loading", "title": "Updating Suggestions...", "icon": {"light": "media/next-edit/nextedit-update-loading-light.svg", "dark": "media/next-edit/nextedit-update-loading-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.disabled-no-changes", "title": "No Updates Available", "icon": {"light": "media/next-edit/nextedit-update-disabled-light.svg", "dark": "media/next-edit/nextedit-update-disabled-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.disabled-cached", "title": "Suggestions Up To Date", "icon": {"light": "media/next-edit/nextedit-update-complete-light.svg", "dark": "media/next-edit/nextedit-update-complete-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.open-panel", "title": "Open Next Edit Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "title": "Accept Suggestion"}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.accept-code-action", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptCodeAction", "title": "Accept Suggestion"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll", "title": "Accept All Suggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "title": "Reject Suggestion"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll", "title": "Reject All Suggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.dismiss", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canDismiss && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "title": "Dismiss Suggestion Highlights"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.next", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNextSmart", "title": "Go to Next Suggestion (Smart)"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.next-forward", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext", "title": "Go to Next Suggestion", "icon": {"light": "media/next-edit/right-light-enabled.svg", "dark": "media/next-edit/right-dark-enabled.svg"}}, {"category": "Augment", "command": "vscode-augment.next-edit.background.previous", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious", "title": "Go to Previous Suggestion", "icon": {"light": "media/next-edit/left-light-enabled.svg", "dark": "media/next-edit/left-dark-enabled.svg"}}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.next-forward.disabled", "title": "Go to Next Suggestion", "icon": {"light": "media/next-edit/right-light-disabled.svg", "dark": "media/next-edit/right-dark-disabled.svg"}, "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.previous.disabled", "title": "Go to Previous Suggestion", "icon": {"light": "media/next-edit/left-light-disabled.svg", "dark": "media/next-edit/left-dark-disabled.svg"}, "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.open", "title": "Augment Next Edit: View Suggestion", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.toggle-bg", "title": "Toggle Background Suggestions", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.toggle-all-highlights", "title": "Toggle Suggestion Highlights", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "when": "vscode-augment.enableNextEdit"}], "icons": {"augment-icon-simple": {"description": "Augment logo (simple)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E900"}}, "augment-icon-smile": {"description": "Augment logo (smile)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E901"}}, "augment-icon-zero": {"description": "Augment logo (zero)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E902"}}, "augment-icon-error": {"description": "Augment logo (error)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E903"}}, "augment-icon-closed-eyes": {"description": "Augment logo (closed eyes)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E904"}}, "augment-icon-dots": {"description": "Augment logo (dots)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E905"}}, "augment-kb-z": {"description": "Keyboard icon Z", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f101"}}, "augment-kb-y": {"description": "Keyboard icon Y", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f102"}}, "augment-kb-x": {"description": "Keyboard icon X", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f103"}}, "augment-kb-win": {"description": "Keyboard icon Win", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f104"}}, "augment-kb-w": {"description": "Keyboard icon W", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f105"}}, "augment-kb-v": {"description": "Keyboard icon V", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f106"}}, "augment-kb-u": {"description": "Keyboard icon U", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f107"}}, "augment-kb-tab": {"description": "Keyboard icon Tab", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f108"}}, "augment-kb-t": {"description": "Keyboard icon T", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f109"}}, "augment-kb-shift": {"description": "Keyboard icon Shift", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10a"}}, "augment-kb-semicolon": {"description": "Keyboard icon Semicolon", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10b"}}, "augment-kb-s": {"description": "Keyboard icon S", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10c"}}, "augment-kb-return": {"description": "Keyboard icon Return", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10d"}}, "augment-kb-r": {"description": "Keyboard icon R", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10e"}}, "augment-kb-q": {"description": "Keyboard icon Q", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10f"}}, "augment-kb-p": {"description": "Keyboard icon P", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f110"}}, "augment-kb-option": {"description": "Keyboard icon Option", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f111"}}, "augment-kb-o": {"description": "Keyboard icon O", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f112"}}, "augment-kb-n": {"description": "Keyboard icon N", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f113"}}, "augment-kb-meta": {"description": "Keyboard icon <PERSON><PERSON>", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f114"}}, "augment-kb-m": {"description": "Keyboard icon M", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f115"}}, "augment-kb-l": {"description": "Keyboard icon L", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f116"}}, "augment-kb-k": {"description": "Keyboard icon K", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f117"}}, "augment-kb-j": {"description": "Keyboard icon J", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f118"}}, "augment-kb-i": {"description": "Keyboard icon I", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f119"}}, "augment-kb-h": {"description": "Keyboard icon H", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11a"}}, "augment-kb-g": {"description": "Keyboard icon G", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11b"}}, "augment-kb-f": {"description": "Keyboard icon F", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11c"}}, "augment-kb-escape": {"description": "Keyboard icon Escape", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11d"}}, "augment-kb-e": {"description": "Keyboard icon E", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11e"}}, "augment-kb-delete": {"description": "Keyboard icon Delete", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11f"}}, "augment-kb-d": {"description": "Keyboard icon D", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f120"}}, "augment-kb-ctrl": {"description": "Keyboard icon Ctrl", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f121"}}, "augment-kb-control": {"description": "Keyboard icon Control", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f122"}}, "augment-kb-command": {"description": "Keyboard icon Command", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f123"}}, "augment-kb-c": {"description": "Keyboard icon C", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f124"}}, "augment-kb-backspace": {"description": "Keyboard icon Backspace", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f125"}}, "augment-kb-b": {"description": "Keyboard icon B", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f126"}}, "augment-kb-alt": {"description": "Keyboard icon Alt", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f127"}}, "augment-kb-a": {"description": "Keyboard icon A", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f128"}}}, "keybindings": [{"command": "vscode-augment.insertCompletion", "when": "editorHasCompletionItemProvider && editorTextFocus && !editorR<PERSON>only", "key": "ctrl-f11", "mac": "ctrl-f11"}, {"command": "vscode-augment.focusAugmentPanel", "key": "ctrl-alt-i", "mac": "cmd-ctrl-i"}, {"command": "vscode-augment.focusAugmentPanel", "key": "ctrl-l", "mac": "cmd-l"}, {"command": "vscode-augment.internal-dv.o", "when": "vscode-augment.enableDebugFeatures", "key": "ctrl-alt-o", "mac": "cmd-ctrl-o"}, {"command": "vscode-augment.internal-dv.i", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && !editorReadonly && !terminalFocus", "key": "ctrl-i", "mac": "cmd-i"}, {"command": "vscode-augment.internal-dv.aac", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "ctrl-enter", "mac": "cmd-enter"}, {"command": "vscode-augment.internal-dv.afc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "enter", "mac": "enter"}, {"command": "vscode-augment.internal-dv.rfc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "backspace"}, {"command": "vscode-augment.internal-dv.fpc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "up"}, {"command": "vscode-augment.internal-dv.fnc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "down"}, {"command": "vscode-augment.internal-dv.c", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "escape"}, {"command": "vscode-augment.showAugmentCommands", "key": "ctrl-shift-a", "mac": "cmd-shift-a"}, {"command": "vscode-augment.toggleAutomaticCompletionSetting", "key": "ctrl-alt-a", "mac": "cmd-alt-a"}, {"command": "vscode-augment.showRemoteAgentsPanel", "when": "vscode-augment.featureFlags.enableRemoteAgents", "key": "ctrl-shift-r", "mac": "cmd-shift-r", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && !editorTextFocus && (activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-y", "mac": "cmd-shift-z", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-enter", "mac": "cmd-enter", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "enter", "mac": "enter", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "tab", "mac": "tab", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll && augment-next-edit.active", "key": "ctrl-alt-enter", "mac": "cmd-alt-enter", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-backspace", "mac": "cmd-backspace", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "backspace", "mac": "backspace", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll && augment-next-edit.active", "key": "ctrl-alt-backspace", "mac": "cmd-alt-backspace", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.dismiss", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canDismiss && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel') && !inlineSuggestionVisible && !editorHasSelection && (!vim.active || vim.mode == 'Normal')", "key": "escape", "mac": "escape", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.next", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNextSmart", "key": "ctrl-;", "mac": "cmd-;", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.next-forward", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext", "key": "ctrl-shift-'", "mac": "cmd-shift-'", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.previous", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious", "key": "ctrl-shift-;", "mac": "cmd-shift-;", "args": "keybinding"}, {"command": "vscode-augment.next-edit.force", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-alt-;", "mac": "cmd-ctrl-;", "args": "keybinding"}, {"command": "vscode-augment.next-edit.open-panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "key": "ctrl-'", "mac": "cmd-'", "args": "keybinding"}, {"command": "_vscode-augment.next-edit.undo-accept-suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canUndoAcceptSuggestion && !editorTextFocus && (activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-z", "mac": "cmd-z", "args": "keybinding"}], "submenus": [{"id": "vscode-augment.context-submenu", "label": "Send to Augment"}, {"id": "vscode-augment.viewTitleMenuEntryPoint", "label": "Augment Options", "icon": "$(menu)"}, {"id": "vscode-augment.next-edit.editor-action-submenu", "label": "Next Edit", "icon": {"light": "media/next-edit/nextedit-available-light.svg", "dark": "media/next-edit/nextedit-available-dark.svg"}}, {"id": "vscode-augment.next-edit.editor-action-submenu.disabled", "label": "Next Edit", "icon": {"light": "media/next-edit/nextedit-unavailable-light.svg", "dark": "media/next-edit/nextedit-unavailable-dark.svg"}}, {"id": "vscode-augment.next-edit.editor-action-submenu.loading", "label": "Next Edit", "icon": {"light": "media/next-edit/nextedit-loading-light.svg", "dark": "media/next-edit/nextedit-loading-dark.svg"}}, {"id": "vscode-augment.next-edit.panel-submenu", "label": "Next Edit Menu", "icon": "$(ellipsis)"}], "menus": {"view/title": [{"submenu": "vscode-augment.viewTitleMenuEntryPoint", "when": "view == augment-chat && vscode-augment.mainPanel.app == 'chat'", "group": "navigation@0"}, {"command": "vscode-augment.next-edit.update", "when": "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && vscode-augment.nextEdit.global.canUpdate && !vscode-augment.nextEdit.global.updateCached", "group": "navigation@1"}, {"command": "_vscode-augment.next-edit.update.loading", "when": "view == augment-next-edit && vscode-augment.nextEdit.global.updating", "group": "navigation@1"}, {"command": "_vscode-augment.next-edit.update.disabled-no-changes", "when": "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && !vscode-augment.nextEdit.global.canUpdate", "group": "navigation@1"}, {"command": "_vscode-augment.next-edit.update.disabled-cached", "when": "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && vscode-augment.nextEdit.global.canUpdate && vscode-augment.nextEdit.global.updateCached", "group": "navigation@1"}, {"submenu": "vscode-augment.next-edit.panel-submenu", "when": "view == augment-next-edit", "group": "navigation@2"}], "vscode-augment.viewTitleMenuEntryPoint": [{"command": "vscode-augment.showSettingsPanel", "group": "menu@1"}, {"command": "vscode-augment.showDocs", "group": "menu@2"}, {"command": "vscode-augment.showAccountPage", "group": "menu@3"}, {"command": "vscode-augment.signOut", "group": "menu@4"}], "editor/context": [{"submenu": "vscode-augment.context-submenu", "group": "0_augment"}, {"command": "vscode-augment.next-edit.force", "group": "1_modification", "when": "vscode-augment.enableNextEdit"}], "vscode-augment.context-submenu": [{"command": "vscode-augment.focusAugmentPanel", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.explain", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.test", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.fix", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.document", "when": "editorHasSelection"}], "editor/lineNumber/context": [{"command": "_vscode-augment.next-edit.background.open", "when": "vscode-augment.enableNextEdit && editorLineNumber in vscode-augment.nextEdit.linesWithGutterIconActions && resourcePath in vscode-augment.nextEdit.filesWithGutterIconActions", "group": "navigation@0"}], "commandPalette": [{"command": "vscode-augment.internal-dv.o", "when": "vscode-augment.enableDebugFeatures"}, {"command": "vscode-augment.insertCompletion", "when": "!editor<PERSON><PERSON><PERSON><PERSON>"}, {"command": "vscode-augment.toggleAutomaticCompletionSetting"}, {"command": "vscode-augment.signIn", "when": "vscode-augment.useOAuth && !vscode-augment.isLoggedIn"}, {"command": "vscode-augment.signOut", "when": "vscode-augment.useOAuth && vscode-augment.isLoggedIn"}, {"command": "vscode-augment.internal-dv.i", "when": "(vscode-augment.internal-new-instructions.enabled || vscode-augment.enableDebugFeatures) && !editorReadonly && !terminalFocus"}, {"command": "vscode-augment.chat.slash.fix"}, {"command": "vscode-augment.startNewChat"}, {"command": "vscode-augment.focusAugmentPanel", "when": "vscode-augment.enableDebugFeatures || vscode-augment.isLoggedIn"}, {"command": "_vscode-augment.showSidebarChat", "when": "false"}, {"command": "_vscode-augment.showSidebarWorkspaceContext", "when": "false"}, {"command": "vscode-augment.clear-recent-editing-history", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.generateCommitMessage", "when": "false"}, {"command": "vscode-augment.showSettingsPanel"}, {"command": "vscode-augment.manageAccountCommunity", "when": "augment.userTier == 'community'"}, {"command": "vscode-augment.manageAccountProfessional", "when": "augment.userTier == 'professional'"}, {"command": "vscode-augment.manageAccountEnterprise", "when": "augment.userTier == 'enterprise'"}, {"command": "vscode-augment.showRemoteAgentsPanel", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"command": "vscode-augment.openSshConfig", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"command": "vscode-augment.next-edit.open-panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}, {"command": "vscode-augment.next-edit.force", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll"}, {"command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll"}, {"command": "vscode-augment.next-edit.background.dismiss", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.next", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.next-forward", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.previous", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.toggle-bg", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.toggle-all-highlights", "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.background.accept-code-action", "when": "false"}, {"command": "vscode-augment.next-edit.toggle-panel-horizontal-split", "when": "view == augment-next-edit"}, {"command": "vscode-augment.next-edit.update", "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.loading", "when": "false"}, {"command": "_vscode-augment.next-edit.update.disabled-no-changes", "when": "false"}, {"command": "_vscode-augment.next-edit.update.disabled-cached", "when": "false"}, {"command": "vscode-augment.next-edit.learn-more", "when": "view == augment-next-edit"}, {"command": "_vscode-augment.next-edit.background.open", "when": "false"}, {"command": "_vscode-augment.next-edit.background.next-forward.disabled", "when": "false"}, {"command": "_vscode-augment.next-edit.background.previous.disabled", "when": "false"}], "scm/title": [{"command": "vscode-augment.generateCommitMessage", "args": ["${resourceUri}"], "group": "navigation", "when": "vscode-augment.enableGenerateCommitMessage && gitOpenRepositoryCount != 0"}], "editor/title": [{"submenu": "vscode-augment.next-edit.editor-action-submenu", "group": "navigation@43", "when": "vscode-augment.enableNextEdit && !terminalEditorActive && vscode-augment.nextEdit.canNext && !vscode-augment.nextEdit.loading"}, {"submenu": "vscode-augment.next-edit.editor-action-submenu.disabled", "group": "navigation@43", "when": "vscode-augment.enableNextEdit && !terminalEditorActive && !vscode-augment.nextEdit.canNext && !vscode-augment.nextEdit.loading"}, {"submenu": "vscode-augment.next-edit.editor-action-submenu.loading", "group": "navigation@43", "when": "vscode-augment.enableNextEdit && !terminalEditorActive && vscode-augment.nextEdit.loading"}], "vscode-augment.next-edit.panel-submenu": [{"command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.nextEdit.canAcceptAll", "title": "Accept All", "group": "2_more@1"}, {"command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.nextEdit.canRejectAll", "title": "Reject All", "group": "2_more@2"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_more@3"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_more@4"}], "vscode-augment.next-edit.editor-action-submenu": [{"command": "vscode-augment.next-edit.background.previous", "title": "Go to Previous Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@1"}, {"command": "vscode-augment.next-edit.background.next-forward", "title": "Go to Next Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@2"}, {"command": "vscode-augment.next-edit.open-panel", "title": "Open Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "group": "2_augment@3"}, {"command": "vscode-augment.next-edit.force", "group": "2_augment@4", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || augment-next-edit.active)"}, {"command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_augment@6", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_augment@7", "when": "vscode-augment.enableNextEdit"}], "vscode-augment.next-edit.editor-action-submenu.disabled": [{"command": "vscode-augment.next-edit.background.previous", "title": "Go to Previous Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@1"}, {"command": "vscode-augment.next-edit.background.next-forward", "title": "Go to Next Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@2"}, {"command": "vscode-augment.next-edit.open-panel", "title": "Open Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "group": "2_augment@3"}, {"command": "vscode-augment.next-edit.force", "group": "2_augment@4", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || augment-next-edit.active)"}, {"command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_augment@6", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_augment@7", "when": "vscode-augment.enableNextEdit"}], "vscode-augment.next-edit.editor-action-submenu.loading": [{"command": "vscode-augment.next-edit.background.previous", "title": "Go to Previous Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@1"}, {"command": "vscode-augment.next-edit.background.next-forward", "title": "Go to Next Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@2"}, {"command": "vscode-augment.next-edit.open-panel", "title": "Open Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "group": "2_augment@3"}, {"command": "vscode-augment.next-edit.force", "group": "2_augment@4", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || augment-next-edit.active)"}, {"command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_augment@6", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_augment@7", "when": "vscode-augment.enableNextEdit"}]}, "viewsContainers": {"activitybar": [{"icon": "media/activitybar.svg", "id": "augment-chat", "title": "Augment"}], "panel": [{"icon": "media/activitybar.svg", "id": "augment-panel", "title": "Augment Next Edit"}]}, "views": {"augment-chat": [{"id": "augment-chat", "name": "Augment", "type": "webview"}], "augment-panel": [{"id": "augment-next-edit", "name": "Augment Next Edit", "type": "webview", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}]}}, "scripts": {"build": "npm run vscode:build-dev && npm run common-webviews:build", "build:prod": "npm run vscode:esbuild-prod && npm run common-webviews:build", "lint": "npm run vscode:lint", "lint:fix": "npm run vscode:lint:fix", "test": "npm run vscode:test && npm run test:e2e", "test:fix": "npm run lint:fix && npm run test", "test:debug": "LOG_LEVEL=verbose npm run test", "test:e2e": "wdio run ./wdio.conf.ts", "watch": "concurrently 'npm:*:watch'", "package-extension": "STABLE_VSCODE_RELEASE_VERSION=$(head -n1 version)-$(git rev-parse --abbrev-ref HEAD).$(git log --format='%ct.%h' -n1) bazel build //clients/vscode:package-extension-prerelease --config=local_output --compilation_mode=opt --stamp && cp ../../bazel-bin/clients/vscode/vscode-augment-prerelease.vsix .", "package-extension-pnpm": "rm -rf out ; rm -rf webviews/dist ; npm run build:prod ; npm run embed-version-info && npm run package-for-release && cp package.json.bak package.json && rm package.json.bak", "marketplace-data": "vsce show --json Augment.vscode-augment", "embed-version-info": "./embed-version-info.py", "vsce-package": "vsce package --no-dependencies --allow-star-activation --skip-license --out=\"${EXTENSION_FILENAME:-out/vscode-augment.vsix}\"", "package-for-release": "[ \"$RELEASE_CHANNEL\" = \"prerelease\" ] && npm run vsce-package -- --pre-release || npm run vsce-package", "vscode:build-dev": "pnpm run vscode:esbuild-sourcemaps", "vscode:watch": "pnpm run vscode:esbuild-sourcemaps --watch", "vscode:esbuild-prod": "npm run vscode:esbuild-base -- --minify", "vscode:esbuild-sourcemaps": "npm run vscode:esbuild-base -- --sourcemap", "vscode:esbuild-base": "esbuild ./src/extension.ts --bundle --outfile=out/extension.js --external:vscode --format=cjs --platform=node", "vscode:extension:dev:hmr": "pnpm run -r dev:vite-hmr-vscode", "vscode:extension:dev:watch": ". ./.augment-hmr-env && pnpm exec ibazel run //clients/vscode:build_dev_to_workspace_hmr --config='local_output' --compilation_mode=dbg --action_env=AUGMENT_HMR=${AUGMENT_HMR} --action_env=AUGMENT_JS_ENV=${AUGMENT_JS_ENV}", "vscode:extension:dev:watch-no-hmr": "AUGMENT_JS_ENV=development pnpm exec ibazel run //clients/vscode:build_dev_to_workspace --config='local_output' --compilation_mode=dbg --action_env=AUGMENT_JS_ENV=development", "vscode:hmr:write:port": "node scripts/generate-augment-hmr-env.js", "vscode:lint": "npm run vscode:eslint && npm run vscode:prettier", "vscode:lint:fix": "npm run vscode:eslint:fix && npm run vscode:prettier:fix", "vscode:eslint": "git ls-files -- . | xargs pre-commit run eslint --files", "vscode:eslint:fix": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "vscode:jest": "jest --config ./jest.config.js", "vscode:prettier": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files", "vscode:prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --files", "vscode:test": "npm run build:prod && npm run vscode:lint && npm run vscode:jest", "common-webviews:build": "cd ../common/webviews && npm run build:vscode", "contributes-gen": "scripts/contributes/cli.ts"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@jest/globals": "^29.7.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/diff": "^7.0.1", "@types/glob": "^7.2.0", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@types/lodash.memoize": "^4.1.9", "@types/lodash.throttle": "^4.1.9", "@types/node": "18.15.0", "@types/node-forge": "^1.3.11", "@types/semver": "^7.5.8", "@types/uuid": "^9.0.8", "@types/vscode": "^1.82.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vscode/test-electron": "^2.3.9", "@wdio/cli": "^9.3.0", "@wdio/globals": "^8.27.0", "@wdio/local-runner": "^8.27.0", "@wdio/mocha-framework": "^8.27.0", "@wdio/spec-reporter": "^8.27.0", "@wdio/types": "^8.27.0", "babel-jest": "^29.7.0", "concurrently": "^8.2.2", "esbuild": "^0.14.54", "eslint": "^8.57.0", "eslint-plugin-jest": "^27.6.3", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-unused-imports": "^3.2.0", "fetch-mock": "^9.11.0", "fetch-mock-jest": "^1.5.1", "glob": "^8.1.0", "isomorphic-fetch": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-mock-vscode": "^3.0.4", "node-fetch": "^2.7.0", "nodemon": "^3.0.3", "npm": "^9.9.2", "replace-in-file": "^6.3.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsx": "^4.17.0", "typescript": "^5.5.3", "wdio-vscode-service": "^6.1.2"}, "dependencies": {"@anthropic-ai/sdk": "^0.27.2", "@anthropic-ai/vertex-sdk": "^0.4.1", "@bufbuild/protobuf": "^2.3.0", "@connectrpc/connect": "^2.0.2", "@vscode/vsce": "2.29.0", "denque": "^2.1.0", "diff": "^7.0.0", "encoding": "^0.1.13", "fuse.js": "^7.0.0", "highlight.js": "^11.9.0", "ignore": "^5.3.0", "jest-junit": "^16.0.0", "json5": "^2.2.3", "lodash": "^4.17.21", "lodash.memoize": "^4.1.2", "lodash.throttle": "^4.1.1", "lru-cache": "^11.0.0", "mac-ca": "^3.1.0", "monaco-editor": "^0.52.2", "node-diff3": "^3.1.2", "p-limit": "^3.1.0", "prettier-plugin-svelte": "^3.2.3", "prosemirror-model": "^1.23.0", "semver": "^7.6.3", "shlex": "^2.1.2", "simple-git": "^3.27.0", "typescript-eslint": "7.12.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-transport": "^4.6.0", "zod": "^3.23.8"}}, "location": {"$mid": 1, "path": "/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "augment", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1749703403593, "pinned": false, "source": "gallery", "id": "fc0e137d-e132-47ed-9455-c4636fa5b897", "publisherId": "7814b14b-491a-4e83-83ac-9222fa835050", "publisherDisplayName": "augment", "targetPlatform": "undefined", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false, "size": 19270757}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "ms-dotnettools.vscode-dotnet-runtime", "uuid": "1aab81a1-b3d9-4aef-976b-577d5d90fe3f"}, "manifest": {"name": "vscode-dotnet-runtime", "repository": {"type": "git", "url": "https://github.com/dotnet/vscode-dotnet-runtime.git"}, "bugs": {"url": "https://github.com/dotnet/vscode-dotnet-runtime/issues"}, "license": "MIT", "author": "Microsoft Corporation", "displayName": ".NET Install Tool", "description": "This extension installs and manages different versions of the .NET SDK and Runtime.", "connectionString": "InstrumentationKey=02dc18e0-7494-43b2-b2a3-18ada5fcb522;IngestionEndpoint=https://westus2-0.in.applicationinsights.azure.com/;LiveEndpoint=https://westus2.livediagnostics.monitor.azure.com/;ApplicationId=e8e56970-a18a-4101-b7d1-1c5dd7c29eeb", "icon": "images/dotnetIcon.png", "version": "2.3.6", "publisher": "ms-dot<PERSON><PERSON>s", "engines": {"vscode": "^1.99.0"}, "categories": ["Other"], "keywords": [".NET", ".NET Core", "dotnet", "Extension Authoring", "runtime"], "capabilities": {"untrustedWorkspaces": {"supported": true}, "virtualWorkspaces": true}, "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "types": "./dist/extension.d.ts", "contributes": {"commands": [{"command": "dotnet.reportIssue", "title": "Report an issue with the .NET Install Tool.", "category": ".NET Install Tool"}, {"command": "dotnet.acquireGlobalSDKPublic", "title": "Install the .NET SDK System-Wide.", "category": ".NET Install Tool"}, {"command": "dotnet.uninstallPublic", "title": "Uninstall .NET.", "category": ".NET Install Tool"}, {"command": "dotnet.resetData", "title": "Reset Extension Data (Uninstalls All Installations, Which May Cause Other Extensions To Fail. Uninstall Dependent Extensions First)", "category": ".NET Install Tool", "enablement": "config.dotnetAcquisitionExtension.showResetDataCommand"}], "configuration": {"title": ".NET Install Tool", "properties": {"dotnetAcquisitionExtension.enableTelemetry": {"type": "boolean", "default": true, "description": "Enable Telemetry for the .NET Install Tool. Restart VS Code to apply changes."}, "dotnetAcquisitionExtension.enablePreviewFeatures": {"type": "boolean", "default": false, "description": "Enable Preview Features for the Extension. Restart VS Code to apply changes."}, "dotnetAcquisitionExtension.installTimeoutValue": {"type": "number", "default": 600, "description": "Timeout for installing .NET in seconds."}, "dotnetAcquisitionExtension.existingDotnetPath": {"type": "array", "markdownDescription": "The path to an existing .NET host executable for an extension's code to run under, not for your project to run under.\nRestart VS Code to apply changes.\n\n⚠️ This is NOT the .NET Runtime that your project will use to run. Extensions such as `C#`, `C# DevKit`, and more have components written in .NET. This .NET PATH is the `dotnet.exe` that these extensions will use to run their code, not your code.\n\nUsing a path value in which .NET does not meet the requirements of a specific extension will cause that extension to fail.\n\n🚀 The version of .NET that is used for your project is determined by the .NET host, or dotnet.exe. The .NET host picks a runtime based on your project. To use a specific version of .NET for your project, install the .NET SDK using the `.NET Install Tool - Install SDK System-Wide` command, install .NET manually using [our instructions](https://dotnet.microsoft.com/download), or edit your PATH environment variable to point to a `dotnet.exe` that has an `/sdk/` folder with only one SDK.", "description": "The path to an existing .NET host executable for an extension's code to run under, not for your project to run under.\nRestart VS Code to apply changes.\n\n⚠️ This is NOT the .NET Runtime that your project will use to run. Extensions such as 'C#', 'C# DevKit', and more have components written in .NET. This .NET PATH is the 'dotnet.exe' that these extensions will use to run their code, not your code.\n\nUsing a path value in which .NET does not meet the requirements of a specific extension will cause that extension to fail.\n\n🚀 The version of .NET that is used for your project is determined by the .NET host, or dotnet.exe. The .NET host picks a runtime based on your project. To use a specific version of .NET for your project, install the .NET SDK using the '.NET Install Tool - Install SDK System-Wide' command, use the instructions at https://dotnet.microsoft.com/download to manually install the .NET SDK, or edit your PATH environment variable to point to a 'dotnet.exe' that has an '/sdk/' folder with only one SDK.", "examples": ["C:\\Program Files\\dotnet\\dotnet.exe", "/usr/local/share/dotnet/dotnet", "/usr/lib/dotnet/dotnet"]}, "dotnetAcquisitionExtension.sharedExistingDotnetPath": {"type": "string", "description": "The path of the preexisting .NET Runtime you'd like to use for ALL extensions. Restart VS Code to apply changes.", "examples": ["C:\\Program Files\\dotnet\\dotnet.exe", "/usr/local/share/dotnet/dotnet", "/usr/lib/dotnet/dotnet"]}, "dotnetAcquisitionExtension.proxyUrl": {"type": "string", "description": "URL to a proxy if you use one, such as: https://proxy:port"}, "dotnetAcquisitionExtension.allowInvalidPaths": {"type": "boolean", "description": "If you'd like to continue using a .NET path that is not meant to be used for an extension and may cause instability (please read above about the existingDotnetPath setting) then set this to true and restart."}, "dotnetAcquisitionExtension.cacheTimeToLiveMultiplier": {"type": "number", "default": 1, "description": "To improve performance, the results of checking .NET Installations may be cached. If you're facing issues with an install not being detected, try setting this to 0.5, or 0; or increasing the number to improve performance. Restart to change."}, "dotnetAcquisitionExtension.showResetDataCommand": {"type": "boolean", "default": false, "description": "Show the command to reset extension data in the command palette. Restart VS Code and remove dependent extensions first."}}}}, "scripts": {"vscode:prepublish": "npm run compile-all && npm install && webpack --mode production && dotnet build ../msbuild/signJs --property jsOutputPath=..\\..\\vscode-dotnet-runtime-extension\\dist -bl -v:normal", "compile": "npm run clean && tsc -p ./", "watch": "npm run compile && tsc -watch -p ./", "test": "npm run compile --silent && node ./dist/test/functional/runTest.js", "clean": "<PERSON><PERSON><PERSON> dist", "compile-all": "cd ../vscode-dotnet-runtime-library && npm install && npm run compile && cd ../vscode-dotnet-runtime-extension && npm install && npm run compile", "lint": "eslint -c .eslintrc.js --ext=.ts vscode-dotnet-runtime-library/src/**/*.ts vscode-dotnet-runtime-extension/src/**/*.ts --ignore-pattern \"**/test/\" --fix", "webpack": "webpack --mode development"}, "dependencies": {"@types/chai-as-promised": "^7.1.8", "@vscode/test-electron": "^2.3.9", "axios": "^1.7.4", "axios-cache-interceptor": "^1.0.1", "axios-retry": "^3.4.0", "chai": "4.3.4", "glob": "^7.2.0", "https-proxy-agent": "^7.0.2", "mocha": "^9.1.3", "open": "^8.4.0", "rimraf": "3.0.2", "shelljs": "^0.8.5", "ts-loader": "^9.5.1", "typescript": "^5.5.4", "vscode-dotnet-runtime-library": "file:../vscode-dotnet-runtime-library", "webpack-permissions-plugin": "^1.0.9"}, "devDependencies": {"@types/chai": "^4.3.5", "@types/mocha": "^9.0.0", "@types/node": "^20.0.0", "@types/rimraf": "3.0.2", "@types/source-map-support": "^0.5.10", "@types/vscode": "1.74.0", "copy-webpack-plugin": "^9.0.1", "webpack": "^5.88.2", "webpack-cli": "^4.9.1"}}, "location": {"$mid": 1, "path": "/Users/<USER>/.windsurf/extensions/ms-dotnettools.vscode-dotnet-runtime-2.3.6-universal", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "universal", "publisherDisplayName": "ms-dot<PERSON><PERSON>s", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1749826851444, "pinned": false, "source": "gallery", "id": "1aab81a1-b3d9-4aef-976b-577d5d90fe3f", "publisherId": "d05e23de-3974-4ff0-8d47-23ee77830092", "publisherDisplayName": "ms-dot<PERSON><PERSON>s", "targetPlatform": "universal", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false, "size": 5469178}, "isValid": true, "validations": [], "preRelease": false}]}