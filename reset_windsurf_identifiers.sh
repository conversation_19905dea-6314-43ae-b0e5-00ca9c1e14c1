#!/bin/bash

# Windsurf标识符重置脚本
# 此脚本会修改所有用于记录个人信息的字段，使Windsurf看起来像全新安装

# 检测操作系统并设置应用程序路径
detect_windsurf_path() {
    local os_type=$(uname -s)
    case "$os_type" in
        "Darwin")
            # macOS
            APP_SUPPORT_DIR="$HOME/Library/Application Support/Windsurf"
            ;;
        "Linux")
            # Linux
            APP_SUPPORT_DIR="$HOME/.config/Windsurf"
            ;;
        "MINGW"*|"CYGWIN"*|"MSYS"*)
            # Windows (Git Bash/WSL)
            APP_SUPPORT_DIR="$APPDATA/Windsurf"
            ;;
        *)
            echo "❌ 不支持的操作系统: $os_type"
            exit 1
            ;;
    esac
}

# 处理命令行参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Windsurf标识符重置脚本"
    echo ""
    echo "用法:"
    echo "  $0                    # 自动检测Windsurf路径"
    echo "  $0 [路径]             # 手动指定Windsurf应用目录"
    echo "  $0 --help            # 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 \"/custom/path/to/Windsurf\""
    exit 0
fi

# 动态获取当前用户的应用程序路径
CURRENT_USER=$(whoami)

# 如果提供了自定义路径参数，使用它；否则自动检测
if [ -n "$1" ]; then
    APP_SUPPORT_DIR="$1"
    echo "📁 使用自定义路径: $APP_SUPPORT_DIR"
else
    detect_windsurf_path
    echo "📁 自动检测到路径: $APP_SUPPORT_DIR"
fi

# 检查路径是否存在
if [ ! -d "$APP_SUPPORT_DIR" ]; then
    echo "❌ 错误：未找到Windsurf应用目录: $APP_SUPPORT_DIR"
    echo "请确认Windsurf已正确安装，或手动指定路径。"
    echo ""
    echo "💡 常见路径："
    echo "   macOS: ~/Library/Application Support/Windsurf"
    echo "   Linux: ~/.config/Windsurf"
    echo "   Windows: %APPDATA%/Windsurf"
    echo ""
    echo "💡 使用方法："
    echo "   $0 \"/path/to/your/Windsurf\""
    exit 1
fi

echo "🔧 正在重置Windsurf标识符..."
echo "📁 应用目录: $APP_SUPPORT_DIR"
echo "👤 当前用户: $CURRENT_USER"
echo ""

# 安全确认
read -p "⚠️  此操作将清除所有Windsurf标识符和用户数据，是否继续？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 0
fi

# 1. 修改核心机器标识符
# 1.1 修改machineid文件
if [ -f "$APP_SUPPORT_DIR/machineid" ]; then
    NEW_MACHINE_ID=$(uuidgen)
    echo "$NEW_MACHINE_ID" > "$APP_SUPPORT_DIR/machineid"
    echo "✅ 已重置机器ID: $NEW_MACHINE_ID"
fi

# 1.2 修改遥测机器标识符
STORAGE_JSON="$APP_SUPPORT_DIR/User/globalStorage/storage.json"
if [ -f "$STORAGE_JSON" ]; then
    # 生成新的遥测ID
    NEW_TELEMETRY_MACHINE_ID=$(openssl rand -hex 32)
    NEW_DEV_DEVICE_ID=$(uuidgen)
    NEW_SESSION_ID=$(uuidgen)
    NEW_INSTANCE_ID=$(uuidgen)
    NEW_SQM_USER_ID=$(openssl rand -hex 16)
    NEW_INSTALLATION_ID=$(uuidgen)
    NEW_USER_GLOBAL_ID=$(uuidgen)
    NEW_SYNC_MACHINE_ID=$(openssl rand -hex 32)
    CURRENT_DATE=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")

    # 使用临时文件进行修改
    TMP_FILE=$(mktemp)

    # 使用jq修改JSON文件中的标识符
    if command -v jq &> /dev/null; then
        jq --arg mid "$NEW_TELEMETRY_MACHINE_ID" \
           --arg did "$NEW_DEV_DEVICE_ID" \
           --arg sid "$NEW_SESSION_ID" \
           --arg iid "$NEW_INSTANCE_ID" \
           --arg sqm "$NEW_SQM_USER_ID" \
           --arg inst "$NEW_INSTALLATION_ID" \
           --arg ugid "$NEW_USER_GLOBAL_ID" \
           --arg sync "$NEW_SYNC_MACHINE_ID" \
           --arg date "$CURRENT_DATE" \
        '.["telemetry.machineId"]=$mid |
         .["telemetry.devDeviceId"]=$did |
         .["telemetry.sessionId"]=$sid |
         .["telemetry.instanceId"]=$iid |
         .["telemetry.sqmUserId"]=$sqm |
         .["telemetry.installationId"]=$inst |
         .["telemetry.firstSessionDate"]=$date |
         .["telemetry.lastSessionDate"]=$date |
         .["user.globalId"]=$ugid |
         .["installation.time"]=$date |
         .["sync.machineId"]=$sync |
         .["settingsSync.machineId"]=$sync |
         .["userDataSync.machineId"]=$sync |
         .["marketplace.machineId"]=$mid |
         .["gallery.machineId"]=$mid |
         .["remote.machineId"]=$mid |
         .["tunnels.machineId"]=$mid' \
        "$STORAGE_JSON" > "$TMP_FILE"
        mv "$TMP_FILE" "$STORAGE_JSON"
        echo "✅ 已重置所有遥测和同步标识符"
    else
        echo "⚠️ 未找到jq工具，无法修改JSON文件。请安装jq: brew install jq"
    fi
fi

# 2. 清理工作区和会话标识符
echo "正在清理工作区和会话标识符..."
rm -rf "$APP_SUPPORT_DIR/User/workspaceStorage"/*
mkdir -p "$APP_SUPPORT_DIR/User/workspaceStorage"
echo "✅ 已清理工作区存储"

# 3. 清理备份文件夹
echo "正在清理备份文件夹..."
rm -rf "$APP_SUPPORT_DIR/Backups"/*
mkdir -p "$APP_SUPPORT_DIR/Backups"
echo "✅ 已清理备份文件夹"

# 4. 清理缓存和存储标识符
echo "正在清理缓存文件..."
rm -f "$APP_SUPPORT_DIR/Cache"/*
rm -f "$APP_SUPPORT_DIR/CachedData"/*
rm -f "$APP_SUPPORT_DIR/CachedExtensions"/*
rm -f "$APP_SUPPORT_DIR/Code Cache"/*
echo "✅ 已清理缓存文件"

# 5. 清理扩展和插件标识符
echo "正在清理扩展标识符..."
rm -rf "$APP_SUPPORT_DIR/User/globalStorage/augment.vscode-augment"/*
echo "✅ 已清理扩展标识符"

# 6. 清理会话和认证标识符
echo "正在清理会话标识符..."
rm -rf "$APP_SUPPORT_DIR/User/History"/*
echo "✅ 已清理历史记录"

# 7. 清理日志文件
echo "正在清理日志文件..."
rm -rf "$APP_SUPPORT_DIR/logs"/*
mkdir -p "$APP_SUPPORT_DIR/logs"
echo "✅ 已清理日志文件"

# 8. 清理额外的标识符文件
echo "正在清理额外的标识符文件..."

# 清理遥测日志
rm -f "$APP_SUPPORT_DIR/User/globalStorage/telemetry.log"
rm -f "$APP_SUPPORT_DIR/User/globalStorage/telemetry-*.log"

# 清理会话存储
rm -rf "$APP_SUPPORT_DIR/User/sessionStorage"/*
mkdir -p "$APP_SUPPORT_DIR/User/sessionStorage"

# 清理同步状态
rm -rf "$APP_SUPPORT_DIR/User/sync"/*
mkdir -p "$APP_SUPPORT_DIR/User/sync"

# 清理远程连接标识符
rm -rf "$APP_SUPPORT_DIR/User/globalStorage/ms-vscode-remote.*"/*

# 清理Live Share标识符
rm -rf "$APP_SUPPORT_DIR/User/globalStorage/ms-vsliveshare.*"/*

# 清理GitHub相关标识符
rm -rf "$APP_SUPPORT_DIR/User/globalStorage/github.*"/*

# 清理扩展主机标识符
rm -f "$APP_SUPPORT_DIR/User/globalStorage/extensionHost.json"

echo "✅ 已清理额外的标识符文件"

# 8.5. 清理用户级扩展目录
echo "正在清理用户级扩展目录..."
USER_WINDSURF_DIR="$HOME/.windsurf"
if [ -d "$USER_WINDSURF_DIR" ]; then
    # 清理扩展目录
    rm -rf "$USER_WINDSURF_DIR/extensions"/*
    mkdir -p "$USER_WINDSURF_DIR/extensions"

    # 清理扩展日志
    rm -rf "$USER_WINDSURF_DIR/logs"/*
    mkdir -p "$USER_WINDSURF_DIR/logs"

    # 清理扩展缓存
    rm -rf "$USER_WINDSURF_DIR/CachedExtensions"/*
    mkdir -p "$USER_WINDSURF_DIR/CachedExtensions"

    # 清理扩展全局存储
    rm -rf "$USER_WINDSURF_DIR/globalStorage"/*
    mkdir -p "$USER_WINDSURF_DIR/globalStorage"

    echo "✅ 已清理用户级扩展目录"
else
    echo "ℹ️  未找到用户级扩展目录: $USER_WINDSURF_DIR"
fi

# 8.5.1. 清理Codeium用户目录
echo "正在清理Codeium用户目录..."
CODEIUM_DIR="$HOME/.codeium"
if [ -d "$CODEIUM_DIR" ]; then
    # 备份Codeium目录（因为可能包含重要配置）
    CODEIUM_BACKUP="$CODEIUM_DIR.backup.$(date +%Y%m%d_%H%M%S)"
    cp -r "$CODEIUM_DIR" "$CODEIUM_BACKUP"
    echo "ℹ️  已备份Codeium目录到: $CODEIUM_BACKUP"

    # 清理认证和会话数据
    rm -f "$CODEIUM_DIR"/*.json 2>/dev/null || true
    rm -f "$CODEIUM_DIR"/*.token 2>/dev/null || true
    rm -f "$CODEIUM_DIR"/*.key 2>/dev/null || true
    rm -f "$CODEIUM_DIR"/*.session 2>/dev/null || true
    rm -f "$CODEIUM_DIR"/*.auth 2>/dev/null || true

    # 清理缓存和日志
    rm -rf "$CODEIUM_DIR/cache"/* 2>/dev/null || true
    rm -rf "$CODEIUM_DIR/logs"/* 2>/dev/null || true
    rm -rf "$CODEIUM_DIR/tmp"/* 2>/dev/null || true

    # 清理配置文件中的标识符
    if [ -f "$CODEIUM_DIR/config.json" ] && command -v jq &> /dev/null; then
        TMP_CONFIG=$(mktemp)
        jq 'del(.user_id) | del(.session_id) | del(.device_id) | del(.machine_id) | del(.auth_token)' \
        "$CODEIUM_DIR/config.json" > "$TMP_CONFIG" 2>/dev/null && mv "$TMP_CONFIG" "$CODEIUM_DIR/config.json" || rm -f "$TMP_CONFIG"
    fi

    echo "✅ 已清理Codeium用户目录"
else
    echo "ℹ️  未找到Codeium用户目录: $CODEIUM_DIR"
fi

# 8.6. 清理系统缓存目录
echo "正在清理系统缓存目录..."
CACHE_DIR="$HOME/Library/Caches/Windsurf"
if [ -d "$CACHE_DIR" ]; then
    rm -rf "$CACHE_DIR"/*
    echo "✅ 已清理系统缓存目录"
else
    echo "ℹ️  未找到系统缓存目录: $CACHE_DIR"
fi

# 8.7. 清理临时文件
echo "正在清理临时文件..."
# 清理/tmp目录下的windsurf临时文件
find /tmp -name "*windsurf*" -type f -delete 2>/dev/null || true
find /tmp -name "*Windsurf*" -type f -delete 2>/dev/null || true

# 清理macOS特定的临时文件
if [ "$(uname -s)" = "Darwin" ]; then
    find /var/folders -name "*windsurf*" -type f -delete 2>/dev/null || true
    find /var/folders -name "*Windsurf*" -type f -delete 2>/dev/null || true
fi
echo "✅ 已清理临时文件"

# 8.8. 清理Keychain中的Windsurf相关项目（仅macOS）
if [ "$(uname -s)" = "Darwin" ]; then
    echo "正在清理Keychain中的Windsurf相关项目..."
    # 查找并删除Windsurf相关的keychain项目
    security find-generic-password -s "Windsurf" 2>/dev/null && {
        security delete-generic-password -s "Windsurf" 2>/dev/null || true
        echo "✅ 已清理Keychain中的Windsurf项目"
    } || echo "ℹ️  未找到Keychain中的Windsurf项目"

    # 清理可能的VSCode相关项目（如果Windsurf基于VSCode）
    security find-generic-password -s "vscode" 2>/dev/null && {
        security delete-generic-password -s "vscode" 2>/dev/null || true
        echo "✅ 已清理Keychain中的VSCode项目"
    } || echo "ℹ️  未找到Keychain中的VSCode项目"
fi

# 9. 重置用户设置中的标识符相关配置
echo "正在重置用户设置..."
USER_SETTINGS="$APP_SUPPORT_DIR/User/settings.json"
if [ -f "$USER_SETTINGS" ]; then
    # 备份原始设置
    cp "$USER_SETTINGS" "$USER_SETTINGS.backup.$(date +%Y%m%d_%H%M%S)"

    # 使用jq移除可能包含标识符的设置
    if command -v jq &> /dev/null; then
        TMP_SETTINGS=$(mktemp)
        jq 'del(.["telemetry.machineId"]) |
            del(.["telemetry.sessionId"]) |
            del(.["sync.machineId"]) |
            del(.["remote.machineId"])' \
        "$USER_SETTINGS" > "$TMP_SETTINGS"
        mv "$TMP_SETTINGS" "$USER_SETTINGS"
        echo "✅ 已清理用户设置中的标识符"
    fi
fi

echo "✅ Windsurf标识符重置完成！请重新启动应用程序以应用更改。"
echo ""
echo "📋 完整重置摘要："
echo "   • 机器标识符: ✅"
echo "   • 遥测标识符: ✅"
echo "   • 会话标识符: ✅"
echo "   • 同步标识符: ✅"
echo "   • 扩展标识符: ✅"
echo "   • 工作区数据: ✅"
echo "   • 缓存文件: ✅"
echo "   • 日志文件: ✅"
echo "   • 用户级扩展: ✅"
echo "   • Codeium配置: ✅"
echo "   • 系统缓存: ✅"
echo "   • 临时文件: ✅"
if [ "$(uname -s)" = "Darwin" ]; then
echo "   • Keychain项目: ✅"
fi
echo ""
echo "📁 已处理的目录："
echo "   • $APP_SUPPORT_DIR"
echo "   • $HOME/.windsurf"
echo "   • $HOME/.codeium"
echo "   • $HOME/Library/Caches/Windsurf"
echo "   • /tmp/*windsurf*"
if [ "$(uname -s)" = "Darwin" ]; then
echo "   • macOS Keychain"
fi
echo ""
echo "⚠️  重要提醒："
echo "   • 已创建用户设置备份文件"
echo "   • 所有扩展需要重新安装"
echo "   • 所有个人配置需要重新设置"
echo "   • 建议重启系统以确保完全清理"